import React, { useState, useEffect, useCallback } from 'react';
import toast from 'react-hot-toast';
import { useAuth } from '../../context/AuthContext';
import shiftService from '../../services/shiftService';

// Helper functions for date presets
const getWeekStart = () => {
  const today = new Date();
  const day = today.getDay();
  const diff = today.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
  const monday = new Date(today.setDate(diff));
  return monday.toISOString().split('T')[0];
};

const getWeekEnd = () => {
  const today = new Date();
  const day = today.getDay();
  const diff = today.getDate() - day + (day === 0 ? 0 : 7); // Adjust when day is Sunday
  const sunday = new Date(today.setDate(diff));
  return sunday.toISOString().split('T')[0];
};

const getMonthStart = () => {
  const today = new Date();
  return new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0];
};

const getMonthEnd = () => {
  const today = new Date();
  return new Date(today.getFullYear(), today.getMonth() + 1, 0).toISOString().split('T')[0];
};

const getToday = () => {
  return new Date().toISOString().split('T')[0];
};

const SimplifiedShiftManagement = () => {
  useAuth();
  const [shifts, setShifts] = useState([]);
  const [trucks, setTrucks] = useState([]);
  const [drivers, setDrivers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingShift, setEditingShift] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [shiftToDelete, setShiftToDelete] = useState(null);
  const [pagination, setPagination] = useState({});

  // Enhanced filter state
  const [filters, setFilters] = useState({
    truck_ids: [],
    driver_ids: [],
    statuses: [],
    shift_types: [],
    date_from: getWeekStart(),
    date_to: getWeekEnd(),
    sort_by: 'shift_date',
    sort_order: 'DESC'
  });

  const loadShifts = useCallback(async () => {
    try {
      setLoading(true);

      // Build query parameters from filters
      const queryParams = {};

      if (filters.truck_ids.length > 0) {
        queryParams.truck_ids = filters.truck_ids.join(',');
      }

      if (filters.driver_ids.length > 0) {
        queryParams.driver_ids = filters.driver_ids.join(',');
      }

      if (filters.statuses.length > 0) {
        queryParams.statuses = filters.statuses.join(',');
      }

      if (filters.shift_types.length > 0) {
        queryParams.shift_types = filters.shift_types.join(',');
      }

      if (filters.date_from) {
        queryParams.date_from = filters.date_from;
      }

      if (filters.date_to) {
        queryParams.date_to = filters.date_to;
      }

      queryParams.sort_by = filters.sort_by;
      queryParams.sort_order = filters.sort_order;
      queryParams.limit = 100; // Show more results for better UX

      // eslint-disable-next-line no-console
      console.log('Loading shifts with filters:', queryParams);

      const response = await shiftService.getShifts(queryParams);
      setShifts(response.data || []);
      setPagination(response.pagination || {});
    } catch (error) {
      console.error('Error loading shifts:', error);
      toast.error('Failed to load shifts');
    } finally {
      setLoading(false);
    }
  }, [filters]);

  const loadTrucks = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/trucks', {
        headers: { 'Authorization': `Bearer ${localStorage.getItem('hauling_token')}` }
      });
      if (response.ok) {
        const data = await response.json();
        setTrucks(data.data || []);
      }
    } catch (error) {
      console.error('Error loading trucks:', error);
    }
  };

  const loadDrivers = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/drivers', {
        headers: { 'Authorization': `Bearer ${localStorage.getItem('hauling_token')}` }
      });
      if (response.ok) {
        const data = await response.json();
        setDrivers(data.data || []);
      }
    } catch (error) {
      console.error('Error loading drivers:', error);
    }
  };

  useEffect(() => {
    loadShifts();
    loadTrucks();
    loadDrivers();
  }, [loadShifts]);

  // Filter management functions
  const updateFilter = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const toggleMultiSelectFilter = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: prev[key].includes(value)
        ? prev[key].filter(item => item !== value)
        : [...prev[key], value]
    }));
  };

  const clearAllFilters = () => {
    setFilters({
      truck_ids: [],
      driver_ids: [],
      statuses: [],
      shift_types: [],
      date_from: getWeekStart(),
      date_to: getWeekEnd(),
      sort_by: 'shift_date',
      sort_order: 'DESC'
    });
  };

  const setDatePreset = (preset) => {
    const today = getToday();
    switch (preset) {
      case 'today':
        updateFilter('date_from', today);
        updateFilter('date_to', today);
        break;
      case 'week':
        updateFilter('date_from', getWeekStart());
        updateFilter('date_to', getWeekEnd());
        break;
      case 'month':
        updateFilter('date_from', getMonthStart());
        updateFilter('date_to', getMonthEnd());
        break;
      default:
        break;
    }
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.truck_ids.length > 0) count++;
    if (filters.driver_ids.length > 0) count++;
    if (filters.statuses.length > 0) count++;
    if (filters.shift_types.length > 0) count++;
    return count;
  };

  const getDaysInRange = () => {
    if (!filters.date_from || !filters.date_to) return 0;
    const start = new Date(filters.date_from);
    const end = new Date(filters.date_to);
    const diffTime = Math.abs(end - start);
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      scheduled: { bg: 'bg-blue-100', text: 'text-blue-800', label: 'Scheduled', icon: '📅' },
      active: { bg: 'bg-green-100', text: 'text-green-800', label: 'Active', icon: '🟢' },
      completed: { bg: 'bg-gray-100', text: 'text-gray-800', label: 'Completed', icon: '✅' },
      cancelled: { bg: 'bg-red-100', text: 'text-red-800', label: 'Cancelled', icon: '❌' }
    };

    const config = statusConfig[status] || statusConfig.scheduled;
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${config.bg} ${config.text} flex items-center gap-1`}>
        <span>{config.icon}</span>
        {config.label}
      </span>
    );
  };

  const handleEditShift = (shift) => {
    setEditingShift(shift);
    setShowCreateModal(true);
  };

  const handleDeleteShift = (shift) => {
    setShiftToDelete(shift);
    setShowDeleteModal(true);
  };

  const confirmDeleteShift = async () => {
    if (!shiftToDelete) return;

    try {
      await shiftService.deleteShift(shiftToDelete.id);
      toast.success('Shift deleted successfully!');
      setShowDeleteModal(false);
      setShiftToDelete(null);
      loadShifts();
    } catch (error) {
      console.error('Error deleting shift:', error);
      toast.error(error.message || 'Failed to delete shift');
    }
  };

  const handleStatusChange = async (shiftId, newStatus) => {
    try {
      await shiftService.updateShift(shiftId, { status: newStatus });
      toast.success(`Shift ${newStatus} successfully!`);
      loadShifts();
    } catch (error) {
      console.error('Error updating shift status:', error);
      toast.error(error.message || 'Failed to update shift status');
    }
  };

  return (
    <div className="min-h-screen bg-secondary-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-secondary-900">
                🔄 Enhanced Shift Management
              </h1>
              <p className="mt-2 text-secondary-600">
                Comprehensive shift scheduling with advanced filtering
              </p>
              {pagination.total_count !== undefined && (
                <p className="mt-1 text-sm text-secondary-500">
                  Showing {pagination.returned_count} of {pagination.total_count} shifts
                  {getActiveFilterCount() > 0 && ` (${getActiveFilterCount()} filters active)`}
                </p>
              )}
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setShowCreateModal(true)}
                className="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 transition-colors"
              >
                + Create Shift
              </button>
            </div>
          </div>
        </div>

        {/* Horizontal Filter Bar - Matching Assignment Management Design */}
        <div className="mb-6 bg-white rounded-lg shadow border border-secondary-200 p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4">

            {/* Date Range Filter */}
            <div className="lg:col-span-2">
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                Date Range ({getDaysInRange()} days)
              </label>
              <div className="space-y-2">
                <div className="flex space-x-1">
                  <input
                    type="date"
                    value={filters.date_from}
                    onChange={(e) => updateFilter('date_from', e.target.value)}
                    className="input text-sm flex-1"
                    placeholder="From"
                  />
                  <input
                    type="date"
                    value={filters.date_to}
                    onChange={(e) => updateFilter('date_to', e.target.value)}
                    className="input text-sm flex-1"
                    placeholder="To"
                  />
                </div>
                <div className="flex space-x-1">
                  <button
                    onClick={() => setDatePreset('today')}
                    className="px-2 py-1 text-xs bg-secondary-100 text-secondary-600 rounded hover:bg-secondary-200 transition-colors"
                  >
                    Today
                  </button>
                  <button
                    onClick={() => setDatePreset('week')}
                    className="px-2 py-1 text-xs bg-secondary-100 text-secondary-600 rounded hover:bg-secondary-200 transition-colors"
                  >
                    Week
                  </button>
                  <button
                    onClick={() => setDatePreset('month')}
                    className="px-2 py-1 text-xs bg-secondary-100 text-secondary-600 rounded hover:bg-secondary-200 transition-colors"
                  >
                    Month
                  </button>
                </div>
              </div>
            </div>

            {/* Truck Filter */}
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                Truck
              </label>
              <select
                value={filters.truck_ids.length === 1 ? filters.truck_ids[0] : ''}
                onChange={(e) => updateFilter('truck_ids', e.target.value ? [parseInt(e.target.value)] : [])}
                className="input text-sm w-full"
              >
                <option value="">All Trucks</option>
                {trucks.map(truck => (
                  <option key={truck.id} value={truck.id}>
                    {truck.truck_number}
                  </option>
                ))}
              </select>
            </div>

            {/* Driver Filter */}
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                Driver
              </label>
              <select
                value={filters.driver_ids.length === 1 ? filters.driver_ids[0] : ''}
                onChange={(e) => updateFilter('driver_ids', e.target.value ? [parseInt(e.target.value)] : [])}
                className="input text-sm w-full"
              >
                <option value="">All Drivers</option>
                {drivers.map(driver => (
                  <option key={driver.id} value={driver.id}>
                    {driver.full_name}
                  </option>
                ))}
              </select>
            </div>

            {/* Status Filter */}
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                Status ({filters.statuses.length} selected)
              </label>
              <div className="space-y-1 max-h-20 overflow-y-auto">
                {['scheduled', 'active', 'completed', 'cancelled'].map(status => (
                  <label key={status} className="flex items-center space-x-1 text-sm">
                    <input
                      type="checkbox"
                      checked={filters.statuses.includes(status)}
                      onChange={() => toggleMultiSelectFilter('statuses', status)}
                      className="rounded border-secondary-300 text-primary-600 focus:ring-primary-500"
                    />
                    <span className="capitalize text-xs">{status}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Shift Type Filter */}
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                Shift Type ({filters.shift_types.length} selected)
              </label>
              <div className="space-y-1">
                {['day', 'night'].map(type => (
                  <label key={type} className="flex items-center space-x-1 text-sm">
                    <input
                      type="checkbox"
                      checked={filters.shift_types.includes(type)}
                      onChange={() => toggleMultiSelectFilter('shift_types', type)}
                      className="rounded border-secondary-300 text-primary-600 focus:ring-primary-500"
                    />
                    <span className="text-xs">{type === 'day' ? '☀️ Day' : '🌙 Night'}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Clear Filters */}
            <div className="flex items-end">
              <button
                onClick={clearAllFilters}
                disabled={getActiveFilterCount() === 0}
                className="btn btn-secondary w-full disabled:opacity-50 disabled:cursor-not-allowed text-sm"
              >
                Clear Filters
                {getActiveFilterCount() > 0 && (
                  <span className="ml-1 bg-primary-100 text-primary-800 text-xs rounded-full px-2 py-0.5">
                    {getActiveFilterCount()}
                  </span>
                )}
              </button>
            </div>
          </div>

          {/* Filter Summary */}
          {getActiveFilterCount() > 0 && (
            <div className="mt-3 pt-3 border-t border-secondary-200">
              <div className="text-sm text-secondary-600">
                <span className="font-medium">Active Filters:</span>
                {filters.truck_ids.length > 0 && (
                  <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs bg-blue-100 text-blue-800">
                    {filters.truck_ids.length} truck{filters.truck_ids.length !== 1 ? 's' : ''}
                  </span>
                )}
                {filters.driver_ids.length > 0 && (
                  <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs bg-green-100 text-green-800">
                    {filters.driver_ids.length} driver{filters.driver_ids.length !== 1 ? 's' : ''}
                  </span>
                )}
                {filters.statuses.length > 0 && (
                  <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs bg-yellow-100 text-yellow-800">
                    {filters.statuses.length} status{filters.statuses.length !== 1 ? 'es' : ''}
                  </span>
                )}
                {filters.shift_types.length > 0 && (
                  <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs bg-purple-100 text-purple-800">
                    {filters.shift_types.length} type{filters.shift_types.length !== 1 ? 's' : ''}
                  </span>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Shifts Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-secondary-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium text-secondary-900">
                  Shifts {filters.date_from === filters.date_to
                    ? `for ${new Date(filters.date_from).toLocaleDateString()}`
                    : `from ${new Date(filters.date_from).toLocaleDateString()} to ${new Date(filters.date_to).toLocaleDateString()}`
                  }
                </h3>
                {loading && (
                  <p className="text-sm text-secondary-500 mt-1">Loading shifts...</p>
                )}
              </div>
              <div className="flex items-center space-x-2">
                <select
                  value={`${filters.sort_by}_${filters.sort_order}`}
                  onChange={(e) => {
                    const [sort_by, sort_order] = e.target.value.split('_');
                    updateFilter('sort_by', sort_by);
                    updateFilter('sort_order', sort_order);
                  }}
                  className="text-sm border border-secondary-300 rounded-md px-2 py-1"
                >
                  <option value="shift_date_DESC">Date (Newest)</option>
                  <option value="shift_date_ASC">Date (Oldest)</option>
                  <option value="truck_number_ASC">Truck (A-Z)</option>
                  <option value="truck_number_DESC">Truck (Z-A)</option>
                  <option value="driver_name_ASC">Driver (A-Z)</option>
                  <option value="driver_name_DESC">Driver (Z-A)</option>
                  <option value="status_ASC">Status (A-Z)</option>
                  <option value="status_DESC">Status (Z-A)</option>
                </select>
              </div>
            </div>
          </div>
          
          {loading ? (
            <div className="p-6 text-center">
              <div className="inline-flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600 mr-2"></div>
                Loading shifts...
              </div>
            </div>
          ) : shifts.length === 0 ? (
            <div className="p-6 text-center text-secondary-500">
              <div className="text-4xl mb-2">📅</div>
              <p className="text-lg font-medium mb-2">No shifts found</p>
              <p className="text-sm">
                {getActiveFilterCount() > 0
                  ? 'Try adjusting your filters or create a new shift'
                  : `No shifts scheduled for the selected date range`
                }
              </p>
              {getActiveFilterCount() > 0 && (
                <button
                  onClick={clearAllFilters}
                  className="mt-3 text-primary-600 hover:text-primary-800 text-sm underline"
                >
                  Clear all filters
                </button>
              )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-secondary-200">
                <thead className="bg-secondary-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Truck
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Driver
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Shift Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Time
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Duration
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-secondary-200">
                  {shifts.map((shift) => {
                    const truck = trucks.find(t => t.id === shift.truck_id);
                    const driver = drivers.find(d => d.id === shift.driver_id);
                    const duration = calculateDuration(shift.start_time, shift.end_time);
                    
                    return (
                      <tr key={shift.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-900">
                          {truck?.truck_number || 'Unknown'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-900">
                          {driver?.full_name || 'Unknown'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-900">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            shift.shift_type === 'day' ? 'bg-yellow-100 text-yellow-800' : 'bg-blue-100 text-blue-800'
                          }`}>
                            {shift.shift_type === 'day' ? '☀️ Day' : '🌙 Night'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-900">
                          {shift.start_time?.substring(0, 5)} - {shift.end_time?.substring(0, 5)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-900">
                          {duration}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {getStatusBadge(shift.status)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center space-x-2">
                            {/* Edit Button */}
                            <button
                              onClick={() => handleEditShift(shift)}
                              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                              title="Edit Shift"
                            >
                              ✏️ Edit
                            </button>

                            {/* Status Action Buttons */}
                            {shift.status === 'scheduled' && (
                              <button
                                onClick={() => handleStatusChange(shift.id, 'active')}
                                className="text-green-600 hover:text-green-800 text-sm font-medium"
                                title="Activate Shift"
                              >
                                ▶️ Activate
                              </button>
                            )}

                            {shift.status === 'active' && (
                              <button
                                onClick={() => handleStatusChange(shift.id, 'completed')}
                                className="text-gray-600 hover:text-gray-800 text-sm font-medium"
                                title="Complete Shift"
                              >
                                ✅ Complete
                              </button>
                            )}

                            {(shift.status === 'scheduled' || shift.status === 'active') && (
                              <button
                                onClick={() => handleStatusChange(shift.id, 'cancelled')}
                                className="text-orange-600 hover:text-orange-800 text-sm font-medium"
                                title="Cancel Shift"
                              >
                                ⏸️ Cancel
                              </button>
                            )}

                            {/* Delete Button - only for non-active shifts */}
                            {shift.status !== 'active' && (
                              <button
                                onClick={() => handleDeleteShift(shift)}
                                className="text-red-600 hover:text-red-800 text-sm font-medium"
                                title="Delete Shift"
                              >
                                🗑️ Delete
                              </button>
                            )}
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Create/Edit Shift Modal */}
      {showCreateModal && (
        <CreateShiftModal
          trucks={trucks}
          drivers={drivers}
          defaultDate={filters.date_from}
          editingShift={editingShift}
          onClose={() => {
            setShowCreateModal(false);
            setEditingShift(null);
          }}
          onSuccess={() => {
            setShowCreateModal(false);
            setEditingShift(null);
            loadShifts();
          }}
        />
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <DeleteConfirmationModal
          shift={shiftToDelete}
          onClose={() => {
            setShowDeleteModal(false);
            setShiftToDelete(null);
          }}
          onConfirm={confirmDeleteShift}
        />
      )}
    </div>
  );
};

// Helper function to calculate duration
const calculateDuration = (startTime, endTime) => {
  if (!startTime || !endTime) return 'N/A';
  
  const start = new Date(`2000-01-01T${startTime}`);
  const end = new Date(`2000-01-01T${endTime}`);
  
  // Handle overnight shifts
  if (end < start) {
    end.setDate(end.getDate() + 1);
  }
  
  const diffMs = end - start;
  const hours = Math.floor(diffMs / (1000 * 60 * 60));
  const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
  
  return `${hours}h ${minutes}m`;
};

// Enhanced Create/Edit Shift Modal with Unified Date Range Approach
const CreateShiftModal = ({ trucks, drivers, defaultDate, editingShift, onClose, onSuccess }) => {
  // Helper function to get appropriate dates for editing
  const getEditDates = (shift) => {
    if (!shift) return { start_date: defaultDate, end_date: defaultDate };

    // Prefer start_date/end_date if available
    if (shift.start_date && shift.end_date) {
      return { start_date: shift.start_date, end_date: shift.end_date };
    }

    // Fallback to shift_date for both
    if (shift.shift_date) {
      return { start_date: shift.shift_date, end_date: shift.shift_date };
    }

    // Default fallback
    return { start_date: defaultDate, end_date: defaultDate };
  };

  const editDates = getEditDates(editingShift);

  const [formData, setFormData] = useState({
    truck_id: editingShift?.truck_id || '',
    driver_id: editingShift?.driver_id || '',
    shift_type: editingShift?.shift_type || 'day',
    start_date: editDates.start_date,
    end_date: editDates.end_date,
    start_time: editingShift?.start_time?.substring(0, 5) || '06:00',
    end_time: editingShift?.end_time?.substring(0, 5) || '18:00'
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  // Preset times based on shift type
  const handleShiftTypeChange = (shiftType) => {
    setFormData(prev => ({
      ...prev,
      shift_type: shiftType,
      start_time: shiftType === 'day' ? '06:00' : '18:00',
      end_time: shiftType === 'day' ? '18:00' : '06:00'
    }));
  };



  const validateForm = () => {
    const newErrors = {};
    if (!formData.truck_id) newErrors.truck_id = 'Please select a truck';
    if (!formData.driver_id) newErrors.driver_id = 'Please select a driver';
    if (!formData.start_date) newErrors.start_date = 'Start date is required';
    if (!formData.end_date) newErrors.end_date = 'End date is required';

    // Validate date range
    if (formData.start_date && formData.end_date) {
      if (new Date(formData.end_date) < new Date(formData.start_date)) {
        newErrors.end_date = 'End date must be after or equal to start date';
      }

      // Check for max 31 days
      const start = new Date(formData.start_date);
      const end = new Date(formData.end_date);
      const diffTime = Math.abs(end - start);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
      if (diffDays > 31) {
        newErrors.end_date = 'Date range cannot exceed 31 days';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    setLoading(true);
    try {
      const baseShiftData = {
        truck_id: parseInt(formData.truck_id),
        driver_id: parseInt(formData.driver_id),
        shift_type: formData.shift_type,
        start_time: `${formData.start_time}:00`,
        end_time: `${formData.end_time}:00`,
        status: 'scheduled'
      };

      // Unified data structure using start_date/end_date for all operations
      const unifiedShiftData = {
        ...baseShiftData,
        start_date: formData.start_date,
        end_date: formData.end_date,
        recurrence_pattern: formData.start_date === formData.end_date ? 'single' : 'custom'
      };

      if (editingShift) {
        // Update existing shift with unified approach
        await shiftService.updateShift(editingShift.id, unifiedShiftData);

        const isRangeEdit = formData.start_date !== formData.end_date;
        const message = isRangeEdit
          ? `Shift updated to ${getDaysInRange(formData.start_date, formData.end_date)} days successfully!`
          : 'Shift updated successfully!';
        toast.success(message);
      } else {
        // Create new shift with unified approach
        await shiftService.createShift(unifiedShiftData);

        const isRangeCreate = formData.start_date !== formData.end_date;
        const message = isRangeCreate
          ? `${getDaysInRange(formData.start_date, formData.end_date)} shifts created successfully!`
          : 'Shift created successfully!';
        toast.success(message);
      }

      onSuccess();
    } catch (error) {
      console.error(`Error ${editingShift ? 'updating' : 'creating'} shift:`, error);
      toast.error(error.message || `Failed to ${editingShift ? 'update' : 'create'} shift`);
    } finally {
      setLoading(false);
    }
  };

  // Helper function to calculate days in range
  const getDaysInRange = (startDate, endDate) => {
    if (!startDate || !endDate) return 0;
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end - start);
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
  };

  // Helper function for unified date range summary
  const getUnifiedDateRangeSummary = () => {
    if (!formData.start_date || !formData.end_date) {
      return 'Please select dates';
    }

    const isSingleDay = formData.start_date === formData.end_date;

    if (isSingleDay) {
      const date = new Date(formData.start_date);
      return `Single shift on ${date.toLocaleDateString()}`;
    } else {
      const days = getDaysInRange(formData.start_date, formData.end_date);
      const startDate = new Date(formData.start_date);
      const endDate = new Date(formData.end_date);
      return `${days} shifts from ${startDate.toLocaleDateString()} to ${endDate.toLocaleDateString()}`;
    }
  };

  const duration = calculateDuration(formData.start_time, formData.end_time);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium text-secondary-900">
            {editingShift ? 'Edit Shift' : 'Create New Shift'}
          </h3>
          <button onClick={onClose} className="text-secondary-400 hover:text-secondary-600">✕</button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Truck Selection */}
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-1">
              Dump Truck *
            </label>
            <select
              value={formData.truck_id}
              onChange={(e) => setFormData(prev => ({ ...prev, truck_id: e.target.value }))}
              className={`w-full border rounded-md px-3 py-2 ${errors.truck_id ? 'border-red-500' : 'border-secondary-300'}`}
            >
              <option value="">Select a truck</option>
              {trucks.map(truck => (
                <option key={truck.id} value={truck.id}>
                  {truck.truck_number} - {truck.license_plate}
                </option>
              ))}
            </select>
            {errors.truck_id && <p className="text-red-500 text-xs mt-1">{errors.truck_id}</p>}
          </div>

          {/* Driver Selection */}
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-1">
              Driver *
            </label>
            <select
              value={formData.driver_id}
              onChange={(e) => setFormData(prev => ({ ...prev, driver_id: e.target.value }))}
              className={`w-full border rounded-md px-3 py-2 ${errors.driver_id ? 'border-red-500' : 'border-secondary-300'}`}
            >
              <option value="">Select a driver</option>
              {drivers.map(driver => (
                <option key={driver.id} value={driver.id}>
                  {driver.full_name} - {driver.employee_id}
                </option>
              ))}
            </select>
            {errors.driver_id && <p className="text-red-500 text-xs mt-1">{errors.driver_id}</p>}
          </div>

          {/* Shift Type */}
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-2">
              Shift Type
            </label>
            <div className="grid grid-cols-2 gap-3">
              <button
                type="button"
                onClick={() => handleShiftTypeChange('day')}
                className={`p-3 rounded-md border text-center ${
                  formData.shift_type === 'day'
                    ? 'border-primary-500 bg-primary-50 text-primary-700'
                    : 'border-secondary-300 bg-white text-secondary-700'
                }`}
              >
                <div className="text-lg">☀️</div>
                <div className="text-sm font-medium">Day Shift</div>
                <div className="text-xs text-secondary-500">6:00 AM - 6:00 PM</div>
              </button>
              <button
                type="button"
                onClick={() => handleShiftTypeChange('night')}
                className={`p-3 rounded-md border text-center ${
                  formData.shift_type === 'night'
                    ? 'border-primary-500 bg-primary-50 text-primary-700'
                    : 'border-secondary-300 bg-white text-secondary-700'
                }`}
              >
                <div className="text-lg">🌙</div>
                <div className="text-sm font-medium">Night Shift</div>
                <div className="text-xs text-secondary-500">6:00 PM - 6:00 AM</div>
              </button>
            </div>
          </div>

          {/* Unified Date Range Selection */}
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-2">
              📅 Date Range
              <span className="text-xs text-secondary-500 ml-2">
                (Set same start and end date for single-day shifts)
              </span>
            </label>
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-xs font-medium text-secondary-600 mb-1">
                  Start Date
                </label>
                <input
                  type="date"
                  value={formData.start_date}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    start_date: e.target.value,
                    // Auto-set end_date to start_date if it's currently empty or before start_date
                    end_date: !prev.end_date || new Date(prev.end_date) < new Date(e.target.value)
                      ? e.target.value
                      : prev.end_date
                  }))}
                  className={`input text-sm ${errors.start_date ? 'border-red-500' : ''}`}
                  required
                />
                {errors.start_date && (
                  <p className="text-red-500 text-xs mt-1">{errors.start_date}</p>
                )}
              </div>
              <div>
                <label className="block text-xs font-medium text-secondary-600 mb-1">
                  End Date
                </label>
                <input
                  type="date"
                  value={formData.end_date}
                  onChange={(e) => setFormData(prev => ({ ...prev, end_date: e.target.value }))}
                  className={`input text-sm ${errors.end_date ? 'border-red-500' : ''}`}
                  min={formData.start_date}
                  required
                />
                {errors.end_date && (
                  <p className="text-red-500 text-xs mt-1">{errors.end_date}</p>
                )}
              </div>
            </div>
          </div>

          {/* Schedule Summary */}
          <div className="bg-blue-50 p-3 rounded-md">
            <div className="text-sm font-medium text-blue-700">
              📋 Schedule Summary: {getUnifiedDateRangeSummary()}
            </div>
            {formData.start_date && formData.end_date && formData.start_date !== formData.end_date && (
              <div className="text-xs text-blue-600 mt-1">
                This will create individual shifts for each day in the range with the selected times.
              </div>
            )}
          </div>

          {/* Time Range */}
          <div className="grid grid-cols-2 gap-3">
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                Start Time
              </label>
              <input
                type="time"
                value={formData.start_time}
                onChange={(e) => setFormData(prev => ({ ...prev, start_time: e.target.value }))}
                className="w-full border border-secondary-300 rounded-md px-3 py-2"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                End Time
              </label>
              <input
                type="time"
                value={formData.end_time}
                onChange={(e) => setFormData(prev => ({ ...prev, end_time: e.target.value }))}
                className="w-full border border-secondary-300 rounded-md px-3 py-2"
              />
            </div>
          </div>

          {/* Duration Display */}
          <div className="bg-secondary-50 p-3 rounded-md">
            <div className="text-sm font-medium text-secondary-700">
              Duration: <span className="text-primary-600">{duration}</span>
            </div>
          </div>

          {/* Submit Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-secondary-700 border border-secondary-300 rounded-md hover:bg-secondary-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50"
            >
              {loading ? (editingShift ? 'Updating...' : 'Creating...') : (editingShift ? 'Update Shift' : 'Create Shift')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Delete Confirmation Modal
const DeleteConfirmationModal = ({ shift, onClose, onConfirm }) => {
  const truck = shift ? `Truck ${shift.truck_id}` : '';
  const driver = shift ? `Driver ${shift.driver_id}` : '';
  const date = shift ? new Date(shift.shift_date || shift.start_date).toLocaleDateString() : '';

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-secondary-900">Confirm Delete</h3>
          <button onClick={onClose} className="text-secondary-400 hover:text-secondary-600">✕</button>
        </div>

        <div className="mb-6">
          <div className="flex items-center mb-3">
            <span className="text-2xl mr-3">⚠️</span>
            <div>
              <p className="text-secondary-900 font-medium">Delete this shift?</p>
              <p className="text-secondary-600 text-sm">This action cannot be undone.</p>
            </div>
          </div>

          <div className="bg-secondary-50 p-3 rounded-md">
            <p className="text-sm text-secondary-700">
              <strong>Shift Details:</strong><br />
              {truck} - {driver}<br />
              {date} ({shift?.start_time?.substring(0, 5)} - {shift?.end_time?.substring(0, 5)})<br />
              Status: {shift?.status}
            </p>
          </div>

          {shift?.status === 'active' && (
            <div className="bg-red-50 border border-red-200 p-3 rounded-md mt-3">
              <p className="text-red-700 text-sm">
                ⚠️ <strong>Warning:</strong> This shift is currently active. Deleting it may affect ongoing operations.
              </p>
            </div>
          )}
        </div>

        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 text-secondary-700 border border-secondary-300 rounded-md hover:bg-secondary-50"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Delete Shift
          </button>
        </div>
      </div>
    </div>
  );
};

export default SimplifiedShiftManagement;
