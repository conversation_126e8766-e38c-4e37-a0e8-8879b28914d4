/**
 * Check Current Database State
 * Identify what's causing the assignment switching issue
 */

const { Pool } = require('pg');

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 5432,
  database: process.env.DB_NAME || 'hauling_qr_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'PostgreSQLPassword'
};

const pool = new Pool(dbConfig);

async function checkCurrentState() {
  try {
    console.log('🔍 CHECKING CURRENT DATABASE STATE...\n');

    // Check all trips for DT-100
    console.log('📊 All Trips for DT-100:');
    const allTrips = await pool.query(`
      SELECT 
        tl.id, tl.trip_number, tl.status, tl.assignment_id,
        tl.created_at, tl.updated_at,
        a.assignment_code, dt.truck_number
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      WHERE dt.truck_number = 'DT-100'
      ORDER BY tl.id DESC
    `);

    allTrips.rows.forEach(trip => {
      console.log(`  Trip ${trip.trip_number} (ID: ${trip.id}): ${trip.status}`);
      console.log(`    Assignment: ${trip.assignment_code} (ID: ${trip.assignment_id})`);
      console.log(`    Created: ${trip.created_at}`);
      console.log(`    Updated: ${trip.updated_at}`);
    });

    // Check active trips specifically
    console.log('\n📊 Active Trips for DT-100:');
    const activeTrips = await pool.query(`
      SELECT 
        tl.id, tl.trip_number, tl.status, tl.assignment_id,
        a.assignment_code
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      WHERE dt.truck_number = 'DT-100'
        AND tl.status NOT IN ('trip_completed', 'cancelled', 'breakdown')
      ORDER BY tl.created_at DESC
    `);

    if (activeTrips.rows.length > 0) {
      console.log(`  Found ${activeTrips.rows.length} active trips:`);
      activeTrips.rows.forEach(trip => {
        console.log(`    Trip ${trip.trip_number} (ID: ${trip.id}): ${trip.status}`);
        console.log(`      Assignment: ${trip.assignment_code} (ID: ${trip.assignment_id})`);
      });
    } else {
      console.log(`  ✅ No active trips found`);
    }

    // Check assignments for DT-100
    console.log('\n📊 Assignments for DT-100:');
    const assignments = await pool.query(`
      SELECT 
        id, assignment_code, status, truck_id,
        loading_location_id, unloading_location_id,
        created_at
      FROM assignments 
      WHERE truck_id = 1
      ORDER BY created_at DESC
      LIMIT 5
    `);

    assignments.rows.forEach(assignment => {
      console.log(`  ${assignment.assignment_code} (ID: ${assignment.id}): ${assignment.status}`);
      console.log(`    Loading: ${assignment.loading_location_id} | Unloading: ${assignment.unloading_location_id}`);
      console.log(`    Created: ${assignment.created_at}`);
    });

    // Check for assignment switching in recent trips
    console.log('\n📊 Recent Assignment Switching:');
    const assignmentSwitching = await pool.query(`
      SELECT 
        id, trip_number, status,
        notes->>'assignment_updated' as assignment_updated,
        notes->>'old_assignment_id' as old_assignment_id,
        notes->>'new_assignment_id' as new_assignment_id,
        notes->>'update_reason' as update_reason
      FROM trip_logs
      WHERE notes::text LIKE '%assignment_updated%'
      ORDER BY created_at DESC
      LIMIT 3
    `);

    if (assignmentSwitching.rows.length > 0) {
      console.log(`  Found ${assignmentSwitching.rows.length} trips with assignment switching:`);
      assignmentSwitching.rows.forEach(trip => {
        console.log(`    Trip ${trip.trip_number} (ID: ${trip.id}): ${trip.status}`);
        console.log(`      Switch: ${trip.old_assignment_id} → ${trip.new_assignment_id}`);
        console.log(`      Reason: ${trip.update_reason}`);
      });
    } else {
      console.log(`  ✅ No assignment switching found`);
    }

    // Check what the getCurrentTripAndAssignment query would return
    console.log('\n📊 getCurrentTripAndAssignment Simulation:');
    const currentTripQuery = await pool.query(`
      SELECT 
        tl.id as trip_id, tl.trip_number, tl.status as trip_status,
        a.id as assignment_id, a.assignment_code, a.status as assignment_status
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN trip_logs tl ON a.id = tl.assignment_id
        AND tl.status NOT IN ('trip_completed', 'cancelled', 'breakdown')
      WHERE dt.truck_number = 'DT-100'
        AND a.status IN ('assigned', 'in_progress')
      ORDER BY a.created_at DESC, tl.created_at DESC
      LIMIT 1
    `);

    if (currentTripQuery.rows.length > 0) {
      const result = currentTripQuery.rows[0];
      console.log(`  Assignment: ${result.assignment_code} (ID: ${result.assignment_id}) - ${result.assignment_status}`);
      console.log(`  Trip: ${result.trip_number || 'NULL'} (ID: ${result.trip_id || 'NULL'}) - ${result.trip_status || 'NULL'}`);
    } else {
      console.log(`  ✅ No current trip/assignment found`);
    }

    return true;

  } catch (error) {
    console.error('❌ Check failed:', error.message);
    return false;
  } finally {
    await pool.end();
  }
}

// Run check if called directly
if (require.main === module) {
  checkCurrentState().then(success => {
    console.log(`\n🎯 Check completed: ${success ? '✅ SUCCESS' : '❌ FAILED'}`);
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('Check execution failed:', error);
    process.exit(1);
  });
}

module.exports = checkCurrentState;
