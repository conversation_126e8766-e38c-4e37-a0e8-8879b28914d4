const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const auth = require('../middleware/auth');
const Joi = require('joi');
const ShiftDisplayHelper = require('../utils/ShiftDisplayHelper');

// Enhanced validation schemas for date range support
const shiftSchema = Joi.object({
  truck_id: Joi.number().integer().positive().required(),
  driver_id: Joi.number().integer().positive().required(),
  shift_type: Joi.string().valid('day', 'night', 'custom').required(),
  display_type: Joi.string().valid('day', 'night', 'custom').optional(),

  // Date fields - either single date or date range
  shift_date: Joi.date().optional(),
  start_date: Joi.date().optional(),
  end_date: Joi.date().optional(),
  recurrence_pattern: Joi.string().valid('single', 'daily', 'weekly', 'weekdays', 'weekends', 'custom').default('single'),

  start_time: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/).required(),
  end_time: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/).required(),
  status: Joi.string().valid('scheduled', 'active', 'completed', 'cancelled').optional(),
  handover_notes: Joi.string().max(1000).optional().allow('')
}).custom((value, helpers) => {
  // Custom validation for date configuration
  const { recurrence_pattern, shift_date, start_date, end_date } = value;

  if (recurrence_pattern === 'single') {
    if (!shift_date) {
      return helpers.error('custom.singleDateRequired');
    }
  } else {
    if (!start_date || !end_date) {
      return helpers.error('custom.dateRangeRequired');
    }
    if (new Date(end_date) < new Date(start_date)) {
      return helpers.error('custom.invalidDateRange');
    }
  }

  return value;
}, 'Date configuration validation').messages({
  'custom.singleDateRequired': 'shift_date is required for single shifts',
  'custom.dateRangeRequired': 'start_date and end_date are required for recurring shifts',
  'custom.invalidDateRange': 'end_date must be after start_date'
});

const updateShiftSchema = shiftSchema.fork(['truck_id', 'driver_id', 'shift_type', 'shift_date', 'start_date', 'end_date', 'start_time', 'end_time'], (schema) => schema.optional());

// @route   GET /api/shifts
// @desc    Get all shifts with filtering options
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const { 
      truck_id, 
      driver_id, 
      shift_date, 
      status, 
      shift_type,
      start_date,
      end_date 
    } = req.query;

    let whereConditions = [];
    let queryParams = [];
    let paramIndex = 1;

    // Build dynamic WHERE clause
    if (truck_id) {
      whereConditions.push(`ds.truck_id = $${paramIndex}`);
      queryParams.push(truck_id);
      paramIndex++;
    }

    if (driver_id) {
      whereConditions.push(`ds.driver_id = $${paramIndex}`);
      queryParams.push(driver_id);
      paramIndex++;
    }

    if (shift_date) {
      whereConditions.push(`ds.shift_date = $${paramIndex}`);
      queryParams.push(shift_date);
      paramIndex++;
    }

    if (status) {
      whereConditions.push(`ds.status = $${paramIndex}`);
      queryParams.push(status);
      paramIndex++;
    }

    if (shift_type) {
      whereConditions.push(`ds.shift_type = $${paramIndex}`);
      queryParams.push(shift_type);
      paramIndex++;
    }

    // Date range filtering
    if (start_date && end_date) {
      whereConditions.push(`ds.shift_date BETWEEN $${paramIndex} AND $${paramIndex + 1}`);
      queryParams.push(start_date, end_date);
      paramIndex += 2;
    } else if (start_date) {
      whereConditions.push(`ds.shift_date >= $${paramIndex}`);
      queryParams.push(start_date);
      paramIndex++;
    } else if (end_date) {
      whereConditions.push(`ds.shift_date <= $${paramIndex}`);
      queryParams.push(end_date);
      paramIndex++;
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    const shiftsQuery = `
      SELECT 
        ds.id,
        ds.truck_id,
        ds.driver_id,
        ds.shift_type,
        ds.shift_date,
        ds.start_time,
        ds.end_time,
        ds.status,
        ds.previous_shift_id,
        ds.handover_notes,
        ds.handover_completed_at,
        ds.assignment_id,
        ds.auto_created,
        ds.created_at,
        ds.updated_at,
        dt.truck_number,
        dt.license_plate,
        d.full_name as driver_name,
        d.employee_id,
        a.assignment_code,
        a.status as assignment_status
      FROM driver_shifts ds
      JOIN dump_trucks dt ON ds.truck_id = dt.id
      JOIN drivers d ON ds.driver_id = d.id
      LEFT JOIN assignments a ON ds.assignment_id = a.id
      ${whereClause}
      ORDER BY ds.shift_date DESC, ds.start_time ASC
    `;

    const result = await query(shiftsQuery, queryParams);

    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length
    });

  } catch (error) {
    console.error('Get shifts error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve shifts'
    });
  }
});

// @route   GET /api/shifts/current/:truck_id
// @desc    Get current active shift for a truck
// @access  Private
router.get('/current/:truck_id', auth, async (req, res) => {
  try {
    const { truck_id } = req.params;

    const currentShiftQuery = `
      SELECT 
        ds.id,
        ds.truck_id,
        ds.driver_id,
        ds.shift_type,
        ds.shift_date,
        ds.start_time,
        ds.end_time,
        ds.status,
        ds.assignment_id,
        dt.truck_number,
        d.full_name as driver_name,
        d.employee_id,
        a.assignment_code
      FROM driver_shifts ds
      JOIN dump_trucks dt ON ds.truck_id = dt.id
      JOIN drivers d ON ds.driver_id = d.id
      LEFT JOIN assignments a ON ds.assignment_id = a.id
      WHERE ds.truck_id = $1
        AND ds.status = 'active'
        AND ds.shift_date = CURRENT_DATE
        AND CURRENT_TIME BETWEEN ds.start_time AND 
            CASE 
              WHEN ds.end_time < ds.start_time 
              THEN ds.end_time + interval '24 hours'
              ELSE ds.end_time 
            END
      ORDER BY ds.created_at DESC
      LIMIT 1
    `;

    const result = await query(currentShiftQuery, [truck_id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'No active shift found for this truck'
      });
    }

    res.json({
      success: true,
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Get current shift error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve current shift'
    });
  }
});

// @route   POST /api/shifts
// @desc    Create new shift
// @access  Private
router.post('/', auth, async (req, res) => {
  try {
    console.log('SHIFT_CREATE_REQUEST', 'Received shift creation request', {
      body: req.body,
      recurrence_pattern: req.body.recurrence_pattern,
      shift_date: req.body.shift_date,
      start_date: req.body.start_date,
      end_date: req.body.end_date
    });

    // Validate input
    const { error } = shiftSchema.validate(req.body);
    if (error) {
      console.log('SHIFT_VALIDATION_ERROR', 'Validation failed', {
        error: error.details[0].message,
        body: req.body
      });
      return res.status(400).json({
        error: 'Validation Error',
        message: error.details[0].message
      });
    }

    const {
      truck_id,
      driver_id,
      shift_type,
      display_type,
      shift_date,
      start_date,
      end_date,
      recurrence_pattern = 'single',
      start_time,
      end_time,
      status = 'scheduled',
      handover_notes = ''
    } = req.body;

    console.log('SHIFT_CREATE_EXTRACTED_DATA', 'Extracted data from request', {
      truck_id,
      driver_id,
      shift_type,
      display_type,
      shift_date,
      start_date,
      end_date,
      recurrence_pattern,
      start_time,
      end_time,
      status,
      handover_notes
    });

    // Simplified overlap check - only check for active and scheduled shifts
    let overlapCheck;
    if (recurrence_pattern === 'single') {
      overlapCheck = await query(`
        SELECT id, shift_type, start_time, end_time FROM driver_shifts
        WHERE truck_id = $1
          AND ((shift_date = $2) OR (start_date <= $2 AND end_date >= $2))
          AND driver_id = $3
          AND status IN ('active', 'scheduled')
      `, [truck_id, shift_date, driver_id]);
    } else {
      overlapCheck = await query(`
        SELECT id, shift_type, start_time, end_time FROM driver_shifts
        WHERE truck_id = $1
          AND driver_id = $2
          AND status IN ('active', 'scheduled')
          AND (
            (shift_date BETWEEN $3 AND $4) OR
            (start_date <= $4 AND end_date >= $3)
          )
      `, [truck_id, driver_id, start_date, end_date]);
    }

    if (overlapCheck.rows.length > 0) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'Shift overlaps with existing shift for this truck and driver'
      });
    }

    // Insert new shift with enhanced date range support
    const result = await query(`
      INSERT INTO driver_shifts
      (truck_id, driver_id, shift_type, display_type, shift_date, start_date, end_date, recurrence_pattern, start_time, end_time, status, handover_notes)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
      RETURNING *
    `, [
      truck_id,
      driver_id,
      shift_type,
      display_type || shift_type, // Use display_type if provided, otherwise fall back to shift_type
      shift_date,
      start_date,
      end_date,
      recurrence_pattern,
      start_time,
      end_time,
      status,
      handover_notes
    ]);

    // Get complete shift details with related data
    const shiftDetails = await query(`
      SELECT 
        ds.*,
        dt.truck_number,
        d.full_name as driver_name,
        d.employee_id
      FROM driver_shifts ds
      JOIN dump_trucks dt ON ds.truck_id = dt.id
      JOIN drivers d ON ds.driver_id = d.id
      WHERE ds.id = $1
    `, [result.rows[0].id]);

    res.status(201).json({
      success: true,
      message: 'Shift created successfully',
      data: shiftDetails.rows[0]
    });

  } catch (error) {
    console.error('Create shift error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to create shift'
    });
  }
});

// @route   PUT /api/shifts/:id
// @desc    Update shift
// @access  Private
router.put('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    // Validate input
    const { error } = updateShiftSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation Error',
        message: error.details[0].message
      });
    }

    // Check if shift exists
    const existingShift = await query('SELECT * FROM driver_shifts WHERE id = $1', [id]);
    if (existingShift.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Shift not found'
      });
    }

    // Build update query dynamically
    const updateFields = [];
    const updateValues = [];
    let paramIndex = 1;

    Object.keys(req.body).forEach(key => {
      if (req.body[key] !== undefined) {
        updateFields.push(`${key} = $${paramIndex}`);
        updateValues.push(req.body[key]);
        paramIndex++;
      }
    });

    if (updateFields.length === 0) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'No fields to update'
      });
    }

    updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
    updateValues.push(id);

    const updateQuery = `
      UPDATE driver_shifts 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING *
    `;

    const result = await query(updateQuery, updateValues);

    res.json({
      success: true,
      message: 'Shift updated successfully',
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Update shift error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to update shift'
    });
  }
});

// @route   PATCH /api/shifts/:id/cancel
// @desc    Cancel shift (soft delete by setting status to cancelled)
// @access  Private
router.patch('/:id/cancel', auth, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if shift exists
    const existingShift = await query('SELECT * FROM driver_shifts WHERE id = $1', [id]);
    if (existingShift.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Shift not found'
      });
    }

    // Soft delete by updating status
    const result = await query(`
      UPDATE driver_shifts
      SET status = 'cancelled', updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `, [id]);

    res.json({
      success: true,
      message: 'Shift cancelled successfully',
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Cancel shift error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to cancel shift'
    });
  }
});

// @route   DELETE /api/shifts/:id
// @desc    Delete shift permanently (hard delete)
// @access  Private
router.delete('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if shift exists
    const existingShift = await query('SELECT * FROM driver_shifts WHERE id = $1', [id]);
    if (existingShift.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Shift not found'
      });
    }

    // Check if shift can be deleted (only cancelled or scheduled shifts)
    const shift = existingShift.rows[0];
    if (shift.status === 'active' || shift.status === 'completed') {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Cannot delete active or completed shifts. Cancel the shift first.'
      });
    }

    // Hard delete from database
    await query('DELETE FROM driver_shifts WHERE id = $1', [id]);

    res.json({
      success: true,
      message: 'Shift deleted permanently'
    });

  } catch (error) {
    console.error('Delete shift error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to delete shift'
    });
  }
});

// @route   POST /api/shifts/handover
// @desc    Create shift handover
// @access  Private
router.post('/handover', auth, async (req, res) => {
  try {
    const {
      truck_id,
      outgoing_shift_id,
      incoming_shift_id,
      active_trip_id = null,
      handover_notes = '',
      fuel_level = null,
      vehicle_condition = ''
    } = req.body;

    // Validate required fields
    if (!truck_id || !outgoing_shift_id || !incoming_shift_id) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'truck_id, outgoing_shift_id, and incoming_shift_id are required'
      });
    }

    // Get trip context if active trip exists
    let tripContext = null;
    if (active_trip_id) {
      const tripQuery = `
        SELECT
          tl.id,
          tl.status,
          tl.actual_loading_location_id,
          tl.actual_unloading_location_id,
          a.loading_location_id,
          a.unloading_location_id
        FROM trip_logs tl
        JOIN assignments a ON tl.assignment_id = a.id
        WHERE tl.id = $1
      `;

      const tripResult = await query(tripQuery, [active_trip_id]);
      if (tripResult.rows.length > 0) {
        tripContext = tripResult.rows[0];
      }
    }

    // Create handover record
    const handoverResult = await query(`
      INSERT INTO shift_handovers (
        truck_id, outgoing_shift_id, incoming_shift_id,
        active_trip_id, trip_status_at_handover,
        location_at_handover, handover_notes,
        fuel_level, vehicle_condition, approved_by
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING *
    `, [
      truck_id,
      outgoing_shift_id,
      incoming_shift_id,
      active_trip_id,
      tripContext?.status || null,
      tripContext?.actual_loading_location_id || tripContext?.loading_location_id || null,
      handover_notes,
      fuel_level,
      vehicle_condition,
      req.user.id
    ]);

    // Update shift statuses
    await query(`
      UPDATE driver_shifts
      SET status = 'completed',
          handover_completed_at = CURRENT_TIMESTAMP,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
    `, [outgoing_shift_id]);

    await query(`
      UPDATE driver_shifts
      SET status = 'active',
          previous_shift_id = $1,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $2
    `, [outgoing_shift_id, incoming_shift_id]);

    res.status(201).json({
      success: true,
      message: 'Shift handover completed successfully',
      data: handoverResult.rows[0]
    });

  } catch (error) {
    console.error('Shift handover error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to complete shift handover'
    });
  }
});

// @route   GET /api/shifts/handovers
// @desc    Get shift handovers with filtering
// @access  Private
router.get('/handovers', auth, async (req, res) => {
  try {
    const { truck_id, start_date, end_date } = req.query;

    let whereConditions = ['1=1'];
    let queryParams = [];
    let paramIndex = 1;

    if (truck_id) {
      whereConditions.push(`sh.truck_id = $${paramIndex}`);
      queryParams.push(truck_id);
      paramIndex++;
    }

    if (start_date && end_date) {
      whereConditions.push(`sh.handover_time BETWEEN $${paramIndex} AND $${paramIndex + 1}`);
      queryParams.push(start_date, end_date);
      paramIndex += 2;
    }

    const handoversQuery = `
      SELECT
        sh.*,
        dt.truck_number,
        d1.full_name as outgoing_driver_name,
        d2.full_name as incoming_driver_name,
        tl.trip_number,
        l.name as location_name,
        u.full_name as approved_by_name
      FROM shift_handovers sh
      JOIN dump_trucks dt ON sh.truck_id = dt.id
      JOIN driver_shifts ds1 ON sh.outgoing_shift_id = ds1.id
      JOIN driver_shifts ds2 ON sh.incoming_shift_id = ds2.id
      JOIN drivers d1 ON ds1.driver_id = d1.id
      JOIN drivers d2 ON ds2.driver_id = d2.id
      LEFT JOIN trip_logs tl ON sh.active_trip_id = tl.id
      LEFT JOIN locations l ON sh.location_at_handover = l.id
      LEFT JOIN users u ON sh.approved_by = u.id
      WHERE ${whereConditions.join(' AND ')}
      ORDER BY sh.handover_time DESC
    `;

    const result = await query(handoversQuery, queryParams);

    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length
    });

  } catch (error) {
    console.error('Get handovers error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve handovers'
    });
  }
});

// @route   POST /api/shifts/activate/:id
// @desc    Manually activate a shift
// @access  Private
router.post('/activate/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if shift exists and is scheduled
    const shift = await query(`
      SELECT * FROM driver_shifts
      WHERE id = $1 AND status = 'scheduled'
    `, [id]);

    if (shift.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Scheduled shift not found'
      });
    }

    // Deactivate any currently active shifts for this truck
    await query(`
      UPDATE driver_shifts
      SET status = 'completed', updated_at = CURRENT_TIMESTAMP
      WHERE truck_id = $1 AND status = 'active'
    `, [shift.rows[0].truck_id]);

    // Activate the shift
    const result = await query(`
      UPDATE driver_shifts
      SET status = 'active', updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `, [id]);

    res.json({
      success: true,
      message: 'Shift activated successfully',
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Activate shift error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to activate shift'
    });
  }
});

// @route   GET /api/shifts/current-drivers
// @desc    Get current active drivers for display purposes only (does not affect workflow)
// @access  Private
router.get('/current-drivers', auth, async (req, res) => {
  try {
    const { truck_ids } = req.query;

    if (!truck_ids) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'truck_ids parameter is required (comma-separated list)'
      });
    }

    // Parse truck IDs
    const truckIdArray = truck_ids.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));

    if (truckIdArray.length === 0) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'No valid truck IDs provided'
      });
    }

    // Get current drivers for display
    const currentDrivers = await ShiftDisplayHelper.getCurrentDriversForDisplay(truckIdArray);

    res.json({
      success: true,
      data: currentDrivers,
      note: 'This data is for display purposes only and does not affect the 4-phase workflow'
    });

  } catch (error) {
    console.error('Get current drivers error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve current drivers'
    });
  }
});

// @route   GET /api/shifts/current-driver/:truck_id
// @desc    Get current active driver for a specific truck (display only)
// @access  Private
router.get('/current-driver/:truck_id', auth, async (req, res) => {
  try {
    const { truck_id } = req.params;

    if (!truck_id || isNaN(parseInt(truck_id))) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'Valid truck_id is required'
      });
    }

    // Get current driver for display
    const currentDriver = await ShiftDisplayHelper.getCurrentDriverForDisplay(parseInt(truck_id));

    res.json({
      success: true,
      data: currentDriver,
      note: 'This data is for display purposes only and does not affect the 4-phase workflow'
    });

  } catch (error) {
    console.error('Get current driver error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve current driver'
    });
  }
});

// @route   GET /api/shifts/debug/:truck_id
// @desc    Debug endpoint to check existing shifts for a truck
// @access  Private
router.get('/debug/:truck_id', auth, async (req, res) => {
  try {
    const { truck_id } = req.params;

    // Get all shifts for this truck
    const shifts = await query(`
      SELECT
        ds.*,
        d.full_name as driver_name,
        dt.truck_number
      FROM driver_shifts ds
      JOIN drivers d ON ds.driver_id = d.id
      JOIN dump_trucks dt ON ds.truck_id = dt.id
      WHERE ds.truck_id = $1
      ORDER BY ds.shift_date DESC, ds.start_time ASC
    `, [truck_id]);

    // Get truck info
    const truckInfo = await query(`
      SELECT id, truck_number, license_plate
      FROM dump_trucks
      WHERE id = $1
    `, [truck_id]);

    res.json({
      success: true,
      data: {
        truck: truckInfo.rows[0] || null,
        shifts: shifts.rows,
        totalShifts: shifts.rows.length
      }
    });

  } catch (error) {
    console.error('Debug shifts error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to debug shifts'
    });
  }
});

module.exports = router;
