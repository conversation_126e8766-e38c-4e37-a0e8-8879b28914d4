import { useState, useEffect, useCallback, useMemo } from 'react';
import api from '../../services/api';
import { saveAs } from 'file-saver';
import toast from 'react-hot-toast';

const TruckTripSummary = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [realTimeStats, setRealTimeStats] = useState({
    total_trucks: 0,
    completed_trips: 0,
    in_progress_trips: 0
  });
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 10,
    hasNextPage: false,
    hasPrevPage: false
  });

  // Filter and search states
  const [filters, setFilters] = useState({
    search: '',
    date_from: '',
    date_to: '',
    sortBy: 'truck_number',
    sortOrder: 'asc'
  });

  // Auto-refresh interval
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(30); // seconds

  // Columns for the table (with date but no grouping by date)
  const columns = [
    { label: 'Date', key: 'date' },
    { label: 'Truck Number', key: 'truck_number' },
    { label: 'Driver', key: 'driver_name' },
    { label: 'Loading Location', key: 'loading_location_name' },
    { label: 'Unloading Location', key: 'unloading_location_name' },
    { label: 'Trips Completed', key: 'trips_completed' }
  ];

  // Load truck trip summary with error handling
  const loadTruckSummary = useCallback(async (page = 1) => {
    setLoading(true);
    try {
      const params = {
        page,
        limit: 10,
        // Map frontend filters to API parameters
        ...filters
      };

      // Remove empty parameters
      Object.keys(params).forEach(key => {
        if (params[key] === '' || params[key] === undefined) {
          delete params[key];
        }
      });

      const response = await api.get('/trucks/trip-summary', { params });
      const rawData = response.data.data || [];

      // Update real-time stats from API response
      if (response.data.summary) {
        setRealTimeStats({
          total_trucks: response.data.summary.total_trucks || 0,
          completed_trips: response.data.summary.completed_trips || 0,
          in_progress_trips: response.data.summary.in_progress_trips || 0
        });
      }

      // Process data - the API already provides accurate trip counts from trip_logs
      const processedData = rawData.map(item => ({
        ...item,
        // Use the counts already calculated by the API
        trips_completed: item.completed_trips || 0,
        trips_in_progress: (item.loading_trips || 0) + (item.unloading_trips || 0),
        total_trips: item.total_trips || 0,
        // Format the date from last_trip_date or assigned_date
        date: item.last_trip_date
          ? new Date(item.last_trip_date).toLocaleDateString()
          : item.assigned_date
            ? new Date(item.assigned_date).toLocaleDateString()
            : 'N/A'
      }));

      // Data is now filtered and sorted by the API
      setData(processedData);

      // Use pagination from API response
      if (response.data.pagination) {
        setPagination(response.data.pagination);
      }
    } catch (error) {
      console.error('Error loading truck trip summary:', error);
      if (error.code === 'ERR_NETWORK') {
        toast.error('Network error. Please check your connection.');
      } else if (error.response?.status === 429) {
        toast.error('Too many requests. Please wait a moment.');
      } else {
        toast.error(error.response?.data?.message || 'Failed to load truck trip summary');
      }
    } finally {
      setLoading(false);
    }
  }, [filters]);

  // Remove debounced function - using proven pattern from Drivers/Trucks pages

  // Auto-refresh functionality
  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      const interval = setInterval(() => {
        loadTruckSummary(pagination.currentPage);
      }, refreshInterval * 1000);

      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval, loadTruckSummary, pagination.currentPage]);

  // Initial load
  useEffect(() => {
    loadTruckSummary();
  }, [loadTruckSummary]);

  // Handle page change
  const handlePageChange = (page) => {
    loadTruckSummary(page);
  };

  // Handle filter changes
  const handleFilterChange = (newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  // Handle sorting
  const handleSort = (column) => {
    const newSortOrder = filters.sortBy === column && filters.sortOrder === 'asc' ? 'desc' : 'asc';
    handleFilterChange({
      sortBy: column,
      sortOrder: newSortOrder
    });
  };

  // Handle search - use proven pattern from Drivers/Trucks pages
  const handleSearch = (searchTerm) => {
    setFilters(prev => ({ ...prev, search: searchTerm }));
  };

  // Clear all filters
  const clearFilters = () => {
    setFilters({
      search: '',
      date_from: '',
      date_to: '',
      sortBy: 'truck_number',
      sortOrder: 'asc'
    });
  };

  // Handle manual refresh
  const handleRefresh = () => {
    loadTruckSummary(pagination.currentPage);
    toast.success('Truck summary refreshed');
  };

  // Count active filters
  const activeFiltersCount = Object.entries(filters).filter(([key, value]) => 
    key !== 'sortBy' && key !== 'sortOrder' && value !== ''
  ).length;

  // Truck summary statistics - use real-time stats from API
  const summaryStats = useMemo(() => {
    return {
      totalTrucks: realTimeStats.total_trucks,
      completedTrips: realTimeStats.completed_trips,
      inProgressTrips: realTimeStats.in_progress_trips
    };
  }, [realTimeStats]);

  // Handle export to CSV
  const handleExportCSV = () => {
    const headers = columns.map(col => col.label).join(',');
    const rows = data.map(row =>
      columns.map(col => `"${(row[col.key] || '').toString().replace(/"/g, '""')}"`).join(',')
    );
    const csvContent = [headers, ...rows].join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    saveAs(blob, 'truck_trip_summary.csv');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Truck Trip Summary</h1>
          <p className="text-secondary-600 mt-1">Overview of truck trip activities</p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          {/* Auto-refresh toggle */}
          <div className="flex items-center space-x-2">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                className="rounded border-secondary-300 text-primary-600 focus:ring-primary-500"
              />
              <span className="ml-2 text-sm text-secondary-700">Auto-refresh</span>
            </label>
            {autoRefresh && (
              <select
                value={refreshInterval}
                onChange={(e) => setRefreshInterval(parseInt(e.target.value))}
                className="text-sm border-secondary-300 rounded-md"
              >
                <option value={10}>10s</option>
                <option value={30}>30s</option>
                <option value={60}>1m</option>
                <option value={300}>5m</option>
              </select>
            )}
          </div>
          
          <button
            onClick={handleRefresh}
            className="btn btn-secondary"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Refresh
          </button>
          <button
            onClick={handleExportCSV}
            className="btn btn-secondary"
          >
            Export CSV
          </button>
        </div>
      </div>

      {/* Truck Summary Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg shadow-sm border border-secondary-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-medium text-sm">{summaryStats.totalTrucks}</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-secondary-900">Total Trucks</p>
              <p className="text-xs text-secondary-500">Registered</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-secondary-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                <span className="text-yellow-600 font-medium text-sm">{summaryStats.inProgressTrips}</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-secondary-900">In Progress</p>
              <p className="text-xs text-secondary-500">Active trips</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-secondary-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                <span className="text-green-600 font-medium text-sm">{summaryStats.completedTrips}</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-secondary-900">Completed</p>
              <p className="text-xs text-secondary-500">Finished trips</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-secondary-200">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Search */}
          <div>
            <label htmlFor="search" className="block text-sm font-medium text-secondary-700 mb-1">
              Search
            </label>
            <input
              type="text"
              id="search"
              value={filters.search}
              onChange={(e) => handleSearch(e.target.value)}
              placeholder="Search truck summaries..."
              className="input"
            />
          </div>

          {/* Date From */}
          <div>
            <label htmlFor="date_from" className="block text-sm font-medium text-secondary-700 mb-1">
              From Date
            </label>
            <input
              type="date"
              id="date_from"
              value={filters.date_from}
              onChange={(e) => handleFilterChange({ date_from: e.target.value })}
              className="input"
            />
          </div>

          {/* Date To */}
          <div>
            <label htmlFor="date_to" className="block text-sm font-medium text-secondary-700 mb-1">
              To Date
            </label>
            <input
              type="date"
              id="date_to"
              value={filters.date_to}
              onChange={(e) => handleFilterChange({ date_to: e.target.value })}
              className="input"
            />
          </div>

          {/* Clear Filters */}
          <div className="flex items-end">
            <button
              onClick={clearFilters}
              disabled={activeFiltersCount === 0}
              className="btn btn-secondary w-full disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Clear Filters
              {activeFiltersCount > 0 && (
                <span className="ml-1 bg-primary-100 text-primary-800 text-xs rounded-full px-2 py-0.5">
                  {activeFiltersCount}
                </span>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Truck Summary Table */}
      <div className="bg-white shadow-lg overflow-hidden sm:rounded-lg border border-secondary-200">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-secondary-200">
            <thead className="bg-gradient-to-r from-secondary-50 to-secondary-100">
              <tr>
                {columns.map(col => (
                  <th
                    key={col.key}
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider cursor-pointer hover:bg-secondary-50 select-none"
                    onClick={() => handleSort(col.key)}
                  >
                    <div className="flex items-center space-x-1">
                      <span>{col.label}</span>
                      <div className="flex flex-col">
                        <svg
                          className={`w-3 h-3 ${filters.sortBy === col.key && filters.sortOrder === 'asc' ? 'text-primary-600' : 'text-secondary-400'}`}
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
                        </svg>
                        <svg
                          className={`w-3 h-3 -mt-1 ${filters.sortBy === col.key && filters.sortOrder === 'desc' ? 'text-primary-600' : 'text-secondary-400'}`}
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </div>
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-secondary-200">
              {loading ? (
                <tr>
                  <td colSpan={columns.length} className="px-6 py-4">
                    <div className="animate-pulse space-y-4">
                      {[...Array(5)].map((_, i) => (
                        <div key={i} className="flex space-x-4">
                          <div className="h-4 bg-secondary-200 rounded w-1/8"></div>
                          <div className="h-4 bg-secondary-200 rounded w-1/6"></div>
                          <div className="h-4 bg-secondary-200 rounded w-1/5"></div>
                          <div className="h-4 bg-secondary-200 rounded w-1/6"></div>
                          <div className="h-4 bg-secondary-200 rounded w-1/6"></div>
                          <div className="h-4 bg-secondary-200 rounded w-1/8"></div>
                        </div>
                      ))}
                    </div>
                  </td>
                </tr>
              ) : data.length === 0 ? (
                <tr>
                  <td colSpan={columns.length} className="px-6 py-12 text-center">
                    <div className="flex flex-col items-center">
                      <svg className="w-12 h-12 text-secondary-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                      </svg>
                      <h3 className="text-lg font-medium text-secondary-900 mb-2">No truck trip data found</h3>
                      <p className="text-secondary-500">
                        {filters.search || filters.date_from || filters.date_to
                          ? 'Try adjusting your search criteria'
                          : 'Trip summary data will appear here once trips are recorded'
                        }
                      </p>
                    </div>
                  </td>
                </tr>
              ) : (
                data.map(row => (
                  <tr key={row.truck_number + (row.driver_name || 'Multiple') + row.loading_location_name + row.unloading_location_name}
                      className="hover:bg-secondary-50 transition-colors duration-150">
                    {columns.map(col => (
                      <td key={col.key} className="px-6 py-4 whitespace-nowrap">
                        {col.key === 'truck_number' ? (
                          <div className="flex items-center">
                            <span className="text-2xl mr-2 sm:mr-3 flex-shrink-0">🚚</span>
                            <div className="text-sm font-medium text-secondary-900">{row[col.key]}</div>
                          </div>
                        ) : col.key === 'driver_name' ? (
                          <div className="text-sm text-secondary-900">{row[col.key] || 'Unassigned'}</div>
                        ) : col.key === 'loading_location_name' || col.key === 'unloading_location_name' ? (
                          <div className="text-sm text-secondary-900 flex items-center">
                            <span className={`${col.key === 'loading_location_name' ? 'text-green-600' : 'text-red-600'} mr-1 flex-shrink-0`}>📍</span>
                            <span className="truncate">{row[col.key] || 'Unknown'}</span>
                          </div>
                        ) : col.key === 'trips_completed' ? (
                          <div className="text-sm font-medium text-secondary-900">{row[col.key] || 0}</div>
                        ) : (
                          <div className="text-sm text-secondary-900">{row[col.key]}</div>
                        )}
                      </td>
                    ))}
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
        
        {/* Pagination */}
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-secondary-200 sm:px-6">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => handlePageChange(pagination.currentPage - 1)}
              disabled={!pagination.hasPrevPage}
              className="relative inline-flex items-center px-4 py-2 border border-secondary-300 text-sm font-medium rounded-md text-secondary-700 bg-white hover:bg-secondary-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              onClick={() => handlePageChange(pagination.currentPage + 1)}
              disabled={!pagination.hasNextPage}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-secondary-300 text-sm font-medium rounded-md text-secondary-700 bg-white hover:bg-secondary-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-secondary-700">
                Showing{' '}
                <span className="font-medium">
                  {(pagination.currentPage - 1) * pagination.itemsPerPage + 1}
                </span>{' '}
                to{' '}
                <span className="font-medium">
                  {Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems)}
                </span>{' '}
                of{' '}
                <span className="font-medium">{pagination.totalItems}</span>{' '}
                results
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  onClick={() => handlePageChange(pagination.currentPage - 1)}
                  disabled={!pagination.hasPrevPage}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-secondary-300 bg-white text-sm font-medium text-secondary-500 hover:bg-secondary-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="sr-only">Previous</span>
                  <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </button>
                
                {pagination.totalPages <= 5 ? (
                  [...Array(pagination.totalPages)].map((_, i) => (
                    <button
                      key={i}
                      onClick={() => handlePageChange(i + 1)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                        i + 1 === pagination.currentPage
                          ? 'z-10 bg-primary-50 border-primary-500 text-primary-600'
                          : 'bg-white border-secondary-300 text-secondary-500 hover:bg-secondary-50'
                      }`}
                    >
                      {i + 1}
                    </button>
                  ))
                ) : (
                  <>
                    {pagination.currentPage > 2 && (
                      <button
                        onClick={() => handlePageChange(1)}
                        className="relative inline-flex items-center px-4 py-2 border border-secondary-300 bg-white text-sm font-medium text-secondary-500 hover:bg-secondary-50"
                      >
                        1
                      </button>
                    )}
                    
                    {pagination.currentPage > 3 && (
                      <span className="relative inline-flex items-center px-4 py-2 border border-secondary-300 bg-white text-sm font-medium text-secondary-700">
                        ...
                      </span>
                    )}
                    
                    {pagination.currentPage > 1 && (
                      <button
                        onClick={() => handlePageChange(pagination.currentPage - 1)}
                        className="relative inline-flex items-center px-4 py-2 border border-secondary-300 bg-white text-sm font-medium text-secondary-500 hover:bg-secondary-50"
                      >
                        {pagination.currentPage - 1}
                      </button>
                    )}
                    
                    <button
                      className="z-10 bg-primary-50 border-primary-500 relative inline-flex items-center px-4 py-2 border text-sm font-medium text-primary-600"
                    >
                      {pagination.currentPage}
                    </button>
                    
                    {pagination.currentPage < pagination.totalPages && (
                      <button
                        onClick={() => handlePageChange(pagination.currentPage + 1)}
                        className="relative inline-flex items-center px-4 py-2 border border-secondary-300 bg-white text-sm font-medium text-secondary-500 hover:bg-secondary-50"
                      >
                        {pagination.currentPage + 1}
                      </button>
                    )}
                    
                    {pagination.currentPage < pagination.totalPages - 2 && (
                      <span className="relative inline-flex items-center px-4 py-2 border border-secondary-300 bg-white text-sm font-medium text-secondary-700">
                        ...
                      </span>
                    )}
                    
                    {pagination.currentPage < pagination.totalPages - 1 && (
                      <button
                        onClick={() => handlePageChange(pagination.totalPages)}
                        className="relative inline-flex items-center px-4 py-2 border border-secondary-300 bg-white text-sm font-medium text-secondary-500 hover:bg-secondary-50"
                      >
                        {pagination.totalPages}
                      </button>
                    )}
                  </>
                )}
                
                <button
                  onClick={() => handlePageChange(pagination.currentPage + 1)}
                  disabled={!pagination.hasNextPage}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-secondary-300 bg-white text-sm font-medium text-secondary-500 hover:bg-secondary-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="sr-only">Next</span>
                  <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  </svg>
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TruckTripSummary;
