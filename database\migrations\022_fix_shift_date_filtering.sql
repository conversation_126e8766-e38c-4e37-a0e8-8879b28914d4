-- Migration 022: Fix Shift Date Filtering for Night Shifts
-- Purpose: Fix the date filtering logic to properly handle night shifts that cross midnight
-- Date: 2025-07-09

-- Update the enhanced view with corrected shift filtering logic
CREATE OR REPLACE VIEW v_assignments_with_current_drivers AS
SELECT 
    a.*,
    dt.truck_number,
    dt.license_plate,
    d.full_name as assigned_driver_name,
    d.employee_id as assigned_driver_employee_id,
    
    -- Current driver from shift management
    ds.driver_id as current_driver_id,
    cd.full_name as current_driver_name,
    cd.employee_id as current_driver_employee_id,
    ds.shift_type as current_shift_type,
    
    -- Driver status
    CASE 
        WHEN ds.driver_id IS NOT NULL THEN 'active_shift'
        WHEN d.id IS NOT NULL THEN 'assigned_only'
        ELSE 'no_driver'
    END as driver_status,
    
    -- Location information
    ll.name as loading_location_name,
    ul.name as unloading_location_name
FROM assignments a
JOIN dump_trucks dt ON a.truck_id = dt.id
LEFT JOIN drivers d ON a.driver_id = d.id
LEFT JOIN locations ll ON a.loading_location_id = ll.id
LEFT JOIN locations ul ON a.unloading_location_id = ul.id
LEFT JOIN driver_shifts ds ON (
    ds.truck_id = a.truck_id 
    AND ds.status = 'active'
    AND (
      -- Day shift or night shift that doesn't cross midnight
      (ds.shift_date = CURRENT_DATE AND CURRENT_TIME BETWEEN ds.start_time AND ds.end_time)
      OR
      -- Night shift that crosses midnight - started yesterday, still active today
      (ds.shift_type = 'night' AND ds.end_time < ds.start_time AND 
       ds.shift_date = CURRENT_DATE - 1 AND CURRENT_TIME <= ds.end_time)
      OR
      -- Night shift that crosses midnight - started today
      (ds.shift_type = 'night' AND ds.end_time < ds.start_time AND 
       ds.shift_date = CURRENT_DATE AND CURRENT_TIME >= ds.start_time)
    )
)
LEFT JOIN drivers cd ON ds.driver_id = cd.id;

-- Update the get_current_active_driver function with corrected logic
CREATE OR REPLACE FUNCTION get_current_active_driver(p_truck_id INTEGER)
RETURNS INTEGER AS $$
DECLARE
    v_driver_id INTEGER;
BEGIN
    -- Get the current active driver for the truck from shift management
    SELECT ds.driver_id INTO v_driver_id
    FROM driver_shifts ds
    WHERE ds.truck_id = p_truck_id
      AND ds.status = 'active'
      AND (
        -- Day shift or night shift that doesn't cross midnight
        (ds.shift_date = CURRENT_DATE AND CURRENT_TIME BETWEEN ds.start_time AND ds.end_time)
        OR
        -- Night shift that crosses midnight - started yesterday, still active today
        (ds.shift_type = 'night' AND ds.end_time < ds.start_time AND 
         ds.shift_date = CURRENT_DATE - 1 AND CURRENT_TIME <= ds.end_time)
        OR
        -- Night shift that crosses midnight - started today
        (ds.shift_type = 'night' AND ds.end_time < ds.start_time AND 
         ds.shift_date = CURRENT_DATE AND CURRENT_TIME >= ds.start_time)
      )
    ORDER BY ds.created_at DESC
    LIMIT 1;
    
    RETURN v_driver_id;
END;
$$ LANGUAGE plpgsql;

-- Update the trigger function with corrected logic
CREATE OR REPLACE FUNCTION auto_populate_driver_from_shift()
RETURNS TRIGGER AS $$
DECLARE
    v_current_driver_id INTEGER;
BEGIN
    -- Only auto-populate if driver_id is NULL
    IF NEW.driver_id IS NULL THEN
        -- Get current active driver for the truck
        SELECT ds.driver_id INTO v_current_driver_id
        FROM driver_shifts ds
        WHERE ds.truck_id = NEW.truck_id
          AND ds.status = 'active'
          AND (
            -- Day shift or night shift that doesn't cross midnight
            (ds.shift_date = CURRENT_DATE AND CURRENT_TIME BETWEEN ds.start_time AND ds.end_time)
            OR
            -- Night shift that crosses midnight - started yesterday, still active today
            (ds.shift_type = 'night' AND ds.end_time < ds.start_time AND 
             ds.shift_date = CURRENT_DATE - 1 AND CURRENT_TIME <= ds.end_time)
            OR
            -- Night shift that crosses midnight - started today
            (ds.shift_type = 'night' AND ds.end_time < ds.start_time AND 
             ds.shift_date = CURRENT_DATE AND CURRENT_TIME >= ds.start_time)
          )
        ORDER BY ds.created_at DESC
        LIMIT 1;
        
        -- If we found an active driver, use it
        IF v_current_driver_id IS NOT NULL THEN
            NEW.driver_id := v_current_driver_id;
            
            -- Add note about auto-assignment
            IF NEW.notes IS NULL OR NEW.notes = '' THEN
                NEW.notes := '[Auto-assigned driver from active shift]';
            ELSE
                NEW.notes := NEW.notes || ' [Auto-assigned driver from active shift]';
            END IF;
        ELSE
            -- No active driver found, leave driver_id as NULL
            -- Add note about missing driver
            IF NEW.notes IS NULL OR NEW.notes = '' THEN
                NEW.notes := '[No active driver found - manual assignment required]';
            ELSE
                NEW.notes := NEW.notes || ' [No active driver found - manual assignment required]';
            END IF;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Success message
DO $$ 
BEGIN 
    RAISE NOTICE 'Migration 022 completed successfully: Fixed shift date filtering for night shifts';
    RAISE NOTICE '- Updated v_assignments_with_current_drivers view with corrected date logic';
    RAISE NOTICE '- Updated get_current_active_driver function with corrected date logic';
    RAISE NOTICE '- Updated auto_populate_driver_from_shift trigger with corrected date logic';
    RAISE NOTICE '- Night shifts crossing midnight now properly detected';
END $$;
