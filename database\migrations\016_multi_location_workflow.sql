-- ============================================================================
-- Migration 016: Multi-Location Trip Workflow Support
-- Purpose: Add support for A→B→C extensions, C→B→C cycles, and dynamic routes
-- Date: 2025-07-03
-- ============================================================================

-- Add workflow tracking columns to trip_logs table
ALTER TABLE trip_logs ADD COLUMN IF NOT EXISTS location_sequence JSONB;
ALTER TABLE trip_logs ADD COLUMN IF NOT EXISTS is_extended_trip BOOLEAN DEFAULT FALSE;
ALTER TABLE trip_logs ADD COLUMN IF NOT EXISTS workflow_type VARCHAR(50) DEFAULT 'standard';
ALTER TABLE trip_logs ADD COLUMN IF NOT EXISTS baseline_trip_id INTEGER;
ALTER TABLE trip_logs ADD COLUMN IF NOT EXISTS cycle_number INTEGER DEFAULT 1;

-- Add foreign key constraint for baseline trip relationship (if not exists)
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints
    WHERE constraint_name = 'fk_baseline_trip'
    AND table_name = 'trip_logs'
  ) THEN
    ALTER TABLE trip_logs ADD CONSTRAINT fk_baseline_trip
      FOREIGN KEY (baseline_trip_id) REFERENCES trip_logs(id) ON DELETE SET NULL;
  END IF;
END $$;

-- Add check constraint for workflow types (if not exists)
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints
    WHERE constraint_name = 'chk_workflow_type'
    AND table_name = 'trip_logs'
  ) THEN
    ALTER TABLE trip_logs ADD CONSTRAINT chk_workflow_type
      CHECK (workflow_type IN ('standard', 'extended', 'cycle', 'dynamic'));
  END IF;
END $$;

-- Add check constraint for cycle numbers (if not exists)
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints
    WHERE constraint_name = 'chk_cycle_number'
    AND table_name = 'trip_logs'
  ) THEN
    ALTER TABLE trip_logs ADD CONSTRAINT chk_cycle_number
      CHECK (cycle_number >= 1);
  END IF;
END $$;

-- Performance indexes for workflow queries
CREATE INDEX IF NOT EXISTS idx_workflow_tracking 
  ON trip_logs (workflow_type, is_extended_trip, cycle_number);

CREATE INDEX IF NOT EXISTS idx_location_sequence 
  ON trip_logs USING GIN (location_sequence);

CREATE INDEX IF NOT EXISTS idx_baseline_trip 
  ON trip_logs (baseline_trip_id) WHERE baseline_trip_id IS NOT NULL;

-- Update existing standard trips with location sequence
UPDATE trip_logs SET
  location_sequence = subquery.location_sequence,
  workflow_type = 'standard'
FROM (
  SELECT
    tl.id,
    jsonb_build_array(
      jsonb_build_object(
        'name', COALESCE(ll.name, 'Unknown Loading'),
        'type', 'loading',
        'confirmed', CASE WHEN tl.loading_start_time IS NOT NULL THEN true ELSE false END,
        'location_id', COALESCE(tl.actual_loading_location_id, a.loading_location_id)
      ),
      jsonb_build_object(
        'name', COALESCE(ul.name, 'Unknown Unloading'),
        'type', 'unloading',
        'confirmed', CASE WHEN tl.unloading_start_time IS NOT NULL THEN true ELSE false END,
        'location_id', COALESCE(tl.actual_unloading_location_id, a.unloading_location_id)
      )
    ) as location_sequence
  FROM trip_logs tl
  JOIN assignments a ON tl.assignment_id = a.id
  LEFT JOIN locations ll ON COALESCE(tl.actual_loading_location_id, a.loading_location_id) = ll.id
  LEFT JOIN locations ul ON COALESCE(tl.actual_unloading_location_id, a.unloading_location_id) = ul.id
  WHERE tl.location_sequence IS NULL
) subquery
WHERE trip_logs.id = subquery.id;

-- Add comment to document the schema changes
COMMENT ON COLUMN trip_logs.location_sequence IS 'JSONB array storing complete route sequence with confirmation status';
COMMENT ON COLUMN trip_logs.is_extended_trip IS 'Boolean flag indicating if this trip is part of an extended workflow';
COMMENT ON COLUMN trip_logs.workflow_type IS 'Type of workflow: standard, extended, cycle, or dynamic';
COMMENT ON COLUMN trip_logs.baseline_trip_id IS 'Reference to original A→B trip for extended workflows';
COMMENT ON COLUMN trip_logs.cycle_number IS 'Sequential number for cycle trips (1-based)';

-- Create a view for workflow analytics
CREATE OR REPLACE VIEW v_workflow_analytics AS
SELECT 
  workflow_type,
  COUNT(*) as total_trips,
  COUNT(CASE WHEN status = 'trip_completed' THEN 1 END) as completed_trips,
  AVG(total_duration_minutes) as avg_duration_minutes,
  AVG(cycle_number) as avg_cycle_number
FROM trip_logs 
WHERE workflow_type IS NOT NULL
GROUP BY workflow_type;

COMMENT ON VIEW v_workflow_analytics IS 'Analytics view for multi-location workflow performance metrics';

-- Migration completed successfully
-- Note: Migration tracking is handled by run-migration.js script
