# SSL Certificate Configuration

This directory contains SSL certificates for the Hauling QR Trip System.

## Directory Structure

```
server/ssl/
├── README.md                 # This file
├── generate-dev-certs.js     # Development certificate generation script
├── dev/                      # Development certificates (self-signed)
│   ├── server.key           # Development private key
│   ├── server.crt           # Development certificate
│   └── server.csr           # Certificate signing request
├── production/               # Production certificates (from CA)
│   ├── server.key           # Production private key
│   ├── server.crt           # Production certificate
│   ├── intermediate.crt     # Intermediate certificate (if needed)
│   └── fullchain.crt        # Full certificate chain
└── .gitignore               # Exclude sensitive files from git

## Certificate Requirements

### Development Certificates
- Self-signed certificates for local testing
- Subject Alternative Names (SAN) for:
  - localhost
  - 127.0.0.1
  - ************** (local network IP)
- Valid for 365 days

### Production Certificates
- Certificates from trusted Certificate Authority
- Support for actual production domain
- Minimum 2048-bit RSA or 256-bit ECDSA
- TLS 1.2+ compatibility

## Environment Variables

Configure these in server/.env:

```env
# SSL Configuration
SSL_CERT_PATH=./ssl/dev/server.crt
SSL_KEY_PATH=./ssl/dev/server.key
SSL_CA_PATH=./ssl/production/intermediate.crt
HTTPS_PORT=5443
ENABLE_HTTPS=true
```

## Security Notes

1. **Never commit private keys to version control**
2. **Use proper file permissions (600) for private keys**
3. **Rotate certificates before expiration**
4. **Use strong passphrases for production keys**
5. **Monitor certificate expiration dates**

## Certificate Generation

### Development (Self-Signed)
```bash
cd server
node ssl/generate-dev-certs.js
```

### Production (Let's Encrypt)
```bash
# Using certbot
certbot certonly --standalone -d yourdomain.com
cp /etc/letsencrypt/live/yourdomain.com/privkey.pem ssl/production/server.key
cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem ssl/production/fullchain.crt
```

### Production (Commercial CA)
1. Generate CSR: `openssl req -new -key server.key -out server.csr`
2. Submit CSR to Certificate Authority
3. Download and install certificates in production/ directory

## Testing

### Development Testing
```bash
# Test HTTPS access
curl -k https://localhost:5443/api/health
curl -k https://**************:5443/api/health

# Test WebSocket WSS
wscat -c wss://localhost:5443 --no-check
```

### Mobile Device Testing
1. Install development certificate on mobile device
2. Access https://**************:5443 from mobile browser
3. Test QR scanner camera access functionality

## Troubleshooting

### Common Issues
1. **Certificate not trusted**: Install CA certificate on client devices
2. **SAN mismatch**: Ensure certificate includes all required domains/IPs
3. **Permission denied**: Check file permissions on certificate files
4. **Port conflicts**: Ensure HTTPS port is not in use by other services

### Certificate Validation
```bash
# Check certificate details
openssl x509 -in ssl/dev/server.crt -text -noout

# Verify certificate chain
openssl verify -CAfile ssl/production/intermediate.crt ssl/production/server.crt

# Test SSL connection
openssl s_client -connect localhost:5443 -servername localhost
```
