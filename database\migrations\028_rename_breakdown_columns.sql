-- Migration: Rename breakdown columns to stopped columns
-- Description: Renames all breakdown-related columns to stopped terminology for consistency
-- Version: 028
-- Date: 2025-07-09

-- Rename columns in trip_logs table
DO $$ 
BEGIN
    -- Check if breakdown_reported_at exists and stopped_reported_at doesn't
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'trip_logs' 
        AND column_name = 'breakdown_reported_at'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'trip_logs' 
        AND column_name = 'stopped_reported_at'
    ) THEN
        ALTER TABLE trip_logs RENAME COLUMN breakdown_reported_at TO stopped_reported_at;
        RAISE NOTICE 'Renamed breakdown_reported_at to stopped_reported_at';
    ELSE
        RAISE NOTICE 'Column breakdown_reported_at already renamed or stopped_reported_at already exists';
    END IF;

    -- Check if breakdown_reason exists and stopped_reason doesn't
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'trip_logs' 
        AND column_name = 'breakdown_reason'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'trip_logs' 
        AND column_name = 'stopped_reason'
    ) THEN
        ALTER TABLE trip_logs RENAME COLUMN breakdown_reason TO stopped_reason;
        RAISE NOTICE 'Renamed breakdown_reason to stopped_reason';
    ELSE
        RAISE NOTICE 'Column breakdown_reason already renamed or stopped_reason already exists';
    END IF;

    -- Check if breakdown_resolved_at exists and stopped_resolved_at doesn't
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'trip_logs' 
        AND column_name = 'breakdown_resolved_at'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'trip_logs' 
        AND column_name = 'stopped_resolved_at'
    ) THEN
        ALTER TABLE trip_logs RENAME COLUMN breakdown_resolved_at TO stopped_resolved_at;
        RAISE NOTICE 'Renamed breakdown_resolved_at to stopped_resolved_at';
    ELSE
        RAISE NOTICE 'Column breakdown_resolved_at already renamed or stopped_resolved_at already exists';
    END IF;

    -- Check if breakdown_resolved_by exists and stopped_resolved_by doesn't
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'trip_logs' 
        AND column_name = 'breakdown_resolved_by'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'trip_logs' 
        AND column_name = 'stopped_resolved_by'
    ) THEN
        ALTER TABLE trip_logs RENAME COLUMN breakdown_resolved_by TO stopped_resolved_by;
        RAISE NOTICE 'Renamed breakdown_resolved_by to stopped_resolved_by';
    ELSE
        RAISE NOTICE 'Column breakdown_resolved_by already renamed or stopped_resolved_by already exists';
    END IF;
END $$;

-- Update any indexes that reference the old column names
DO $$
DECLARE
    index_record RECORD;
BEGIN
    -- Find indexes that reference the old column names
    FOR index_record IN 
        SELECT indexname, indexdef 
        FROM pg_indexes 
        WHERE tablename = 'trip_logs' 
        AND (indexdef LIKE '%breakdown_reported_at%' 
             OR indexdef LIKE '%breakdown_reason%' 
             OR indexdef LIKE '%breakdown_resolved_at%' 
             OR indexdef LIKE '%breakdown_resolved_by%')
    LOOP
        -- Drop the old index
        EXECUTE 'DROP INDEX IF EXISTS ' || index_record.indexname;
        RAISE NOTICE 'Dropped index: %', index_record.indexname;
    END LOOP;
END $$;

-- Create new indexes with stopped terminology if needed
CREATE INDEX IF NOT EXISTS idx_trip_logs_stopped_reported_at ON trip_logs(stopped_reported_at);
CREATE INDEX IF NOT EXISTS idx_trip_logs_stopped_resolved_at ON trip_logs(stopped_resolved_at);
CREATE INDEX IF NOT EXISTS idx_trip_logs_stopped_status ON trip_logs(status) WHERE status = 'stopped';

-- Add comments for documentation
COMMENT ON COLUMN trip_logs.stopped_reported_at IS 'Timestamp when the trip was stopped/reported as having issues';
COMMENT ON COLUMN trip_logs.stopped_reason IS 'Reason why the trip was stopped (mechanical issue, accident, etc.)';
COMMENT ON COLUMN trip_logs.stopped_resolved_at IS 'Timestamp when the stopped trip was resolved';
COMMENT ON COLUMN trip_logs.stopped_resolved_by IS 'User ID who resolved the stopped trip';

-- Verify the migration
DO $$
BEGIN
    -- Check if all new columns exist
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'trip_logs' 
        AND column_name IN ('stopped_reported_at', 'stopped_reason', 'stopped_resolved_at', 'stopped_resolved_by')
        GROUP BY table_name
        HAVING COUNT(*) = 4
    ) THEN
        RAISE NOTICE 'Migration verification: All stopped columns successfully created';
    ELSE
        RAISE WARNING 'Migration verification: Some stopped columns may be missing';
    END IF;
    
    -- Check if old columns are gone
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'trip_logs' 
        AND column_name IN ('breakdown_reported_at', 'breakdown_reason', 'breakdown_resolved_at', 'breakdown_resolved_by')
    ) THEN
        RAISE NOTICE 'Migration verification: All breakdown columns successfully renamed';
    ELSE
        RAISE WARNING 'Migration verification: Some breakdown columns may still exist';
    END IF;
END $$;
