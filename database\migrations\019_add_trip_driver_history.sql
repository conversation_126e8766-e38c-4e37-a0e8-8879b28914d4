-- ============================================================================
-- Migration 019: Add Historical Driver Tracking to trip_logs
-- Purpose: Store the actual driver who performed each trip at execution time
-- Date: 2025-01-09
-- ============================================================================

-- Add historical driver fields to trip_logs table (with IF NOT EXISTS handling)
DO $$ BEGIN
    ALTER TABLE trip_logs ADD COLUMN performed_by_driver_id INTEGER REFERENCES drivers(id);
EXCEPTION
    WHEN duplicate_column THEN null;
END $$;

DO $$ BEGIN
    ALTER TABLE trip_logs ADD COLUMN performed_by_driver_name VARCHAR(100);
EXCEPTION
    WHEN duplicate_column THEN null;
END $$;

DO $$ BEGIN
    ALTER TABLE trip_logs ADD COLUMN performed_by_employee_id VARCHAR(20);
EXCEPTION
    WHEN duplicate_column THEN null;
END $$;

DO $$ BEGIN
    ALTER TABLE trip_logs ADD COLUMN performed_by_shift_id INTEGER REFERENCES driver_shifts(id);
EXCEPTION
    WHEN duplicate_column THEN null;
END $$;

DO $$ BEGIN
    ALTER TABLE trip_logs ADD COLUMN performed_by_shift_type shift_type;
EXCEPTION
    WHEN duplicate_column THEN null;
END $$;

-- Add indexes for performance optimization (with IF NOT EXISTS handling)
DO $$ BEGIN
    CREATE INDEX idx_trip_logs_performed_by_driver ON trip_logs(performed_by_driver_id);
EXCEPTION
    WHEN duplicate_table THEN null;
END $$;

DO $$ BEGIN
    CREATE INDEX idx_trip_logs_performed_by_shift ON trip_logs(performed_by_shift_id);
EXCEPTION
    WHEN duplicate_table THEN null;
END $$;

DO $$ BEGIN
    CREATE INDEX idx_trip_logs_performed_by_employee ON trip_logs(performed_by_employee_id);
EXCEPTION
    WHEN duplicate_table THEN null;
END $$;

-- Add comments for documentation
COMMENT ON COLUMN trip_logs.performed_by_driver_id IS 'ID of driver who actually performed this trip';
COMMENT ON COLUMN trip_logs.performed_by_driver_name IS 'Name of driver who performed this trip (for historical accuracy)';
COMMENT ON COLUMN trip_logs.performed_by_employee_id IS 'Employee ID of driver who performed this trip';
COMMENT ON COLUMN trip_logs.performed_by_shift_id IS 'Shift ID during which this trip was performed';
COMMENT ON COLUMN trip_logs.performed_by_shift_type IS 'Type of shift (day/night) during trip execution';

-- ============================================================================
-- Populate existing trips with historical driver data
-- ============================================================================

-- Update existing trips with assignment driver information (where available)
UPDATE trip_logs
SET performed_by_driver_id = a.driver_id,
    performed_by_driver_name = d.full_name,
    performed_by_employee_id = d.employee_id
FROM assignments a, drivers d
WHERE trip_logs.assignment_id = a.id
  AND a.driver_id = d.id
  AND a.driver_id IS NOT NULL
  AND trip_logs.performed_by_driver_id IS NULL;

-- For trips with shift data available, try to populate shift information
-- This attempts to match trips with shifts based on truck and timing
UPDATE trip_logs
SET performed_by_shift_id = ds.id,
    performed_by_shift_type = ds.shift_type
FROM assignments a, driver_shifts ds
WHERE trip_logs.assignment_id = a.id
  AND ds.truck_id = a.truck_id
  AND ds.driver_id = trip_logs.performed_by_driver_id
  AND ds.shift_date = trip_logs.loading_start_time::date
  AND trip_logs.loading_start_time::time BETWEEN ds.start_time AND
      CASE
          WHEN ds.end_time < ds.start_time
          THEN ds.end_time + interval '24 hours'
          ELSE ds.end_time
      END
  AND trip_logs.performed_by_driver_id IS NOT NULL
  AND trip_logs.performed_by_shift_id IS NULL
  AND trip_logs.loading_start_time IS NOT NULL;

-- ============================================================================
-- Create helper function to capture active driver for a truck
-- ============================================================================

CREATE OR REPLACE FUNCTION capture_active_driver_for_trip(
    p_truck_id INTEGER,
    p_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
RETURNS TABLE (
    driver_id INTEGER,
    driver_name VARCHAR(100),
    employee_id VARCHAR(20),
    shift_id INTEGER,
    shift_type shift_type
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        d.id as driver_id,
        d.full_name as driver_name,
        d.employee_id,
        ds.id as shift_id,
        ds.shift_type
    FROM driver_shifts ds
    JOIN drivers d ON ds.driver_id = d.id
    WHERE ds.truck_id = p_truck_id
        AND ds.status = 'active'
        AND ds.shift_date = p_timestamp::date
        AND p_timestamp::time BETWEEN ds.start_time AND 
            CASE 
                WHEN ds.end_time < ds.start_time 
                THEN ds.end_time + interval '24 hours'
                ELSE ds.end_time 
            END
    ORDER BY ds.created_at DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Add comment for the helper function
COMMENT ON FUNCTION capture_active_driver_for_trip(INTEGER, TIMESTAMP) IS 'Captures the active driver for a truck at a specific timestamp for trip history';

-- ============================================================================
-- Update trip summary view to include historical driver information
-- ============================================================================

-- Drop existing view if it exists
DROP VIEW IF EXISTS v_trip_summary;

-- Recreate view with historical driver information
CREATE VIEW v_trip_summary AS
SELECT
    tl.id,
    a.assignment_code,
    a.assigned_date,
    dt.truck_number,
    
    -- Historical driver information (who actually performed the trip)
    COALESCE(tl.performed_by_driver_name, d.full_name) as driver_name,
    COALESCE(tl.performed_by_employee_id, d.employee_id) as employee_id,
    tl.performed_by_shift_type,
    
    -- Trip details
    tl.trip_number,
    tl.status,
    tl.loading_start_time,
    tl.trip_completed_time,
    tl.total_duration_minutes,
    tl.loading_duration_minutes,
    tl.travel_duration_minutes,
    tl.unloading_duration_minutes,
    tl.is_exception,
    tl.exception_reason,
    
    -- Location information
    ll.name as loading_location,
    ul.name as unloading_location,
    COALESCE(al.name, ll.name) as actual_loading_location,
    COALESCE(aul.name, ul.name) as actual_unloading_location,
    
    -- Driver data source indicator
    CASE 
        WHEN tl.performed_by_driver_id IS NOT NULL THEN 'historical'
        WHEN d.id IS NOT NULL THEN 'assignment'
        ELSE 'none'
    END as driver_data_source
    
FROM trip_logs tl
JOIN assignments a ON tl.assignment_id = a.id
JOIN dump_trucks dt ON a.truck_id = dt.id
LEFT JOIN drivers d ON a.driver_id = d.id
LEFT JOIN locations ll ON a.loading_location_id = ll.id
LEFT JOIN locations ul ON a.unloading_location_id = ul.id
LEFT JOIN locations al ON tl.actual_loading_location_id = al.id
LEFT JOIN locations aul ON tl.actual_unloading_location_id = aul.id;

-- Add comment for the updated view
COMMENT ON VIEW v_trip_summary IS 'Trip summary with historical driver information prioritized over assignment drivers';

-- ============================================================================
-- Create trigger to automatically capture driver info on trip creation
-- ============================================================================

CREATE OR REPLACE FUNCTION auto_capture_trip_driver()
RETURNS TRIGGER AS $$
DECLARE
    driver_info RECORD;
    truck_id INTEGER;
BEGIN
    -- Get truck_id from assignment
    SELECT a.truck_id INTO truck_id
    FROM assignments a
    WHERE a.id = NEW.assignment_id;
    
    -- Only capture driver info if not already set and trip is starting
    IF NEW.performed_by_driver_id IS NULL AND NEW.loading_start_time IS NOT NULL THEN
        -- Capture active driver at the time of loading start
        SELECT * INTO driver_info
        FROM capture_active_driver_for_trip(truck_id, NEW.loading_start_time);
        
        IF FOUND THEN
            NEW.performed_by_driver_id := driver_info.driver_id;
            NEW.performed_by_driver_name := driver_info.driver_name;
            NEW.performed_by_employee_id := driver_info.employee_id;
            NEW.performed_by_shift_id := driver_info.shift_id;
            NEW.performed_by_shift_type := driver_info.shift_type;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic driver capture (drop if exists first)
DROP TRIGGER IF EXISTS trigger_auto_capture_trip_driver ON trip_logs;
CREATE TRIGGER trigger_auto_capture_trip_driver
    BEFORE INSERT OR UPDATE ON trip_logs
    FOR EACH ROW
    EXECUTE FUNCTION auto_capture_trip_driver();

-- Add comment for the trigger
COMMENT ON TRIGGER trigger_auto_capture_trip_driver ON trip_logs IS 'Automatically captures active driver information when trip starts';

-- ============================================================================
-- Migration completion (handled automatically by run-migration.js)
-- ============================================================================
-- Verification queries (for testing)
-- ============================================================================

-- Check if columns were added successfully
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'trip_logs' 
  AND column_name LIKE 'performed_by_%'
ORDER BY column_name;

-- Check if indexes were created
SELECT indexname, indexdef 
FROM pg_indexes 
WHERE tablename = 'trip_logs' 
  AND indexname LIKE 'idx_trip_logs_performed_by_%';

-- Count trips with historical driver data
SELECT 
    COUNT(*) as total_trips,
    COUNT(performed_by_driver_id) as trips_with_historical_driver,
    ROUND((COUNT(performed_by_driver_id)::NUMERIC / COUNT(*) * 100), 2) as percentage_with_driver
FROM trip_logs;

-- Sample of updated trip data
SELECT 
    id, trip_number, status,
    performed_by_driver_name,
    performed_by_employee_id,
    performed_by_shift_type,
    loading_start_time
FROM trip_logs 
WHERE performed_by_driver_id IS NOT NULL
ORDER BY id DESC
LIMIT 5;
