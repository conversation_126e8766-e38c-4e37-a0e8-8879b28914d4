-- ============================================================================
-- Migration: Add auto_created Column to Assignments Table
-- Purpose: Add missing auto_created column to satisfy constraint requirements
-- Date: 2025-07-10
-- ============================================================================

-- Check if auto_created column exists, if not add it
DO $$
BEGIN
    -- Check if column exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'assignments' 
        AND column_name = 'auto_created'
    ) THEN
        -- Add the auto_created column
        ALTER TABLE assignments 
        ADD COLUMN auto_created BOOLEAN DEFAULT false;
        
        RAISE NOTICE 'Added auto_created column to assignments table';
    ELSE
        RAISE NOTICE 'auto_created column already exists in assignments table';
    END IF;
END $$;

-- Update existing assignments to set auto_created based on notes content
UPDATE assignments 
SET auto_created = true
WHERE auto_created = false 
  AND (
    notes LIKE '%Auto-assigned%' OR 
    notes LIKE '%auto-assignment%' OR
    notes LIKE '%No active driver%' OR
    notes LIKE '%manual assignment required%' OR
    assignment_code LIKE '%AUTO%' OR
    assignment_code LIKE '%DYN-%'
  );

-- Add index for performance
CREATE INDEX IF NOT EXISTS idx_assignments_auto_created 
ON assignments (auto_created) 
WHERE auto_created = true;

-- Success message
DO $$ 
BEGIN 
    RAISE NOTICE 'Migration 028 completed successfully: auto_created column added';
    RAISE NOTICE '- Added auto_created column with default false';
    RAISE NOTICE '- Updated existing auto-assignments based on notes and assignment codes';
    RAISE NOTICE '- Added performance index for auto_created column';
END $$;
