-- Migration: Add breakdown status and management
-- Description: Adds breakdown status to trip_status enum and related columns for breakdown management
-- Version: 007
-- Date: 2024-12-19

-- Add 'breakdown' to the trip_status enum
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = 'breakdown' 
        AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'trip_status')
    ) THEN
        ALTER TYPE trip_status ADD VALUE 'breakdown';
    END IF;
END $$;

-- Add breakdown-related columns to trip_logs for better tracking
DO $$
BEGIN
    -- Add breakdown_reported_at column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'trip_logs' AND column_name = 'breakdown_reported_at'
    ) THEN
        ALTER TABLE trip_logs ADD COLUMN breakdown_reported_at TIMESTAMP;
    END IF;

    -- Add breakdown_reason column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'trip_logs' AND column_name = 'breakdown_reason'
    ) THEN
        ALTER TABLE trip_logs ADD COLUMN breakdown_reason TEXT;
    END IF;

    -- Add breakdown_resolved_at column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'trip_logs' AND column_name = 'breakdown_resolved_at'
    ) THEN
        ALTER TABLE trip_logs ADD COLUMN breakdown_resolved_at TIMESTAMP;
    END IF;

    -- Add breakdown_resolved_by column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'trip_logs' AND column_name = 'breakdown_resolved_by'
    ) THEN
        ALTER TABLE trip_logs ADD COLUMN breakdown_resolved_by INTEGER REFERENCES users(id);
    END IF;

    -- Add previous_status column to track status before breakdown
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'trip_logs' AND column_name = 'previous_status'
    ) THEN
        ALTER TABLE trip_logs ADD COLUMN previous_status trip_status;
    END IF;
END $$;

-- Create indexes for breakdown status queries (only if they don't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE indexname = 'idx_trip_logs_breakdown'
    ) THEN
        CREATE INDEX idx_trip_logs_breakdown ON trip_logs(status) WHERE status = 'breakdown';
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE indexname = 'idx_trip_logs_breakdown_date'
    ) THEN
        CREATE INDEX idx_trip_logs_breakdown_date ON trip_logs(breakdown_reported_at) WHERE breakdown_reported_at IS NOT NULL;
    END IF;
END $$;

-- Add comments for documentation
COMMENT ON COLUMN trip_logs.breakdown_reported_at IS 'Timestamp when breakdown was reported';
COMMENT ON COLUMN trip_logs.breakdown_reason IS 'Reason for breakdown (mechanical, accident, etc.)';
COMMENT ON COLUMN trip_logs.breakdown_resolved_at IS 'Timestamp when breakdown was resolved';
COMMENT ON COLUMN trip_logs.breakdown_resolved_by IS 'User who resolved the breakdown';
COMMENT ON COLUMN trip_logs.previous_status IS 'Status before breakdown for proper restoration';

-- Update the view to include breakdown information
DROP VIEW IF EXISTS v_trip_summary;
CREATE VIEW v_trip_summary AS
SELECT
    tl.id,
    a.assignment_code,
    a.assigned_date,
    dt.truck_number,
    d.full_name as driver_name,
    tl.trip_number,
    tl.status,
    tl.previous_status,
    tl.loading_start_time,
    tl.trip_completed_time,
    tl.total_duration_minutes,
    tl.loading_duration_minutes,
    tl.travel_duration_minutes,
    tl.unloading_duration_minutes,
    tl.is_exception,
    tl.exception_reason,
    tl.breakdown_reported_at,
    tl.breakdown_reason,
    tl.breakdown_resolved_at,
    ll.name as loading_location,
    ul.name as unloading_location,
    COALESCE(al.name, ll.name) as actual_loading_location,
    COALESCE(aul.name, ul.name) as actual_unloading_location
FROM trip_logs tl
JOIN assignments a ON tl.assignment_id = a.id
JOIN dump_trucks dt ON a.truck_id = dt.id
JOIN drivers d ON a.driver_id = d.id
LEFT JOIN locations ll ON a.loading_location_id = ll.id
LEFT JOIN locations ul ON a.unloading_location_id = ul.id
LEFT JOIN locations al ON tl.actual_loading_location_id = al.id
LEFT JOIN locations aul ON tl.actual_unloading_location_id = aul.id
ORDER BY tl.created_at DESC;

-- Migration completed successfully
