#!/usr/bin/env node
/**
 * HTTP Configuration Test Script
 * Verifies that HTTP mode is properly configured
 */

const { loadConfig } = require('../config-loader');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

console.log('🔍 Testing HTTP Configuration...\n');

// Ensure HTTP mode is enabled
const envPath = path.join(__dirname, '..', '.env');
let envContent = fs.readFileSync(envPath, 'utf8');

// Update configuration for HTTP testing
envContent = envContent.replace(/^NODE_ENV=.*/m, 'NODE_ENV=development');
envContent = envContent.replace(/^ENABLE_HTTPS=.*/m, 'ENABLE_HTTPS=false');

// Write updated .env file
fs.writeFileSync(envPath, envContent);

// Load configuration
const config = loadConfig();

console.log('📋 Configuration Summary:');
console.log('='.repeat(40));
console.log(`🌍 Environment: ${config.NODE_ENV}`);
console.log(`🔒 HTTPS Enabled: ${config.ENABLE_HTTPS}`);
console.log(`🌐 IP Address: ${config.IP_ADDRESS}`);
console.log(`🚀 Frontend Port: ${config.CLIENT_PORT}`);
console.log(`📡 Backend HTTP Port: ${config.BACKEND_HTTP_PORT}`);
console.log(`📡 Backend HTTPS Port: ${config.HTTPS_PORT}`);
console.log(`🔗 API Base URL: ${config.API_BASE_URL}`);
console.log(`🔌 WebSocket URL: ${config.WS_URL}`);
console.log('='.repeat(40));

// Test URLs
const testUrls = [
  `http://localhost:${config.BACKEND_HTTP_PORT}/health`,
  `http://${config.IP_ADDRESS}:${config.BACKEND_HTTP_PORT}/health`,
  `http://localhost:${config.CLIENT_PORT}`,
  `http://${config.IP_ADDRESS}:${config.CLIENT_PORT}`
];

console.log('\n🧪 Expected URLs for HTTP Mode:');
testUrls.forEach((url, index) => {
  console.log(`${index + 1}. ${url}`);
});

console.log('\n✅ HTTP Configuration Test Complete!');
console.log('\n📝 Next Steps:');
console.log('1. Run: npm run dev');
console.log('2. Backend should start on port 5000');
console.log('3. Frontend should start on port 3000');
console.log('4. WebSocket should use ws:// protocol');
console.log('5. All connections should be HTTP-only');

console.log('\n🔧 Troubleshooting:');
console.log('- If backend starts on wrong port, check server.js port configuration');
console.log('- If WebSocket fails, check network-utils.js protocol detection');
console.log('- If CORS errors occur, check unified-config.js CORS origins');
