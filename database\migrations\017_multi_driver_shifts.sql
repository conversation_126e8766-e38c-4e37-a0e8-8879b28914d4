-- ============================================================================
-- Multi-Driver Shifts System Migration
-- Enables multiple drivers per truck with shift-based assignments
-- Preserves 4-phase workflow integrity
-- ============================================================================

-- Create shift types enum (if not exists)
DO $$ BEGIN
    CREATE TYPE shift_type AS ENUM ('day', 'night', 'custom');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create shift status enum (if not exists)
DO $$ BEGIN
    CREATE TYPE shift_status AS ENUM ('scheduled', 'active', 'completed', 'cancelled');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- ============================================================================
-- TABLE: driver_shifts
-- Purpose: Manage driver shift schedules per truck
-- ============================================================================
CREATE TABLE IF NOT EXISTS driver_shifts (
    id SERIAL PRIMARY KEY,
    truck_id INTEGER NOT NULL REFERENCES dump_trucks(id) ON DELETE CASCADE,
    driver_id INTEGER NOT NULL REFERENCES drivers(id) ON DELETE CASCADE,
    shift_type shift_type NOT NULL DEFAULT 'day',
    shift_date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    status shift_status NOT NULL DEFAULT 'scheduled',
    
    -- Handover tracking
    previous_shift_id INTEGER REFERENCES driver_shifts(id),
    handover_notes TEXT,
    handover_completed_at TIMESTAMP,
    
    -- Assignment integration
    assignment_id INTEGER REFERENCES assignments(id),
    auto_created BOOLEAN DEFAULT false,
    
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Note: Overlapping shift validation will be handled at application level
    -- PostgreSQL exclusion constraints with complex expressions can be challenging
    -- We'll use a unique constraint on truck_id, shift_date, start_time instead
    UNIQUE(truck_id, shift_date, start_time)
);

-- ============================================================================
-- TABLE: shift_handovers
-- Purpose: Track shift transitions and trip handovers
-- ============================================================================
CREATE TABLE IF NOT EXISTS shift_handovers (
    id SERIAL PRIMARY KEY,
    truck_id INTEGER NOT NULL REFERENCES dump_trucks(id) ON DELETE CASCADE,
    outgoing_shift_id INTEGER NOT NULL REFERENCES driver_shifts(id),
    incoming_shift_id INTEGER NOT NULL REFERENCES driver_shifts(id),
    
    -- Trip context during handover
    active_trip_id INTEGER REFERENCES trip_logs(id),
    trip_status_at_handover VARCHAR(50),
    location_at_handover INTEGER REFERENCES locations(id),
    
    -- Handover details
    handover_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    handover_notes TEXT,
    fuel_level DECIMAL(5,2),
    vehicle_condition TEXT,
    
    -- Approval tracking
    approved_by INTEGER REFERENCES users(id),
    approved_at TIMESTAMP,
    
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- ============================================================================
-- Enhanced assignments table for shift compatibility
-- ============================================================================

-- Add shift-aware columns to assignments
ALTER TABLE assignments 
ADD COLUMN IF NOT EXISTS shift_id INTEGER REFERENCES driver_shifts(id),
ADD COLUMN IF NOT EXISTS is_shift_assignment BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS shift_handover_id INTEGER REFERENCES shift_handovers(id);

-- ============================================================================
-- INDEXES for performance
-- ============================================================================

-- Driver shifts indexes (with IF NOT EXISTS handling)
DO $$ BEGIN
    CREATE INDEX idx_driver_shifts_truck_date ON driver_shifts(truck_id, shift_date);
EXCEPTION
    WHEN duplicate_table THEN null;
END $$;

DO $$ BEGIN
    CREATE INDEX idx_driver_shifts_driver_date ON driver_shifts(driver_id, shift_date);
EXCEPTION
    WHEN duplicate_table THEN null;
END $$;

DO $$ BEGIN
    CREATE INDEX idx_driver_shifts_status ON driver_shifts(status);
EXCEPTION
    WHEN duplicate_table THEN null;
END $$;

DO $$ BEGIN
    CREATE INDEX idx_driver_shifts_active ON driver_shifts(truck_id, status, shift_date)
        WHERE status = 'active';
EXCEPTION
    WHEN duplicate_table THEN null;
END $$;

-- Shift handovers indexes (with IF NOT EXISTS handling)
DO $$ BEGIN
    CREATE INDEX idx_shift_handovers_truck ON shift_handovers(truck_id);
EXCEPTION
    WHEN duplicate_table THEN null;
END $$;

DO $$ BEGIN
    CREATE INDEX idx_shift_handovers_time ON shift_handovers(handover_time);
EXCEPTION
    WHEN duplicate_table THEN null;
END $$;

DO $$ BEGIN
    CREATE INDEX idx_shift_handovers_trip ON shift_handovers(active_trip_id);
EXCEPTION
    WHEN duplicate_table THEN null;
END $$;

-- Enhanced assignments indexes (with IF NOT EXISTS handling)
DO $$ BEGIN
    CREATE INDEX idx_assignments_shift ON assignments(shift_id) WHERE shift_id IS NOT NULL;
EXCEPTION
    WHEN duplicate_table THEN null;
END $$;

DO $$ BEGIN
    CREATE INDEX idx_assignments_shift_truck ON assignments(truck_id, shift_id)
        WHERE is_shift_assignment = true;
EXCEPTION
    WHEN duplicate_table THEN null;
END $$;

-- ============================================================================
-- FUNCTIONS for shift management
-- ============================================================================

-- Function to get current active driver for a truck
CREATE OR REPLACE FUNCTION get_current_driver_for_truck(p_truck_id INTEGER)
RETURNS TABLE (
    driver_id INTEGER,
    driver_name VARCHAR(100),
    shift_id INTEGER,
    shift_type shift_type,
    shift_start TIME,
    shift_end TIME
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        d.id as driver_id,
        d.full_name as driver_name,
        ds.id as shift_id,
        ds.shift_type,
        ds.start_time as shift_start,
        ds.end_time as shift_end
    FROM driver_shifts ds
    JOIN drivers d ON ds.driver_id = d.id
    WHERE ds.truck_id = p_truck_id
        AND ds.status = 'active'
        AND ds.shift_date = CURRENT_DATE
        AND CURRENT_TIME BETWEEN ds.start_time AND 
            CASE 
                WHEN ds.end_time < ds.start_time 
                THEN ds.end_time + interval '24 hours'
                ELSE ds.end_time 
            END
    ORDER BY ds.created_at DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Function to create automatic shift assignment
CREATE OR REPLACE FUNCTION create_shift_assignment(
    p_truck_id INTEGER,
    p_shift_id INTEGER,
    p_loading_location_id INTEGER,
    p_unloading_location_id INTEGER
) RETURNS INTEGER AS $$
DECLARE
    v_assignment_id INTEGER;
    v_driver_id INTEGER;
    v_assignment_code VARCHAR(50);
BEGIN
    -- Get driver from shift
    SELECT driver_id INTO v_driver_id
    FROM driver_shifts
    WHERE id = p_shift_id;
    
    IF v_driver_id IS NULL THEN
        RAISE EXCEPTION 'Invalid shift ID: %', p_shift_id;
    END IF;
    
    -- Generate assignment code
    v_assignment_code := 'SHIFT-' || TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMMDD-HH24MISS') || '-' || p_shift_id;
    
    -- Create assignment
    INSERT INTO assignments (
        assignment_code, truck_id, driver_id,
        loading_location_id, unloading_location_id,
        shift_id, is_shift_assignment,
        assigned_date, status, priority,
        expected_loads_per_day, notes
    ) VALUES (
        v_assignment_code, p_truck_id, v_driver_id,
        p_loading_location_id, p_unloading_location_id,
        p_shift_id, true,
        CURRENT_DATE, 'assigned', 'normal',
        1, 'Auto-created for shift-based assignment'
    ) RETURNING id INTO v_assignment_id;
    
    -- Update shift with assignment reference
    UPDATE driver_shifts 
    SET assignment_id = v_assignment_id,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_shift_id;
    
    RETURN v_assignment_id;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- SAMPLE DATA for testing
-- ============================================================================

-- Insert sample shifts for existing trucks (day/night shifts)
INSERT INTO driver_shifts (truck_id, driver_id, shift_type, shift_date, start_time, end_time, status) VALUES
-- Truck DT-100 (ID: 1) - Day and Night shifts
(1, 1, 'day', CURRENT_DATE, '06:00:00', '18:00:00', 'active'),
(1, 2, 'night', CURRENT_DATE, '18:00:00', '06:00:00', 'scheduled'),

-- Truck DT-101 (ID: 2) - Day and Night shifts  
(2, 2, 'day', CURRENT_DATE, '06:00:00', '18:00:00', 'active'),
(2, 3, 'night', CURRENT_DATE, '18:00:00', '06:00:00', 'scheduled'),

-- Truck DT-102 (ID: 3) - Day and Night shifts
(3, 3, 'day', CURRENT_DATE, '06:00:00', '18:00:00', 'active'),
(3, 1, 'night', CURRENT_DATE, '18:00:00', '06:00:00', 'scheduled');

-- ============================================================================
-- TRIGGERS for automatic shift management
-- ============================================================================

-- Trigger to auto-activate shifts at start time
CREATE OR REPLACE FUNCTION auto_activate_shifts()
RETURNS TRIGGER AS $$
BEGIN
    -- Auto-activate shifts that should be starting now
    UPDATE driver_shifts 
    SET status = 'active', updated_at = CURRENT_TIMESTAMP
    WHERE status = 'scheduled'
        AND shift_date = CURRENT_DATE
        AND CURRENT_TIME >= start_time
        AND CURRENT_TIME < CASE 
            WHEN end_time < start_time 
            THEN end_time + interval '24 hours'
            ELSE end_time 
        END;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for shift activation (runs every minute)
-- Note: In production, this should be handled by a cron job or scheduler
CREATE OR REPLACE FUNCTION schedule_shift_activation()
RETURNS void AS $$
BEGIN
    PERFORM auto_activate_shifts();
END;
$$ LANGUAGE plpgsql;
