const request = require('supertest');
const { expect } = require('chai');
const app = require('../server');
const { query } = require('../config/database');

describe('Stopped Terminology Refactoring Tests', () => {
  let authToken;
  let testTruck;
  let testDriver;
  let testAssignment;
  let testTrip;

  before(async function() {
    this.timeout(30000);
    
    // Login to get auth token
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        username: 'admin',
        password: 'admin123'
      });
    
    authToken = loginResponse.body.token;
    
    // Create test truck
    const truckResponse = await request(app)
      .post('/api/trucks')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        truck_number: 'TEST-STOPPED-001',
        license_plate: 'TST001',
        status: 'active'
      });
    
    testTruck = truckResponse.body.data;
    
    // Create test driver
    const driverResponse = await request(app)
      .post('/api/drivers')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        full_name: 'Test Driver Stopped',
        employee_id: 'EMP-STOPPED-001',
        status: 'active'
      });
    
    testDriver = driverResponse.body.data;
    
    // Create test assignment
    const assignmentResponse = await request(app)
      .post('/api/assignments')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        truck_id: testTruck.id,
        driver_id: testDriver.id,
        loading_location_id: 1,
        unloading_location_id: 2,
        assigned_date: new Date().toISOString().split('T')[0]
      });
    
    testAssignment = assignmentResponse.body.data;
  });

  describe('1. API Endpoint Refactoring', () => {
    it('should have stopped-analytics endpoint instead of breakdown-analytics', async () => {
      const response = await request(app)
        .get('/api/analytics/stopped-analytics')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          start_date: '2025-01-01',
          end_date: '2025-01-31'
        });
      
      expect(response.status).to.equal(200);
      expect(response.body.success).to.be.true;
      expect(response.body.data).to.have.property('frequency');
      expect(response.body.data).to.have.property('locationPatterns');
      expect(response.body.data).to.have.property('trends');
      console.log('✅ Stopped analytics endpoint working correctly');
    });

    it('should have resolve-stopped endpoint instead of resolve-breakdown', async () => {
      // First create a trip and mark it as stopped
      const tripResponse = await request(app)
        .post('/api/trips')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          assignment_id: testAssignment.id,
          status: 'loading_start'
        });
      
      testTrip = tripResponse.body.data;
      
      // Mark trip as stopped
      const stopResponse = await request(app)
        .post(`/api/trips/${testTrip.id}/stop`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          stop_type: 'pause',
          reason: 'Test stop for refactoring validation'
        });
      
      expect(stopResponse.status).to.equal(200);
      expect(stopResponse.body.success).to.be.true;
      
      // Test resolve-stopped endpoint
      const resolveResponse = await request(app)
        .post(`/api/trips/${testTrip.id}/resolve-stopped`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          resolution_notes: 'Test resolution'
        });
      
      expect(resolveResponse.status).to.equal(200);
      expect(resolveResponse.body.success).to.be.true;
      expect(resolveResponse.body.message).to.include('Stop resolved');
      console.log('✅ Resolve-stopped endpoint working correctly');
    });
  });

  describe('2. Database Query Complete Replacement', () => {
    it('should only use stopped status in queries (no breakdown references)', async () => {
      // Create a trip with stopped status
      const stoppedTripQuery = `
        INSERT INTO trip_logs (assignment_id, trip_number, status, breakdown_reported_at, breakdown_reason)
        VALUES ($1, $2, 'stopped', CURRENT_TIMESTAMP, 'Test stopped trip')
        RETURNING *
      `;

      const stoppedTrip = await query(stoppedTripQuery, [testAssignment.id, 'TEST-STOPPED-001']);
      expect(stoppedTrip.rows).to.have.length(1);
      expect(stoppedTrip.rows[0].status).to.equal('stopped');

      // Test analytics query that should only include stopped (no breakdown)
      const analyticsQuery = `
        SELECT COUNT(*) as total_stopped
        FROM trip_logs
        WHERE status = 'stopped'
      `;

      const analyticsResult = await query(analyticsQuery);
      expect(parseInt(analyticsResult.rows[0].total_stopped)).to.be.at.least(1);
      console.log('✅ Database queries use only stopped status (complete replacement)');
    });

    it('should return stopped terminology in API responses', async () => {
      const response = await request(app)
        .get('/api/analytics/stopped-analytics')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          start_date: '2025-01-01',
          end_date: '2025-01-31'
        });
      
      expect(response.status).to.equal(200);
      
      // Check that response uses stopped terminology
      if (response.body.data.frequency && response.body.data.frequency.length > 0) {
        expect(response.body.data.frequency[0]).to.have.property('stoppedCount');
        expect(response.body.data.frequency[0]).to.not.have.property('breakdownCount');
      }
      
      if (response.body.data.locationPatterns && response.body.data.locationPatterns.length > 0) {
        expect(response.body.data.locationPatterns[0]).to.have.property('stoppedCount');
        expect(response.body.data.locationPatterns[0]).to.have.property('phaseWhenStopped');
      }
      
      if (response.body.data.trends && response.body.data.trends.length > 0) {
        expect(response.body.data.trends[0]).to.have.property('stoppedCount');
        expect(response.body.data.trends[0]).to.not.have.property('breakdownCount');
      }
      
      console.log('✅ API responses use stopped terminology correctly');
    });
  });

  describe('3. Frontend Component Integration', () => {
    it('should verify StoppedAnalytics component exists and BreakdownAnalytics is removed', async () => {
      const fs = require('fs');
      const path = require('path');
      
      // Check that StoppedAnalytics.js exists
      const stoppedAnalyticsPath = path.join(__dirname, '../client/src/pages/analytics-reports/components/TripPerformance/StoppedAnalytics.js');
      expect(fs.existsSync(stoppedAnalyticsPath)).to.be.true;
      
      // Check that BreakdownAnalytics.js is removed
      const breakdownAnalyticsPath = path.join(__dirname, '../client/src/pages/analytics-reports/components/TripPerformance/BreakdownAnalytics.js');
      expect(fs.existsSync(breakdownAnalyticsPath)).to.be.false;
      
      console.log('✅ Frontend components correctly refactored');
    });
  });

  describe('4. Complete Terminology Replacement', () => {
    it('should have zero breakdown references in application code', async () => {
      // Verify that application only uses stopped status
      const stoppedOnlyQuery = `
        SELECT COUNT(*) as stopped_count
        FROM trip_logs
        WHERE status = 'stopped'
      `;

      const stoppedResult = await query(stoppedOnlyQuery);
      const stoppedCount = parseInt(stoppedResult.rows[0].stopped_count);

      // Test that analytics endpoint only returns stopped terminology
      const response = await request(app)
        .get('/api/analytics/stopped-analytics')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          start_date: '2020-01-01',
          end_date: '2025-12-31'
        });

      expect(response.status).to.equal(200);
      expect(response.body.success).to.be.true;

      // Verify response structure uses only stopped terminology
      expect(response.body.data).to.have.property('frequency');
      expect(response.body.data).to.have.property('locationPatterns');
      expect(response.body.data).to.have.property('trends');

      // Verify no breakdown terminology in response
      const responseStr = JSON.stringify(response.body);
      expect(responseStr).to.not.include('breakdown');
      expect(responseStr).to.not.include('Breakdown');

      console.log('✅ Complete terminology replacement verified - zero breakdown references');
    });
  });

  after(async function() {
    this.timeout(10000);
    
    // Cleanup test data
    try {
      if (testTrip) {
        await query('DELETE FROM trip_logs WHERE id = $1', [testTrip.id]);
      }
      if (testAssignment) {
        await query('DELETE FROM assignments WHERE id = $1', [testAssignment.id]);
      }
      if (testDriver) {
        await query('DELETE FROM drivers WHERE id = $1', [testDriver.id]);
      }
      if (testTruck) {
        await query('DELETE FROM dump_trucks WHERE id = $1', [testTruck.id]);
      }
      
      // Clean up any test trips created during testing
      await query('DELETE FROM trip_logs WHERE trip_number LIKE $1', ['TEST-STOPPED-%']);
      
      console.log('✅ Test cleanup completed');
    } catch (error) {
      console.log('⚠️ Test cleanup warning:', error.message);
    }
  });
});
