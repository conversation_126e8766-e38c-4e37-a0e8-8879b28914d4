const http = require('http');

console.log('🔍 Debug Unified Date Range Approach\n');

function makeRequest(endpoint, options = {}) {
  return new Promise((resolve, reject) => {
    const requestOptions = {
      hostname: 'localhost',
      port: 5000,
      path: endpoint,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    };

    const req = http.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => { data += chunk; });
      res.on('end', () => {
        try {
          resolve({
            status: res.statusCode,
            data: JSON.parse(data)
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: data,
            parseError: error.message
          });
        }
      });
    });
    
    req.on('error', reject);
    if (options.body) req.write(JSON.stringify(options.body));
    req.end();
  });
}

async function debugUnifiedApproach() {
  console.log('🚀 Testing Unified Date Range Approach');
  
  try {
    // Authenticate
    const authResponse = await makeRequest('/api/auth/login', {
      method: 'POST',
      body: { username: 'admin', password: 'admin123' }
    });
    
    if (authResponse.status !== 200) {
      console.log('❌ Authentication failed');
      return;
    }
    
    const token = authResponse.data.token;
    console.log('✅ Authentication successful');
    
    const headers = { 'Authorization': `Bearer ${token}` };
    
    // Test 1: Create single-day shift with unified approach
    console.log('\n📋 Test 1: Creating single-day shift with unified approach');
    const singleDayData = {
      truck_id: 1,
      driver_id: 1,
      shift_type: 'day',
      start_date: '2025-04-01',
      end_date: '2025-04-01', // Same date = single day
      start_time: '06:00:00',
      end_time: '18:00:00',
      status: 'scheduled'
    };
    
    console.log('   Request data:', JSON.stringify(singleDayData, null, 2));
    
    const createResponse = await makeRequest('/api/shifts', {
      method: 'POST',
      headers,
      body: singleDayData
    });
    
    console.log('   Response status:', createResponse.status);
    console.log('   Response data:', JSON.stringify(createResponse.data, null, 2));
    
    if (createResponse.status === 201) {
      console.log('   ✅ Single-day shift created successfully');
      const shiftId = createResponse.data.data.id;
      
      // Test 2: Edit the shift
      console.log('\n📋 Test 2: Editing shift with unified approach');
      const editData = {
        start_date: '2025-04-02',
        end_date: '2025-04-02',
        start_time: '07:00:00',
        end_time: '19:00:00'
      };
      
      console.log('   Edit data:', JSON.stringify(editData, null, 2));
      
      const editResponse = await makeRequest(`/api/shifts/${shiftId}`, {
        method: 'PUT',
        headers,
        body: editData
      });
      
      console.log('   Edit response status:', editResponse.status);
      console.log('   Edit response data:', JSON.stringify(editResponse.data, null, 2));
      
      if (editResponse.status === 200) {
        console.log('   ✅ Shift edited successfully');
      } else {
        console.log('   ❌ Shift edit failed');
      }
      
      // Cleanup
      await makeRequest(`/api/shifts/${shiftId}`, {
        method: 'DELETE',
        headers
      });
      console.log('   🧹 Cleaned up test shift');
      
    } else {
      console.log('   ❌ Single-day shift creation failed');
    }
    
    // Test 3: Create multi-day shift
    console.log('\n📋 Test 3: Creating multi-day shift with unified approach');
    const multiDayData = {
      truck_id: 2,
      driver_id: 2,
      shift_type: 'night',
      start_date: '2025-04-05',
      end_date: '2025-04-07', // 3 days
      start_time: '18:00:00',
      end_time: '06:00:00',
      status: 'scheduled'
    };
    
    console.log('   Request data:', JSON.stringify(multiDayData, null, 2));
    
    const multiCreateResponse = await makeRequest('/api/shifts', {
      method: 'POST',
      headers,
      body: multiDayData
    });
    
    console.log('   Response status:', multiCreateResponse.status);
    console.log('   Response data:', JSON.stringify(multiCreateResponse.data, null, 2));
    
    if (multiCreateResponse.status === 201) {
      console.log('   ✅ Multi-day shifts created successfully');
      
      // Cleanup
      if (Array.isArray(multiCreateResponse.data.data)) {
        for (const shift of multiCreateResponse.data.data) {
          await makeRequest(`/api/shifts/${shift.id}`, {
            method: 'DELETE',
            headers
          });
        }
      }
      console.log('   🧹 Cleaned up test shifts');
    } else {
      console.log('   ❌ Multi-day shift creation failed');
    }
    
  } catch (error) {
    console.error('❌ Debug test failed:', error.message);
  }
}

debugUnifiedApproach();
