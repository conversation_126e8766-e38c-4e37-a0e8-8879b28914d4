import axios from 'axios';
import toast from 'react-hot-toast';

// Import network utilities for dynamic detection
import { getApiBaseUrl as getApiBaseUrlUtil } from '../utils/network-utils';

// Determine API base URL with automatic detection
const getApiBaseUrl = () => {
  return getApiBaseUrlUtil();
};

// Detect if running on mobile device
const isMobileDevice = () => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

// Create axios instance with mobile-optimized configuration
const api = axios.create({
  baseURL: getApiBaseUrl(),
  timeout: isMobileDevice() ? 30000 : 10000, // Longer timeout for mobile
  headers: {
    'Content-Type': 'application/json',
  },
});

// Ensure axios sends credentials (cookies) with every request
api.defaults.withCredentials = true;

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('hauling_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response;
      
      if (status === 401) {
        // Unauthorized - clear token and redirect to login
        localStorage.removeItem('hauling_token');
        localStorage.removeItem('hauling_user');
        
        if (data.error === 'Token Expired') {
          toast.error('Your session has expired. Please login again.');
        } else {
          toast.error('Authentication failed. Please login again.');
        }
        
        // Redirect to login page
        window.location.href = '/login';
      } else if (status === 403) {
        toast.error('Access denied. You don\'t have permission for this action.');
      } else if (status === 404) {
        toast.error('Resource not found.');
      } else if (status >= 500) {
        toast.error('Server error. Please try again later.');
      } else if (data.message) {
        toast.error(data.message);
      }
    } else if (error.request) {
      // Network error
      toast.error('Network error. Please check your connection.');
    } else {
      // Other error
      toast.error('An unexpected error occurred.');
    }
    
    return Promise.reject(error);
  }
);

// Retry utility for rate limited requests
const retryWithBackoff = async (fn, retries = 3, delay = 1000) => {
  try {
    return await fn();
  } catch (error) {
    if (error.response?.status === 429 && retries > 0) {
      const retryAfter = error.response.headers['retry-after'];
      const waitTime = retryAfter ? parseInt(retryAfter) * 1000 : delay;
      
      console.log(`Rate limited. Retrying in ${waitTime}ms... (${retries} retries left)`);
      
      await new Promise(resolve => setTimeout(resolve, waitTime));
      return retryWithBackoff(fn, retries - 1, delay * 2);
    }
    throw error;
  }
};

// Mobile-specific login with enhanced error handling
const mobileLogin = async (credentials) => {
  console.log('🔍 Mobile login attempt:', { isMobile: isMobileDevice() });

  try {
    // Create mobile-optimized request
    const mobileApi = axios.create({
      baseURL: getApiBaseUrl(),
      timeout: 30000, // 30 second timeout for mobile
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': navigator.userAgent,
      },
      withCredentials: true,
    });

    console.log('📡 Making login request to:', `${getApiBaseUrl()}/auth/login`);

    const response = await mobileApi.post('/auth/login', credentials);

    console.log('✅ Mobile login response received:', {
      status: response.status,
      hasToken: !!response.data.token,
      hasUser: !!response.data.user
    });

    const { token, user } = response.data;

    // Store token and user in localStorage with error handling
    try {
      localStorage.setItem('hauling_token', token);
      localStorage.setItem('hauling_user', JSON.stringify(user));
      console.log('✅ Token stored in localStorage');
    } catch (storageError) {
      console.error('❌ localStorage error:', storageError);
      throw new Error('Failed to store authentication data');
    }

    return { success: true, data: response.data };
  } catch (error) {
    console.error('❌ Mobile login error:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      code: error.code,
      isMobile: isMobileDevice()
    });

    // Enhanced mobile error handling
    if (error.code === 'NETWORK_ERROR' || error.message.includes('Network Error')) {
      throw new Error('Network connection failed. Please check your internet connection and try again.');
    }

    if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
      throw new Error('Request timed out. Please check your connection and try again.');
    }

    if (error.response?.status === 429) {
      throw error; // Let retry logic handle rate limiting
    }

    throw error;
  }
};

// Auth API methods
export const authAPI = {  // Login user with mobile-optimized retry logic
  login: async (credentials) => {
    // Use mobile-specific login for mobile devices
    if (isMobileDevice()) {
      console.log('📱 Using mobile-optimized login');
      return retryWithBackoff(async () => {
        return await mobileLogin(credentials);
      }, 3, 2000); // 3 retries with 2 second delay
    }

    // Standard login for desktop
    return retryWithBackoff(async () => {
      try {
        const response = await api.post('/auth/login', credentials);
        const { token, user } = response.data;

        // Store token and user in localStorage
        localStorage.setItem('hauling_token', token);
        localStorage.setItem('hauling_user', JSON.stringify(user));

        return { success: true, data: response.data };
      } catch (error) {
        console.error('Login API error:', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          message: error.message
        });

        // Re-throw for retry logic to handle
        if (error.response?.status === 429) {
          throw error;
        }
        
        return { 
          success: false, 
          error: error.response?.data?.message || 'Login failed',
          status: error.response?.status
        };
      }
    });
  },

  // Logout user
  logout: async () => {
    try {
      await api.post('/auth/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Always clear local storage
      localStorage.removeItem('hauling_token');
      localStorage.removeItem('hauling_user');
    }
  },

  // Get user profile
  getProfile: async () => {
    try {
      const response = await api.get('/auth/profile');
      return { success: true, data: response.data };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || 'Failed to get profile' 
      };
    }
  },

  // Verify token
  verifyToken: async () => {
    try {
      const response = await api.get('/auth/verify');
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false };
    }
  },

  // Change password
  changePassword: async (passwordData) => {
    try {
      const response = await api.post('/auth/change-password', passwordData);
      return { success: true, data: response.data };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || 'Failed to change password' 
      };
    }
  }
};

// Users API methods
export const usersAPI = {
  getAll: (config = {}) => api.get('/users', config),
  getById: (id) => api.get(`/users/${id}`),
  create: (data) => api.post('/users', data),
  update: (id, data) => api.put(`/users/${id}`, data),
  delete: (id) => api.delete(`/users/${id}`),
  activate: (id) => api.put(`/users/${id}/activate`),
  getStats: () => api.get('/users/stats'),
};

// Trucks API methods
export const trucksAPI = {
  getAll: (config = {}) => api.get('/trucks', config),
  getById: (id) => api.get(`/trucks/${id}`),
  create: (data) => api.post('/trucks', data),
  update: (id, data) => api.put(`/trucks/${id}`, data),
  delete: (id) => api.delete(`/trucks/${id}`),
  generateQR: (id) => api.get(`/trucks/${id}/qr`),
};

// Drivers API methods
export const driversAPI = {
  getAll: (config = {}) => api.get('/drivers', config),
  getById: (id) => api.get(`/drivers/${id}`),
  create: (data) => api.post('/drivers', data),
  update: (id, data) => api.put(`/drivers/${id}`, data),
  delete: (id) => api.delete(`/drivers/${id}`),
};

// Locations API methods
export const locationsAPI = {
  getAll: (config = {}) => api.get('/locations', config),
  getById: (id) => api.get(`/locations/${id}`),
  create: (data) => api.post('/locations', data),
  update: (id, data) => api.put(`/locations/${id}`, data),
  delete: (id) => api.delete(`/locations/${id}`),
  getQRCode: (id) => api.get(`/locations/${id}/qr`),
};

// Assignments API methods
export const assignmentsAPI = {
  getAll: (config = {}) => api.get('/assignments', config),
  getById: (id) => api.get(`/assignments/${id}`),
  create: (data) => api.post('/assignments', data),
  update: (id, data) => api.put(`/assignments/${id}`, data),
  delete: (id) => api.delete(`/assignments/${id}`),
  getActive: () => api.get('/assignments/active'),
};

// Trips API methods
export const tripsAPI = {
  getAll: (config = {}) => api.get('/trips', config),
  getById: (id) => api.get(`/trips/${id}`),
  create: (data) => api.post('/trips', data),
  update: (id, data) => api.put(`/trips/${id}`, data),
  delete: (id) => api.delete(`/trips/${id}`),
  getSummary: () => api.get('/trips/summary'),
  getExceptionStats: () => api.get('/trips/stats/exceptions'),
  getDurationStats: (params = {}) => api.get('/trips/stats/durations', { params }),
};

// REMOVED: Approvals API methods - exception management system eliminated

// Scanner API methods
export const scannerAPI = {
  processScan: (data) => api.post('/scanner/scan', data),
  getTripStatus: (tripId) => api.get(`/scanner/status/${tripId}`),
};

// Analytics API methods
export const analyticsAPI = {
  getDashboard: () => api.get('/analytics/dashboard'),
  getTripMetrics: (params) => api.get('/analytics/trips', { params }),
  getPerformance: (params) => api.get('/analytics/performance', { params }),
  getAssignments: (params) => api.get('/analytics/assignments', { params }),
  getRoutes: (params) => api.get('/analytics/routes', { params }),
  getAssignmentTrends: (params) => api.get('/analytics/assignment-trends', { params }),
  getTruckRankings: (params) => api.get('/analytics/truck-rankings', { params }),
};

// Generic API request function for custom endpoints
export const apiRequest = async (endpoint, options = {}) => {
  try {
    const config = {
      url: endpoint,
      method: options.method || 'GET',
      ...options
    };

    // Handle request body
    if (options.body) {
      config.data = typeof options.body === 'string' ? JSON.parse(options.body) : options.body;
    }

    const response = await api(config);
    return response.data;
  } catch (error) {
    // Re-throw the error to be handled by the calling component
    throw error;
  }
};

export default api;