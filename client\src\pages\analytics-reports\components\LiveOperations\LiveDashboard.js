import React from 'react';

const StatusBadge = ({ alertStatus, currentPhase }) => {
  const getStatusConfig = (alertStatus, currentPhase) => {
    switch (alertStatus) {
      case 'breakdown':
        return { color: 'bg-red-100 text-red-800 border-red-200', icon: '🔧', label: 'Breakdown' };
      case 'overdue':
        return { color: 'bg-yellow-100 text-yellow-800 border-yellow-200', icon: '⚠️', label: 'Overdue' };
      case 'exception':
        return { color: 'bg-orange-100 text-orange-800 border-orange-200', icon: '⚡', label: 'Exception' };
      default:
        return { color: 'bg-green-100 text-green-800 border-green-200', icon: '✅', label: 'Normal' };
    }
  };

  const config = getStatusConfig(alertStatus, currentPhase);

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${config.color}`}>
      <span className="mr-1">{config.icon}</span>
      {config.label}
    </span>
  );
};

const formatTimeInPhase = (minutes) => {
  if (minutes < 60) {
    return `${Math.round(minutes)}m`;
  } else {
    const hours = Math.floor(minutes / 60);
    const mins = Math.round(minutes % 60);
    return `${hours}h ${mins}m`;
  }
};

const getPhaseDisplay = (phase) => {
  switch (phase) {
    case 'loading_start': return '⬆️ Loading';
    case 'loading_end': return '🚛 To Unload';
    case 'unloading_start': return '⬇️ Unloading';
    case 'unloading_end': return '🔄 To Load';
    case 'breakdown': return '🔧 Breakdown';
    case 'assigned': return '📋 Assigned';
    default: return '⭕ Idle';
  }
};

const LiveDashboard = ({ data, loading }) => {
  if (loading) {
    return (
      <div className="animate-pulse space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="bg-secondary-200 h-16 rounded-lg"></div>
        ))}
      </div>
    );
  }

  if (!data || !data.operations || data.operations.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow border border-secondary-200 p-8 text-center">
        <span className="text-4xl block mb-2">📊</span>
        <p className="text-secondary-500">No live operations data available</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow border border-secondary-200 overflow-hidden">
      {/* Desktop View */}
      <div className="hidden md:block">
        <table className="min-w-full divide-y divide-secondary-200">
          <thead className="bg-secondary-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Truck
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Driver
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Assignment
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Current Phase
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Location
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                ETA
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Status
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-secondary-200">
            {data.operations.map((truck, index) => (
              <tr key={truck.truckId || `truck-${index}`} className="hover:bg-secondary-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="text-sm font-medium text-secondary-900">
                      {truck.truckNumber}
                    </div>
                    {truck.isDynamicAssignment && (
                      <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                        Dynamic
                      </span>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-secondary-900">
                    {truck.driverName}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-secondary-900">
                    {truck.assignmentCode || 'N/A'}
                  </div>
                  {truck.tripNumber && (
                    <div className="text-xs text-secondary-500">
                      Trip #{truck.tripNumber}
                    </div>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-secondary-900">
                    {getPhaseDisplay(truck.currentPhase)}
                  </div>
                  <div className="text-xs text-secondary-500">
                    {formatTimeInPhase(truck.timeInPhaseMinutes)}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-secondary-900">
                    {truck.currentLocationDisplay}
                  </div>
                  {truck.routeDisplay && truck.routeDisplay !== 'Unknown → Unknown' && (
                    <div className="text-xs text-secondary-500">
                      {truck.routeDisplay}
                    </div>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-900">
                  {truck.estimatedMinutesRemaining ? 
                    `${truck.estimatedMinutesRemaining}m` : 
                    'N/A'
                  }
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <StatusBadge 
                    alertStatus={truck.alertStatus} 
                    currentPhase={truck.currentPhase}
                  />
                  {truck.priority && truck.priority !== 'normal' && (
                    <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                      {truck.priority}
                    </span>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Mobile View */}
      <div className="md:hidden">
        <div className="space-y-4 p-4">
          {data.operations.map((truck, index) => (
            <div key={truck.truckId || `mobile-truck-${index}`} className="bg-secondary-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  <div className="font-medium text-secondary-900">
                    {truck.truckNumber}
                  </div>
                  {truck.isDynamicAssignment && (
                    <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                      Dynamic
                    </span>
                  )}
                </div>
                <StatusBadge 
                  alertStatus={truck.alertStatus} 
                  currentPhase={truck.currentPhase}
                />
              </div>
              
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-secondary-500">Driver:</span>
                  <span className="text-secondary-900">{truck.driverName}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-secondary-500">Phase:</span>
                  <span className="text-secondary-900">{getPhaseDisplay(truck.currentPhase)}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-secondary-500">Location:</span>
                  <span className="text-secondary-900">{truck.currentLocationDisplay}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-secondary-500">Time in Phase:</span>
                  <span className="text-secondary-900">{formatTimeInPhase(truck.timeInPhaseMinutes)}</span>
                </div>
                
                {truck.estimatedMinutesRemaining && (
                  <div className="flex justify-between">
                    <span className="text-secondary-500">ETA:</span>
                    <span className="text-secondary-900">{truck.estimatedMinutesRemaining}m</span>
                  </div>
                )}
                
                {truck.assignmentCode && (
                  <div className="flex justify-between">
                    <span className="text-secondary-500">Assignment:</span>
                    <span className="text-secondary-900">{truck.assignmentCode}</span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default LiveDashboard;
