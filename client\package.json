{"name": "hauling-qr-client", "version": "1.0.0", "description": "Frontend React App for Hauling QR Trip Management System", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.2", "@yudiel/react-qr-scanner": "^2.3.1", "axios": "^1.6.2", "chart.js": "^4.4.0", "classnames": "^2.3.2", "date-fns": "^2.30.0", "html5-qrcode": "^2.3.8", "js-cookie": "^3.0.5", "jszip": "^3.10.1", "node-html-parser": "^7.1.0", "pdf-lib": "^1.17.1", "pwa-asset-generator": "^2.0.0", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-icons": "^4.12.0", "react-qr-scanner": "^1.0.0-alpha.11", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1"}, "scripts": {"start": "set HOST=0.0.0.0&&react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"], "rules": {"no-console": "off", "no-unused-vars": "warn", "react-hooks/exhaustive-deps": "warn"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6"}}