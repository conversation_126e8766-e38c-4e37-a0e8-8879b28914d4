const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying Breakdown-to-Stopped Terminology Refactoring\n');

// Function to search for text in files
function searchInFile(filePath, searchTerm) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    const matches = [];
    
    lines.forEach((line, index) => {
      if (line.toLowerCase().includes(searchTerm.toLowerCase())) {
        matches.push({
          line: index + 1,
          content: line.trim()
        });
      }
    });
    
    return matches;
  } catch (error) {
    return [];
  }
}

// Function to recursively search directories
function searchInDirectory(dirPath, searchTerm, excludeDirs = []) {
  const results = [];
  
  try {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        if (!excludeDirs.includes(item) && !item.startsWith('.')) {
          results.push(...searchInDirectory(fullPath, searchTerm, excludeDirs));
        }
      } else if (stat.isFile()) {
        const ext = path.extname(item);
        if (['.js', '.jsx', '.ts', '.tsx'].includes(ext)) {
          const matches = searchInFile(fullPath, searchTerm);
          if (matches.length > 0) {
            results.push({
              file: fullPath,
              matches: matches
            });
          }
        }
      }
    }
  } catch (error) {
    // Ignore errors for inaccessible directories
  }
  
  return results;
}

// Test 1: Verify StoppedAnalytics component exists
console.log('📋 Test 1: Frontend Component Verification');
const stoppedAnalyticsPath = path.join(__dirname, '../client/src/pages/analytics-reports/components/TripPerformance/StoppedAnalytics.js');
const breakdownAnalyticsPath = path.join(__dirname, '../client/src/pages/analytics-reports/components/TripPerformance/BreakdownAnalytics.js');

if (fs.existsSync(stoppedAnalyticsPath)) {
  console.log('✅ StoppedAnalytics.js component exists');
} else {
  console.log('❌ StoppedAnalytics.js component missing');
}

if (!fs.existsSync(breakdownAnalyticsPath)) {
  console.log('✅ BreakdownAnalytics.js component removed');
} else {
  console.log('❌ BreakdownAnalytics.js component still exists');
}

// Test 2: Search for remaining breakdown references in application code
console.log('\n📋 Test 2: Searching for Remaining Breakdown References');

const searchDirs = [
  path.join(__dirname, '../client/src'),
  path.join(__dirname, '../server/routes')
];

const excludeDirs = ['node_modules', '.git', 'build', 'dist'];
let totalBreakdownRefs = 0;

for (const searchDir of searchDirs) {
  if (fs.existsSync(searchDir)) {
    const results = searchInDirectory(searchDir, 'breakdown', excludeDirs);
    
    if (results.length > 0) {
      console.log(`\n🔍 Found breakdown references in ${searchDir}:`);
      results.forEach(result => {
        console.log(`   📄 ${result.file}`);
        result.matches.forEach(match => {
          console.log(`      Line ${match.line}: ${match.content}`);
          totalBreakdownRefs++;
        });
      });
    }
  }
}

if (totalBreakdownRefs === 0) {
  console.log('✅ No breakdown references found in application code');
} else {
  console.log(`❌ Found ${totalBreakdownRefs} breakdown references that need attention`);
}

// Test 3: Verify API endpoint files use stopped terminology
console.log('\n📋 Test 3: API Endpoint Verification');

const analyticsRoutePath = path.join(__dirname, '../server/routes/analytics.js');
if (fs.existsSync(analyticsRoutePath)) {
  const analyticsContent = fs.readFileSync(analyticsRoutePath, 'utf8');
  
  if (analyticsContent.includes('/stopped-analytics')) {
    console.log('✅ stopped-analytics endpoint found');
  } else {
    console.log('❌ stopped-analytics endpoint missing');
  }
  
  if (analyticsContent.includes("status = 'stopped'")) {
    console.log('✅ Database queries use stopped status');
  } else {
    console.log('❌ Database queries may not be using stopped status');
  }
  
  // Check for dual status queries (should be removed)
  if (analyticsContent.includes("IN ('stopped', 'breakdown')")) {
    console.log('❌ Found dual-status queries that should be replaced');
  } else {
    console.log('✅ No dual-status queries found (complete replacement)');
  }
}

const tripsRoutePath = path.join(__dirname, '../server/routes/trips.js');
if (fs.existsSync(tripsRoutePath)) {
  const tripsContent = fs.readFileSync(tripsRoutePath, 'utf8');
  
  if (tripsContent.includes('/resolve-stopped')) {
    console.log('✅ resolve-stopped endpoint found');
  } else {
    console.log('❌ resolve-stopped endpoint missing');
  }
}

// Test 4: Check frontend imports
console.log('\n📋 Test 4: Frontend Import Verification');

const frontendFiles = [
  '../client/src/pages/analytics-reports/components/TripPerformance/TripPerformance.js',
  '../client/src/pages/analytics/Analytics.js',
  '../client/src/pages/analytics/UnifiedAnalytics.js'
];

let correctImports = 0;
let totalImportFiles = 0;

frontendFiles.forEach(filePath => {
  const fullPath = path.join(__dirname, filePath);
  if (fs.existsSync(fullPath)) {
    totalImportFiles++;
    const content = fs.readFileSync(fullPath, 'utf8');
    
    if (content.includes('import StoppedAnalytics') && !content.includes('import BreakdownAnalytics')) {
      correctImports++;
      console.log(`✅ ${path.basename(filePath)} uses correct imports`);
    } else {
      console.log(`❌ ${path.basename(filePath)} has incorrect imports`);
    }
  }
});

// Test 5: Database column usage verification
console.log('\n📋 Test 5: Database Column Usage');

if (fs.existsSync(analyticsRoutePath)) {
  const analyticsContent = fs.readFileSync(analyticsRoutePath, 'utf8');
  
  // Check if still using breakdown column names appropriately
  if (analyticsContent.includes('breakdown_reported_at') && 
      analyticsContent.includes('breakdown_reason') && 
      analyticsContent.includes('breakdown_resolved_at')) {
    console.log('✅ Database columns properly referenced (columns remain for data storage)');
  } else {
    console.log('❌ Database column references may be missing');
  }
}

// Summary
console.log('\n📊 REFACTORING VERIFICATION SUMMARY');
console.log('=====================================');

if (totalBreakdownRefs === 0) {
  console.log('✅ PASSED: No breakdown terminology in application code');
} else {
  console.log(`❌ FAILED: ${totalBreakdownRefs} breakdown references still exist`);
}

if (correctImports === totalImportFiles) {
  console.log('✅ PASSED: All frontend imports use stopped terminology');
} else {
  console.log(`❌ FAILED: ${totalImportFiles - correctImports} files have incorrect imports`);
}

console.log('\n🎯 REFACTORING STATUS:');
if (totalBreakdownRefs === 0 && correctImports === totalImportFiles) {
  console.log('🟢 COMPLETE: Breakdown-to-Stopped refactoring successfully completed!');
  console.log('   - All frontend components updated');
  console.log('   - All API endpoints refactored');
  console.log('   - Database queries use only stopped status');
  console.log('   - Import statements corrected');
  console.log('   - UI terminology consistent');
} else {
  console.log('🟡 INCOMPLETE: Some issues remain that need attention');
}

console.log('\n✨ Refactoring verification completed!\n');
