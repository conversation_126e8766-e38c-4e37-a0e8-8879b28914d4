/**
 * Fix Trip 254 Completion
 * Complete the stuck Trip 254 and clean up the problematic state
 */

const { Pool } = require('pg');

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 5432,
  database: process.env.DB_NAME || 'hauling_qr_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'PostgreSQLPassword'
};

const pool = new Pool(dbConfig);

async function fixTrip254Completion() {
  try {
    console.log('🔧 FIXING TRIP 254 COMPLETION...\n');

    // Check current state
    console.log('📊 Current State:');
    const currentState = await pool.query(`
      SELECT 
        id, trip_number, status, assignment_id,
        loading_start_time, loading_end_time,
        unloading_start_time, unloading_end_time,
        trip_completed_time
      FROM trip_logs 
      WHERE id IN (253, 254, 255)
      ORDER BY id
    `);

    currentState.rows.forEach(trip => {
      console.log(`  Trip ${trip.trip_number} (ID: ${trip.id}): ${trip.status}`);
      console.log(`    Loading: ${trip.loading_start_time || 'NULL'} → ${trip.loading_end_time || 'NULL'}`);
      console.log(`    Unloading: ${trip.unloading_start_time || 'NULL'} → ${trip.unloading_end_time || 'NULL'}`);
      console.log(`    Completed: ${trip.trip_completed_time || 'NULL'}`);
    });

    // Fix Trip 254 if it's stuck in unloading_end
    const trip254 = currentState.rows.find(trip => trip.id === 254);
    if (trip254 && trip254.status === 'unloading_end') {
      console.log('\n🔧 Completing Trip 254:');
      
      const completionTime = new Date(new Date(trip254.unloading_end_time).getTime() + (60 * 1000)); // Add 1 minute
      const totalDuration = Math.round((completionTime - new Date(trip254.loading_start_time)) / (1000 * 60));
      
      await pool.query(`
        UPDATE trip_logs 
        SET 
          status = 'trip_completed',
          trip_completed_time = $1,
          total_duration_minutes = $2,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = 254
      `, [completionTime.toISOString(), totalDuration]);
      
      console.log(`  ✅ Trip 254 completed at ${completionTime.toISOString()}`);
      console.log(`  ✅ Total duration: ${totalDuration} minutes`);
    }

    // Delete any problematic Trip 255 that was created incorrectly
    const trip255 = currentState.rows.find(trip => trip.id === 255);
    if (trip255) {
      console.log('\n🗑️ Deleting problematic Trip 255:');
      await pool.query(`DELETE FROM trip_logs WHERE id = 255`);
      console.log(`  ✅ Trip 255 deleted`);
    }

    // Check final state
    console.log('\n📊 Final State:');
    const finalState = await pool.query(`
      SELECT 
        id, trip_number, status, assignment_id,
        trip_completed_time IS NOT NULL as is_completed
      FROM trip_logs 
      WHERE id IN (253, 254)
      ORDER BY id
    `);

    finalState.rows.forEach(trip => {
      console.log(`  Trip ${trip.trip_number} (ID: ${trip.id}): ${trip.status} ${trip.is_completed ? '✅' : '❌'}`);
    });

    // Check for any active trips
    const activeTrips = await pool.query(`
      SELECT COUNT(*) as count
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      WHERE dt.truck_number = 'DT-100'
        AND tl.status NOT IN ('trip_completed', 'cancelled', 'breakdown')
    `);

    console.log(`\n🎯 Active trips for DT-100: ${activeTrips.rows[0].count}`);
    console.log(`🎯 System ready for new trip: ${activeTrips.rows[0].count === 0 ? '✅ YES' : '❌ NO'}`);

    return activeTrips.rows[0].count === 0;

  } catch (error) {
    console.error('❌ Fix failed:', error.message);
    return false;
  } finally {
    await pool.end();
  }
}

// Run fix if called directly
if (require.main === module) {
  fixTrip254Completion().then(success => {
    console.log(`\n🎯 Fix Result: ${success ? '✅ SUCCESS' : '❌ FAILED'}`);
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('Fix execution failed:', error);
    process.exit(1);
  });
}

module.exports = fixTrip254Completion;
