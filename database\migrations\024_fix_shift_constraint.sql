-- ============================================================================
-- Migration: Fix Shift Constraint for Multi-Driver Support
-- Purpose: Update the unique constraint to allow multiple drivers per truck
-- Date: 2025-01-10
-- ============================================================================

-- Drop the existing overly restrictive constraint
ALTER TABLE driver_shifts DROP CONSTRAINT IF EXISTS driver_shifts_truck_id_shift_date_start_time_key;

-- Add a more appropriate constraint that includes driver_id
-- This allows multiple drivers to work the same truck on the same date/time
-- but prevents the same driver from having duplicate shifts
ALTER TABLE driver_shifts 
ADD CONSTRAINT driver_shifts_unique_driver_shift 
UNIQUE(truck_id, driver_id, shift_date, start_time);

-- Note: Complex overlap detection is handled in application logic
-- We rely on the application-level validation for time overlap prevention
-- This is more flexible and handles complex scenarios like overnight shifts

-- Create index for efficient shift conflict queries
CREATE INDEX IF NOT EXISTS idx_driver_shifts_conflict_check 
ON driver_shifts (driver_id, shift_date, start_time, end_time, status)
WHERE status IN ('scheduled', 'active');

-- Create index for truck-based queries (multiple drivers per truck)
CREATE INDEX IF NOT EXISTS idx_driver_shifts_truck_multi_driver 
ON driver_shifts (truck_id, shift_date, start_time)
WHERE status IN ('scheduled', 'active');

-- Add comment explaining the constraint strategy
COMMENT ON CONSTRAINT driver_shifts_unique_driver_shift ON driver_shifts IS 
'Prevents duplicate shifts for the same driver on same truck/date/time. Allows multiple drivers per truck.';

-- Complex overlap detection is handled in application logic for flexibility

-- Check for any existing duplicates that would violate the new constraint
DO $$
DECLARE
    duplicate_count INTEGER;
    rec RECORD;
BEGIN
    -- Check for duplicates that would violate the new constraint
    SELECT COUNT(*) INTO duplicate_count
    FROM (
        SELECT truck_id, driver_id, shift_date, start_time, COUNT(*)
        FROM driver_shifts
        WHERE status IN ('scheduled', 'active')
        GROUP BY truck_id, driver_id, shift_date, start_time
        HAVING COUNT(*) > 1
    ) duplicates;

    IF duplicate_count > 0 THEN
        RAISE NOTICE 'Found % duplicate shift combinations that need manual review', duplicate_count;

        -- Log the duplicates for manual review
        RAISE NOTICE 'Duplicate shifts found:';
        FOR rec IN
            SELECT truck_id, driver_id, shift_date, start_time, array_agg(id) as shift_ids
            FROM driver_shifts
            WHERE status IN ('scheduled', 'active')
            GROUP BY truck_id, driver_id, shift_date, start_time
            HAVING COUNT(*) > 1
        LOOP
            RAISE NOTICE 'Truck %, Driver %, Date %, Time %: Shift IDs %',
                rec.truck_id, rec.driver_id, rec.shift_date, rec.start_time, rec.shift_ids;
        END LOOP;
    ELSE
        RAISE NOTICE 'No duplicate shifts found. Constraint migration completed successfully.';
    END IF;
END $$;
