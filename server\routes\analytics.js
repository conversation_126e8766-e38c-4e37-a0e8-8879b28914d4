const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const auth = require('../middleware/auth');

// @route   GET /api/analytics/dashboard
// @desc    Get dashboard analytics data
// @access  Private
router.get('/dashboard', auth, async (req, res) => {
  try {
    // Get fleet overview
    const fleetQuery = `
      SELECT
        (SELECT COUNT(*) FROM dump_trucks) as total_trucks,
        (SELECT COUNT(*) FROM dump_trucks WHERE status = 'active') as active_trucks,
        (SELECT COUNT(*) FROM drivers) as total_drivers,
        (SELECT COUNT(*) FROM drivers WHERE status = 'active') as active_drivers,
        (SELECT COUNT(*) FROM locations) as total_locations,
        (SELECT COUNT(*) FROM locations WHERE status = 'active') as active_locations,
        (SELECT COUNT(*) FROM assignments) as total_assignments,
        (SELECT COUNT(*) FROM assignments WHERE status IN ('assigned', 'in_progress')) as active_assignments
    `;

    // Get trip statistics
    const tripsQuery = `
      SELECT 
        COUNT(*) as total_trips,
        COUNT(CASE WHEN DATE(created_at) = CURRENT_DATE THEN 1 END) as today_trips,
        COUNT(CASE WHEN DATE(created_at) = CURRENT_DATE AND status = 'trip_completed' THEN 1 END) as today_completed,
        COUNT(CASE WHEN created_at >= DATE_TRUNC('week', CURRENT_DATE) THEN 1 END) as weekly_trips,
        COUNT(CASE WHEN created_at >= DATE_TRUNC('week', CURRENT_DATE) AND status = 'trip_completed' THEN 1 END) as weekly_completed,
        COUNT(CASE WHEN created_at >= DATE_TRUNC('month', CURRENT_DATE) THEN 1 END) as monthly_trips,
        COUNT(CASE WHEN created_at >= DATE_TRUNC('month', CURRENT_DATE) AND status = 'trip_completed' THEN 1 END) as monthly_completed,
        COUNT(CASE WHEN status = 'trip_completed' THEN 1 END) as total_completed
      FROM trip_logs
    `;

    // Get performance metrics
    const performanceQuery = `
      SELECT 
        AVG(total_duration_minutes) as avg_trip_time,
        AVG(loading_duration_minutes) as avg_loading_time,
        AVG(travel_duration_minutes) as avg_travel_time,
        AVG(unloading_duration_minutes) as avg_unloading_time,
        (COUNT(CASE WHEN status = 'trip_completed' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0)) as completion_rate,
        (COUNT(CASE WHEN is_exception = false AND status = 'trip_completed' THEN 1 END) * 100.0 / NULLIF(COUNT(CASE WHEN status = 'trip_completed' THEN 1 END), 0)) as on_time_rate,
        (COUNT(CASE WHEN is_exception = true THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0)) as exception_rate
      FROM trip_logs
      WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
    `;

    const [fleetResult, tripsResult, performanceResult] = await Promise.all([
      query(fleetQuery),
      query(tripsQuery),
      query(performanceQuery)
    ]);

    const fleet = fleetResult.rows[0];
    const trips = tripsResult.rows[0];
    const performance = performanceResult.rows[0];

    // Format the response
    const dashboardData = {
      fleet: {
        totalTrucks: parseInt(fleet.total_trucks) || 0,
        activeTrucks: parseInt(fleet.active_trucks) || 0,
        totalDrivers: parseInt(fleet.total_drivers) || 0,
        activeDrivers: parseInt(fleet.active_drivers) || 0,
        totalLocations: parseInt(fleet.total_locations) || 0,
        activeLocations: parseInt(fleet.active_locations) || 0,
        totalAssignments: parseInt(fleet.total_assignments) || 0,
        activeAssignments: parseInt(fleet.active_assignments) || 0
      },
      trips: {
        todayTrips: parseInt(trips.today_trips) || 0,
        todayCompleted: parseInt(trips.today_completed) || 0,
        weeklyTrips: parseInt(trips.weekly_trips) || 0,
        weeklyCompleted: parseInt(trips.weekly_completed) || 0,
        monthlyTrips: parseInt(trips.monthly_trips) || 0,
        monthlyCompleted: parseInt(trips.monthly_completed) || 0,
        totalTrips: parseInt(trips.total_trips) || 0,
        totalCompleted: parseInt(trips.total_completed) || 0
      },
      performance: {
        avgTripTime: performance.avg_trip_time ? Math.round(parseFloat(performance.avg_trip_time)) : 0,
        avgLoadingTime: performance.avg_loading_time ? Math.round(parseFloat(performance.avg_loading_time)) : 0,
        avgTravelTime: performance.avg_travel_time ? Math.round(parseFloat(performance.avg_travel_time)) : 0,
        avgUnloadingTime: performance.avg_unloading_time ? Math.round(parseFloat(performance.avg_unloading_time)) : 0,
        completionRate: performance.completion_rate ? parseFloat(performance.completion_rate).toFixed(1) : '0.0',
        onTimeRate: performance.on_time_rate ? parseFloat(performance.on_time_rate).toFixed(1) : '0.0',
        exceptionRate: performance.exception_rate ? parseFloat(performance.exception_rate).toFixed(1) : '0.0'
      }
    };

    res.json({
      success: true,
      data: dashboardData
    });

  } catch (error) {
    console.error('Get dashboard analytics error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve dashboard analytics'
    });
  }
});

// @route   GET /api/analytics/assignments
// @desc    Get assignment analytics data
// @access  Private
router.get('/assignments', auth, async (req, res) => {
  try {
    const { start_date, end_date } = req.query;

    // Build date filter
    let dateFilter = '';
    if (start_date && end_date) {
      dateFilter = `WHERE a.created_at >= '${start_date}' AND a.created_at <= '${end_date}'`;
    } else {
      dateFilter = `WHERE a.created_at >= CURRENT_DATE - INTERVAL '30 days'`;
    }

    // Get assignment summary statistics
    const summaryQuery = `
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN a.status = 'assigned' THEN 1 END) as active,
        COUNT(CASE WHEN a.status = 'completed' THEN 1 END) as completed,
        COUNT(CASE WHEN a.notes::text LIKE '%auto_assignment%' THEN 1 END) as auto_created,
        ROUND(
          (COUNT(CASE WHEN a.status = 'completed' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0)), 1
        ) as completion_rate
      FROM assignments a
      ${dateFilter}
    `;

    // Get recent assignments
    const recentQuery = `
      SELECT
        a.id,
        a.assignment_code,
        a.status,
        a.created_at,
        dt.truck_number,
        ll.name as loading_location,
        ul.name as unloading_location,
        CASE WHEN a.notes::text LIKE '%auto_assignment%' THEN true ELSE false END as is_auto_created
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      ${dateFilter}
      ORDER BY a.created_at DESC
      LIMIT 20
    `;

    // Get performance metrics
    const performanceQuery = `
      SELECT
        AVG(EXTRACT(EPOCH FROM (a.updated_at - a.created_at))/1000) as avg_creation_time,
        ROUND(
          (COUNT(CASE WHEN a.status IN ('assigned', 'completed') THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0)), 1
        ) as success_rate,
        ROUND(
          (COUNT(CASE WHEN a.notes::text LIKE '%auto_assignment%' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0)), 1
        ) as auto_assignment_rate
      FROM assignments a
      ${dateFilter}
    `;

    const [summaryResult, recentResult, performanceResult] = await Promise.all([
      query(summaryQuery),
      query(recentQuery),
      query(performanceQuery)
    ]);

    const summary = summaryResult.rows[0];
    const recent = recentResult.rows;
    const performance = performanceResult.rows[0];

    res.json({
      success: true,
      data: {
        summary: {
          total: parseInt(summary.total) || 0,
          active: parseInt(summary.active) || 0,
          completed: parseInt(summary.completed) || 0,
          autoCreated: parseInt(summary.auto_created) || 0,
          completionRate: summary.completion_rate || '0.0'
        },
        recent: recent,
        trends: [], // Placeholder for future trend analysis
        performance: {
          avgCreationTime: Math.round(performance.avg_creation_time) || 0,
          successRate: performance.success_rate || 0,
          autoAssignmentRate: performance.auto_assignment_rate || 0
        }
      }
    });

  } catch (error) {
    console.error('Error fetching assignment analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch assignment analytics data'
    });
  }
});

// @route   GET /api/analytics/performance
// @desc    Get performance analytics data
// @access  Private
router.get('/performance', auth, async (req, res) => {
  try {
    const { start_date, end_date } = req.query;

    let dateFilter = '';
    const queryParams = [];
    
    if (start_date && end_date) {
      dateFilter = 'WHERE tl.created_at BETWEEN $1 AND $2';
      queryParams.push(start_date, end_date);
    } else {
      dateFilter = 'WHERE tl.created_at >= CURRENT_DATE - INTERVAL \'30 days\'';
    }

    // Daily performance trends
    const trendsQuery = `
      SELECT 
        DATE(tl.created_at) as date,
        COUNT(*) as total_trips,
        COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) as completed_trips,
        AVG(tl.total_duration_minutes) as avg_duration,
        COUNT(CASE WHEN tl.is_exception = true THEN 1 END) as exceptions
      FROM trip_logs tl
      ${dateFilter}
      GROUP BY DATE(tl.created_at)
      ORDER BY DATE(tl.created_at)
    `;

    // Truck utilization
    const truckUtilizationQuery = `
      SELECT 
        dt.truck_number,
        dt.id as truck_id,
        COUNT(tl.id) as trip_count,
        AVG(tl.total_duration_minutes) as avg_duration,
        (COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) * 100.0 / NULLIF(COUNT(tl.id), 0)) as completion_rate
      FROM dump_trucks dt
      LEFT JOIN assignments a ON dt.id = a.truck_id
      LEFT JOIN trip_logs tl ON a.id = tl.assignment_id
      ${dateFilter.replace('tl.created_at', 'COALESCE(tl.created_at, a.created_at)')}
      GROUP BY dt.id, dt.truck_number
      ORDER BY trip_count DESC
      LIMIT 10
    `;

    // Driver performance
    const driverPerformanceQuery = `
      SELECT 
        d.full_name,
        d.employee_id,
        COUNT(tl.id) as trip_count,
        AVG(tl.total_duration_minutes) as avg_duration,
        (COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) * 100.0 / NULLIF(COUNT(tl.id), 0)) as completion_rate
      FROM drivers d
      LEFT JOIN assignments a ON d.id = a.driver_id
      LEFT JOIN trip_logs tl ON a.id = tl.assignment_id
      ${dateFilter.replace('tl.created_at', 'COALESCE(tl.created_at, a.created_at)')}
      GROUP BY d.id, d.full_name, d.employee_id
      ORDER BY trip_count DESC
      LIMIT 10
    `;

    const [trendsResult, truckUtilResult, driverPerfResult] = await Promise.all([
      query(trendsQuery, queryParams),
      query(truckUtilizationQuery, queryParams),
      query(driverPerformanceQuery, queryParams)
    ]);

    const performanceData = {
      trends: trendsResult.rows.map(row => ({
        date: row.date,
        totalTrips: parseInt(row.total_trips) || 0,
        completedTrips: parseInt(row.completed_trips) || 0,
        avgDuration: row.avg_duration ? Math.round(parseFloat(row.avg_duration)) : 0,
        exceptions: parseInt(row.exceptions) || 0
      })),
      truckUtilization: truckUtilResult.rows.map(row => ({
        truckNumber: row.truck_number,
        truckId: parseInt(row.truck_id),
        tripCount: parseInt(row.trip_count) || 0,
        avgDuration: row.avg_duration ? Math.round(parseFloat(row.avg_duration)) : 0,
        completionRate: row.completion_rate ? parseFloat(row.completion_rate).toFixed(1) : '0.0'
      })),
      driverPerformance: driverPerfResult.rows.map(row => ({
        driverName: row.full_name,
        employeeId: row.employee_id,
        tripCount: parseInt(row.trip_count) || 0,
        avgDuration: row.avg_duration ? Math.round(parseFloat(row.avg_duration)) : 0,
        completionRate: row.completion_rate ? parseFloat(row.completion_rate).toFixed(1) : '0.0'
      }))
    };

    res.json({
      success: true,
      data: performanceData
    });

  } catch (error) {
    console.error('Get performance analytics error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve performance analytics'
    });
  }
});

// @route   GET /api/analytics/exceptions
// @desc    Get exceptions analytics data
// @access  Private
router.get('/exceptions', auth, async (req, res) => {
  try {
    const { start_date, end_date } = req.query;

    let dateFilter = '';
    const queryParams = [];
    
    if (start_date && end_date) {
      dateFilter = 'WHERE tl.created_at BETWEEN $1 AND $2';
      queryParams.push(start_date, end_date);
    } else {
      dateFilter = 'WHERE tl.created_at >= CURRENT_DATE - INTERVAL \'30 days\'';
    }

    // Exception summary
    const summaryQuery = `
      SELECT 
        COUNT(CASE WHEN tl.is_exception = true THEN 1 END) as total_exceptions,
        COUNT(CASE WHEN tl.is_exception = true AND tl.exception_approved_by IS NULL THEN 1 END) as pending_exceptions,
        COUNT(CASE WHEN tl.is_exception = true AND tl.exception_approved_by IS NOT NULL THEN 1 END) as resolved_exceptions,
        (COUNT(CASE WHEN tl.is_exception = true THEN 1 END) * 100.0 / NULLIF(COUNT(tl.id), 0)) as exception_rate
      FROM trip_logs tl
      ${dateFilter}
    `;

    // Exception types (simulated as we don't have exception types in schema)
    const exceptionsQuery = `
      SELECT 
        tl.id,
        tl.trip_number,
        tl.exception_reason,
        tl.is_exception,
        tl.exception_approved_by,
        tl.exception_approved_at,
        tl.created_at,
        dt.truck_number,
        d.full_name as driver_name,
        d.employee_id
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      JOIN drivers d ON a.driver_id = d.id
      ${dateFilter} AND tl.is_exception = true
      ORDER BY tl.created_at DESC
      LIMIT 50
    `;

    // Daily exception trends
    const trendsQuery = `
      SELECT 
        DATE(tl.created_at) as date,
        COUNT(CASE WHEN tl.is_exception = true THEN 1 END) as exception_count
      FROM trip_logs tl
      ${dateFilter}
      GROUP BY DATE(tl.created_at)
      ORDER BY DATE(tl.created_at)
    `;

    const [summaryResult, exceptionsResult, trendsResult] = await Promise.all([
      query(summaryQuery, queryParams),
      query(exceptionsQuery, queryParams),
      query(trendsQuery, queryParams)
    ]);

    const summary = summaryResult.rows[0];

    const exceptionsData = {
      summary: {
        total: parseInt(summary.total_exceptions) || 0,
        pending: parseInt(summary.pending_exceptions) || 0,
        resolved: parseInt(summary.resolved_exceptions) || 0,
        rate: summary.exception_rate ? parseFloat(summary.exception_rate).toFixed(1) : '0.0'
      },
      recent: exceptionsResult.rows.map(row => ({
        id: row.id,
        tripNumber: row.trip_number,
        reason: row.exception_reason || 'No reason provided',
        truckNumber: row.truck_number,
        driverName: row.driver_name,
        employeeId: row.employee_id,
        status: row.exception_approved_by ? 'resolved' : 'pending',
        createdAt: row.created_at,
        resolvedAt: row.exception_approved_at
      })),
      trends: trendsResult.rows.map(row => ({
        date: row.date,
        count: parseInt(row.exception_count) || 0
      }))
    };

    res.json({
      success: true,
      data: exceptionsData
    });

  } catch (error) {
    console.error('Get exceptions analytics error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve exceptions analytics'
    });
  }
});

// @route   GET /api/analytics/trips
// @desc    Get trip metrics analytics data
// @access  Private
router.get('/trips', auth, async (req, res) => {
  try {
    const { start_date, end_date, truck_id, driver_id } = req.query;

    let whereConditions = [];
    let queryParams = [];
    let paramCount = 0;

    // Date filter
    if (start_date && end_date) {
      paramCount += 2;
      whereConditions.push(`tl.created_at BETWEEN $${paramCount - 1} AND $${paramCount}`);
      queryParams.push(start_date, end_date);
    } else {
      paramCount++;
      whereConditions.push(`tl.created_at >= $${paramCount}`);
      queryParams.push(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString());
    }

    // Truck filter
    if (truck_id) {
      paramCount++;
      whereConditions.push(`a.truck_id = $${paramCount}`);
      queryParams.push(parseInt(truck_id));
    }

    // Driver filter
    if (driver_id) {
      paramCount++;
      whereConditions.push(`a.driver_id = $${paramCount}`);
      queryParams.push(parseInt(driver_id));
    }

    const whereClause = `WHERE ${whereConditions.join(' AND ')}`;

    const metricsQuery = `
      SELECT 
        COUNT(*) as total_trips,
        COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) as completed_trips,
        AVG(tl.total_duration_minutes) as avg_total_duration,
        AVG(tl.loading_duration_minutes) as avg_loading_duration,
        AVG(tl.travel_duration_minutes) as avg_travel_duration,
        AVG(tl.unloading_duration_minutes) as avg_unloading_duration,
        MIN(tl.total_duration_minutes) as min_duration,
        MAX(tl.total_duration_minutes) as max_duration
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      ${whereClause}
    `;

    const result = await query(metricsQuery, queryParams);
    const metrics = result.rows[0];

    const tripMetrics = {
      totalTrips: parseInt(metrics.total_trips) || 0,
      completedTrips: parseInt(metrics.completed_trips) || 0,
      avgTotalDuration: metrics.avg_total_duration ? Math.round(parseFloat(metrics.avg_total_duration)) : 0,
      avgLoadingDuration: metrics.avg_loading_duration ? Math.round(parseFloat(metrics.avg_loading_duration)) : 0,
      avgTravelDuration: metrics.avg_travel_duration ? Math.round(parseFloat(metrics.avg_travel_duration)) : 0,
      avgUnloadingDuration: metrics.avg_unloading_duration ? Math.round(parseFloat(metrics.avg_unloading_duration)) : 0,
      minDuration: metrics.min_duration ? Math.round(parseFloat(metrics.min_duration)) : 0,
      maxDuration: metrics.max_duration ? Math.round(parseFloat(metrics.max_duration)) : 0,
      completionRate: metrics.total_trips > 0 ? 
        ((parseInt(metrics.completed_trips) / parseInt(metrics.total_trips)) * 100).toFixed(1) : '0.0'
    };

    res.json({
      success: true,
      data: tripMetrics
    });

  } catch (error) {
    console.error('Get trip metrics error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve trip metrics'
    });
  }
});

// @route   GET /api/analytics/routes
// @desc    Get route performance analytics data
// @access  Private
router.get('/routes', auth, async (req, res) => {
  try {
    const { start_date, end_date } = req.query;

    let dateFilter = '';
    const queryParams = [];
    
    if (start_date && end_date) {
      dateFilter = 'WHERE tl.created_at BETWEEN $1 AND $2';
      queryParams.push(start_date, end_date);
    } else {
      dateFilter = 'WHERE tl.created_at >= CURRENT_DATE - INTERVAL \'30 days\'';
    }

    // Route performance analysis
    const routePerformanceQuery = `
      SELECT 
        CONCAT(l1.name, ' → ', l2.name) as route,
        COUNT(tl.id) as trip_count,
        AVG(tl.total_duration_minutes) as avg_duration,
        AVG(tl.travel_duration_minutes) as avg_travel_time,
        (COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) * 100.0 / NULLIF(COUNT(tl.id), 0)) as completion_rate,
        (COUNT(CASE WHEN tl.is_exception = false AND tl.status = 'trip_completed' THEN 1 END) * 100.0 / NULLIF(COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END), 0)) as efficiency_rate
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN locations l1 ON a.loading_location_id = l1.id
      JOIN locations l2 ON a.unloading_location_id = l2.id
      ${dateFilter}
      GROUP BY l1.name, l2.name, l1.id, l2.id
      HAVING COUNT(tl.id) >= 3
      ORDER BY trip_count DESC, avg_duration ASC
      LIMIT 15
    `;

    const routeResult = await query(routePerformanceQuery, queryParams);

    const routeData = {
      performance: routeResult.rows.map(row => ({
        route: row.route,
        avgTime: row.avg_duration ? Math.round(parseFloat(row.avg_duration)) : 0,
        trips: parseInt(row.trip_count) || 0,
        efficiency: row.efficiency_rate ? Math.round(parseFloat(row.efficiency_rate)) : 0,
        completionRate: row.completion_rate ? parseFloat(row.completion_rate).toFixed(1) : '0.0'
      }))
    };

    res.json({
      success: true,
      data: routeData
    });

  } catch (error) {
    console.error('Get route analytics error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve route analytics'
    });
  }
});

// @route   GET /api/analytics/assignment-trends
// @desc    Get assignment creation trends data
// @access  Private
router.get('/assignment-trends', auth, async (req, res) => {
  try {
    const { period = '30d' } = req.query;
    
    // Convert period to days for SQL query
    let days = 30;
    if (period === '7d') days = 7;
    else if (period === '30d') days = 30;
    else if (period === '90d') days = 90;
    
    // SQL query to get assignment creation trends
    const trendsQuery = `
      SELECT
        DATE_TRUNC('day', created_at)::DATE AS "date",
        COUNT(id) AS "count"
      FROM
        assignments
      WHERE
        created_at >= NOW() - INTERVAL '${days} day'
      GROUP BY
        "date"
      ORDER BY
        "date" ASC;
    `;

    const result = await query(trendsQuery);
    
    // Format the response
    const data = result.rows.map(row => ({
      date: row.date,
      count: parseInt(row.count) || 0
    }));

    res.json({
      success: true,
      data: data
    });

  } catch (error) {
    console.error('Get assignment trends error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve assignment trends'
    });
  }
});

// @route   GET /api/analytics/truck-rankings
// @desc    Get top dump trucks performance rankings
// @access  Private
router.get('/truck-rankings', auth, async (req, res) => {
  try {
    const { start_date, end_date } = req.query;

    let dateFilter = '';
    const queryParams = [];
    
    if (start_date && end_date) {
      dateFilter = 'WHERE tl.created_at BETWEEN $1 AND $2';
      queryParams.push(start_date, end_date);
    } else {
      dateFilter = 'WHERE tl.created_at >= CURRENT_DATE - INTERVAL \'30 days\'';
    }

    // Query for truck performance rankings
    const truckRankingsQuery = `
      SELECT
        dt.truck_number,
        dt.id as truck_id,
        d.full_name as driver_name,
        d.employee_id,
        COUNT(tl.id) as total_completed_trips,
        AVG(tl.total_duration_minutes) as avg_trip_duration_minutes,
        AVG(tl.loading_duration_minutes) as avg_loading_duration_minutes,
        AVG(tl.travel_duration_minutes) as avg_travel_duration_minutes,
        AVG(tl.unloading_duration_minutes) as avg_unloading_duration_minutes,
        MIN(tl.total_duration_minutes) as min_trip_duration,
        MAX(tl.total_duration_minutes) as max_trip_duration,
        COUNT(CASE WHEN tl.is_exception = true THEN 1 END) as exception_count,
        (COUNT(CASE WHEN tl.is_exception = true THEN 1 END) * 100.0 / NULLIF(COUNT(tl.id), 0)) as exception_rate,
        -- Performance score calculation (higher is better)
        ROUND(
          (
            -- Base score from trip completion (40%)
            (COUNT(tl.id) * 0.4) +
            -- Efficiency score based on average duration (30%, inverted so lower duration = higher score)
            (CASE
              WHEN AVG(tl.total_duration_minutes) > 0 THEN
                (300 - LEAST(AVG(tl.total_duration_minutes), 300)) * 0.3 / 300 * 100
              ELSE 0
            END) +
            -- Exception rate score (30%, inverted so lower exception rate = higher score)
            ((100 - LEAST(COUNT(CASE WHEN tl.is_exception = true THEN 1 END) * 100.0 / NULLIF(COUNT(tl.id), 0), 100)) * 0.3)
          ), 2
        ) as performance_score
      FROM dump_trucks dt
      JOIN assignments a ON dt.id = a.truck_id
      JOIN drivers d ON a.driver_id = d.id
      JOIN trip_logs tl ON a.id = tl.assignment_id
      ${dateFilter}
      AND tl.status = 'trip_completed'
      AND dt.status = 'active'
      AND d.status = 'active'
      GROUP BY dt.id, dt.truck_number, d.full_name, d.employee_id
      HAVING COUNT(tl.id) >= 1  -- Only include trucks with at least 1 completed trip
      ORDER BY performance_score DESC, total_completed_trips DESC
      LIMIT 20
    `;

    const result = await query(truckRankingsQuery, queryParams);

    // Format the response
    const truckRankings = result.rows.map((row, index) => ({
      rank: index + 1,
      truckNumber: row.truck_number,
      truckId: parseInt(row.truck_id),
      driverName: row.driver_name,
      employeeId: row.employee_id,
      totalCompletedTrips: parseInt(row.total_completed_trips) || 0,
      avgTripDuration: row.avg_trip_duration_minutes ? Math.round(parseFloat(row.avg_trip_duration_minutes)) : 0,
      avgLoadingDuration: row.avg_loading_duration_minutes ? Math.round(parseFloat(row.avg_loading_duration_minutes)) : 0,
      avgTravelDuration: row.avg_travel_duration_minutes ? Math.round(parseFloat(row.avg_travel_duration_minutes)) : 0,
      avgUnloadingDuration: row.avg_unloading_duration_minutes ? Math.round(parseFloat(row.avg_unloading_duration_minutes)) : 0,
      minTripDuration: row.min_trip_duration ? Math.round(parseFloat(row.min_trip_duration)) : 0,
      maxTripDuration: row.max_trip_duration ? Math.round(parseFloat(row.max_trip_duration)) : 0,
      exceptionCount: parseInt(row.exception_count) || 0,
      exceptionRate: row.exception_rate ? parseFloat(row.exception_rate).toFixed(1) : '0.0',
      performanceScore: parseFloat(row.performance_score) || 0
    }));

    res.json({
      success: true,
      data: {
        rankings: truckRankings,
        totalTrucks: truckRankings.length,
        period: {
          start: start_date || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          end: end_date || new Date().toISOString().split('T')[0]
        }
      }
    });

  } catch (error) {
    console.error('Get truck rankings error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve truck performance rankings'
    });
  }
});

// ============================================================================
// ANALYTICS & REPORTS SYSTEM - NEW ENDPOINTS
// ============================================================================

// @route   GET /api/analytics/fleet-overview
// @desc    Get comprehensive fleet overview data for Analytics & Reports Tab 1
// @access  Private
router.get('/fleet-overview', auth, async (req, res) => {
  try {
    // Real-time fleet metrics
    const fleetMetricsQuery = `
      SELECT
        -- Fleet Status Counts
        (SELECT COUNT(*) FROM dump_trucks WHERE status = 'active') as total_active_trucks,
        (SELECT COUNT(*) FROM dump_trucks) as total_trucks,
        (SELECT COUNT(*) FROM drivers WHERE status = 'active') as total_active_drivers,

        -- Current Trip Status Counts
        (SELECT COUNT(*) FROM trip_logs tl
         JOIN assignments a ON tl.assignment_id = a.id
         WHERE tl.status IN ('loading_start', 'loading_end', 'unloading_start', 'unloading_end')) as trucks_in_operation,

        (SELECT COUNT(*) FROM trip_logs tl
         WHERE tl.status IN ('stopped', 'breakdown') AND tl.breakdown_resolved_at IS NULL) as trucks_in_stopped,

        -- Today's Performance
        (SELECT COUNT(*) FROM trip_logs WHERE DATE(created_at) = CURRENT_DATE) as today_total_trips,
        (SELECT COUNT(*) FROM trip_logs WHERE DATE(created_at) = CURRENT_DATE AND status = 'trip_completed') as today_completed_trips,
        (SELECT COUNT(*) FROM trip_logs WHERE DATE(created_at) = CURRENT_DATE AND status IN ('stopped', 'breakdown')) as today_stopped,

        -- Current Phase Distribution
        (SELECT COUNT(*) FROM trip_logs WHERE status = 'loading_start') as trucks_loading,
        (SELECT COUNT(*) FROM trip_logs WHERE status = 'loading_end') as trucks_traveling_to_unload,
        (SELECT COUNT(*) FROM trip_logs WHERE status = 'unloading_start') as trucks_unloading,
        (SELECT COUNT(*) FROM trip_logs WHERE status = 'unloading_end') as trucks_traveling_to_load,

        -- Performance Metrics
        (SELECT AVG(total_duration_minutes) FROM trip_logs
         WHERE status = 'trip_completed' AND DATE(created_at) = CURRENT_DATE) as today_avg_trip_time,

        (SELECT COUNT(CASE WHEN status = 'trip_completed' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0)
         FROM trip_logs WHERE DATE(created_at) = CURRENT_DATE) as today_completion_rate
    `;

    // Fleet utilization trends (last 7 days)
    const utilizationTrendsQuery = `
      SELECT
        DATE(created_at) as date,
        COUNT(*) as total_trips,
        COUNT(CASE WHEN status = 'trip_completed' THEN 1 END) as completed_trips,
        AVG(total_duration_minutes) as avg_duration,
        COUNT(CASE WHEN status IN ('stopped', 'breakdown') THEN 1 END) as stopped_count
      FROM trip_logs
      WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `;

    // Alert counts
    const alertsQuery = `
      SELECT
        -- Overdue trips (in phase > 2 hours)
        (SELECT COUNT(*) FROM trip_logs tl
         WHERE tl.status IN ('loading_start', 'loading_end', 'unloading_start', 'unloading_end')
         AND (
           (tl.status = 'loading_start' AND tl.loading_start_time < NOW() - INTERVAL '2 hours') OR
           (tl.status = 'loading_end' AND tl.loading_end_time < NOW() - INTERVAL '2 hours') OR
           (tl.status = 'unloading_start' AND tl.unloading_start_time < NOW() - INTERVAL '2 hours') OR
           (tl.status = 'unloading_end' AND tl.unloading_end_time < NOW() - INTERVAL '2 hours')
         )) as overdue_trips,

        -- Active stopped trips
        (SELECT COUNT(*) FROM trip_logs
         WHERE status IN ('stopped', 'breakdown') AND breakdown_resolved_at IS NULL) as active_stopped,

        -- Exception trips today
        (SELECT COUNT(*) FROM trip_logs
         WHERE is_exception = true AND DATE(created_at) = CURRENT_DATE) as today_exceptions
    `;

    const [metricsResult, trendsResult, alertsResult] = await Promise.all([
      query(fleetMetricsQuery),
      query(utilizationTrendsQuery),
      query(alertsQuery)
    ]);

    const metrics = metricsResult.rows[0];
    const trends = trendsResult.rows;
    const alerts = alertsResult.rows[0];

    // Calculate fleet utilization percentage
    const fleetUtilization = metrics.total_active_trucks > 0
      ? Math.round((metrics.trucks_in_operation / metrics.total_active_trucks) * 100)
      : 0;

    res.json({
      success: true,
      data: {
        metrics: {
          totalTrucks: parseInt(metrics.total_trucks) || 0,
          activeTrucks: parseInt(metrics.total_active_trucks) || 0,
          activeDrivers: parseInt(metrics.total_active_drivers) || 0,
          trucksInOperation: parseInt(metrics.trucks_in_operation) || 0,
          trucksInStopped: parseInt(metrics.trucks_in_stopped) || 0,
          fleetUtilization: fleetUtilization,
          todayTrips: parseInt(metrics.today_total_trips) || 0,
          todayCompleted: parseInt(metrics.today_completed_trips) || 0,
          todayStopped: parseInt(metrics.today_stopped) || 0,
          todayAvgTripTime: metrics.today_avg_trip_time ? Math.round(parseFloat(metrics.today_avg_trip_time)) : 0,
          todayCompletionRate: metrics.today_completion_rate ? parseFloat(metrics.today_completion_rate).toFixed(1) : '0.0'
        },
        phaseDistribution: {
          loading: parseInt(metrics.trucks_loading) || 0,
          travelingToUnload: parseInt(metrics.trucks_traveling_to_unload) || 0,
          unloading: parseInt(metrics.trucks_unloading) || 0,
          travelingToLoad: parseInt(metrics.trucks_traveling_to_load) || 0
        },
        trends: trends.map(row => ({
          date: row.date,
          totalTrips: parseInt(row.total_trips) || 0,
          completedTrips: parseInt(row.completed_trips) || 0,
          avgDuration: row.avg_duration ? Math.round(parseFloat(row.avg_duration)) : 0,
          stoppedCount: parseInt(row.stopped_count) || 0,
          completionRate: row.total_trips > 0
            ? ((parseInt(row.completed_trips) / parseInt(row.total_trips)) * 100).toFixed(1)
            : '0.0'
        })),
        alerts: {
          overdueTrips: parseInt(alerts.overdue_trips) || 0,
          activeStopped: parseInt(alerts.active_stopped) || 0,
          todayExceptions: parseInt(alerts.today_exceptions) || 0
        }
      }
    });

  } catch (error) {
    console.error('Fleet overview analytics error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve fleet overview analytics'
    });
  }
});

// @route   GET /api/analytics/fleet-status
// @desc    Get detailed fleet status grid for Analytics & Reports Tab 1
// @access  Private
router.get('/fleet-status', auth, async (req, res) => {
  try {
    const fleetStatusQuery = `
      SELECT
        dt.truck_number,
        dt.id as truck_id,
        d.full_name as driver_name,
        d.employee_id as driver_id,
        a.assignment_code,
        tl.status as trip_status,
        tl.trip_number,

        -- Current location determination
        CASE
          WHEN tl.status IN ('assigned', 'loading_start') THEN
            COALESCE(al.name, ll.name, 'Unknown')
          WHEN tl.status = 'loading_end' THEN 'En Route to Unloading'
          WHEN tl.status IN ('unloading_start', 'unloading_end') THEN
            COALESCE(aul.name, ul.name, 'Unknown')
          WHEN tl.status IN ('stopped', 'breakdown') THEN
            CONCAT('Stopped at ', COALESCE(al.name, aul.name, 'Unknown Location'))
          ELSE 'Unknown'
        END as current_location,

        -- Time in current phase
        CASE
          WHEN tl.status = 'loading_start' AND tl.loading_start_time IS NOT NULL THEN
            EXTRACT(EPOCH FROM (NOW() - tl.loading_start_time))/60
          WHEN tl.status = 'loading_end' AND tl.loading_end_time IS NOT NULL THEN
            EXTRACT(EPOCH FROM (NOW() - tl.loading_end_time))/60
          WHEN tl.status = 'unloading_start' AND tl.unloading_start_time IS NOT NULL THEN
            EXTRACT(EPOCH FROM (NOW() - tl.unloading_start_time))/60
          WHEN tl.status = 'unloading_end' AND tl.unloading_end_time IS NOT NULL THEN
            EXTRACT(EPOCH FROM (NOW() - tl.unloading_end_time))/60
          WHEN tl.status IN ('stopped', 'breakdown') AND tl.breakdown_reported_at IS NOT NULL THEN
            EXTRACT(EPOCH FROM (NOW() - tl.breakdown_reported_at))/60
          ELSE 0
        END as time_in_phase_minutes,

        -- Status indicators
        CASE
          WHEN tl.status IN ('stopped', 'breakdown') THEN 'stopped'
          WHEN tl.status IN ('loading_start', 'loading_end', 'unloading_start', 'unloading_end') THEN 'active'
          WHEN tl.status = 'trip_completed' THEN 'completed'
          WHEN tl.status = 'assigned' THEN 'assigned'
          ELSE 'idle'
        END as status_category,

        -- Performance indicators
        tl.total_duration_minutes,
        tl.is_exception,
        tl.breakdown_reason,

        -- Assignment details
        ll.name as loading_location,
        ul.name as unloading_location,
        a.priority,

        -- Timestamps for ETA calculations
        tl.loading_start_time,
        tl.loading_end_time,
        tl.unloading_start_time,
        tl.unloading_end_time,
        tl.created_at as trip_created_at

      FROM dump_trucks dt
      LEFT JOIN assignments a ON dt.id = a.truck_id AND a.status IN ('assigned', 'in_progress')
      LEFT JOIN drivers d ON a.driver_id = d.id
      LEFT JOIN trip_logs tl ON a.id = tl.assignment_id
        AND tl.id = (
          SELECT id FROM trip_logs tl2
          WHERE tl2.assignment_id = a.id
          ORDER BY tl2.created_at DESC
          LIMIT 1
        )
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN locations al ON tl.actual_loading_location_id = al.id
      LEFT JOIN locations aul ON tl.actual_unloading_location_id = aul.id
      WHERE dt.status = 'active'
      ORDER BY
        CASE
          WHEN tl.status IN ('stopped', 'breakdown') THEN 1
          WHEN tl.status IN ('loading_start', 'loading_end', 'unloading_start', 'unloading_end') THEN 2
          WHEN tl.status = 'assigned' THEN 3
          ELSE 4
        END,
        dt.truck_number
    `;

    const result = await query(fleetStatusQuery);

    const fleetStatus = result.rows.map(row => ({
      truckId: row.truck_id,
      truckNumber: row.truck_number,
      driverName: row.driver_name || 'Unassigned',
      driverId: row.driver_id || null,
      assignmentCode: row.assignment_code || null,
      tripStatus: row.trip_status || 'idle',
      tripNumber: row.trip_number || null,
      currentLocation: row.current_location,
      timeInPhaseMinutes: Math.round(parseFloat(row.time_in_phase_minutes) || 0),
      statusCategory: row.status_category,
      totalDuration: row.total_duration_minutes ? Math.round(parseFloat(row.total_duration_minutes)) : null,
      isException: row.is_exception || false,
      stoppedReason: row.breakdown_reason || null,
      loadingLocation: row.loading_location,
      unloadingLocation: row.unloading_location,
      priority: row.priority || 'normal',
      timestamps: {
        loadingStart: row.loading_start_time,
        loadingEnd: row.loading_end_time,
        unloadingStart: row.unloading_start_time,
        unloadingEnd: row.unloading_end_time,
        tripCreated: row.trip_created_at
      }
    }));

    res.json({
      success: true,
      data: {
        fleetStatus: fleetStatus,
        totalTrucks: fleetStatus.length,
        lastUpdated: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Fleet status analytics error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve fleet status data'
    });
  }
});

// @route   GET /api/analytics/trip-performance
// @desc    Get comprehensive trip performance analytics for Analytics & Reports Tab 2
// @access  Private
router.get('/trip-performance', auth, async (req, res) => {
  try {
    const { start_date, end_date } = req.query;

    // Date filter setup
    let dateFilter = '';
    let queryParams = [];

    if (start_date && end_date) {
      dateFilter = 'WHERE tl.created_at BETWEEN $1 AND $2';
      queryParams = [start_date, end_date];
    } else {
      dateFilter = 'WHERE tl.created_at >= $1';
      queryParams = [new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()];
    }

    // Phase-by-phase duration analysis
    const phaseAnalysisQuery = `
      SELECT
        -- Loading Phase Analysis
        AVG(tl.loading_duration_minutes) as avg_loading_duration,
        PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY tl.loading_duration_minutes) as median_loading_duration,
        MIN(tl.loading_duration_minutes) as min_loading_duration,
        MAX(tl.loading_duration_minutes) as max_loading_duration,

        -- Travel Phase Analysis
        AVG(tl.travel_duration_minutes) as avg_travel_duration,
        PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY tl.travel_duration_minutes) as median_travel_duration,
        MIN(tl.travel_duration_minutes) as min_travel_duration,
        MAX(tl.travel_duration_minutes) as max_travel_duration,

        -- Unloading Phase Analysis
        AVG(tl.unloading_duration_minutes) as avg_unloading_duration,
        PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY tl.unloading_duration_minutes) as median_unloading_duration,
        MIN(tl.unloading_duration_minutes) as min_unloading_duration,
        MAX(tl.unloading_duration_minutes) as max_unloading_duration,

        -- Overall Performance
        AVG(tl.total_duration_minutes) as avg_total_duration,
        COUNT(*) as total_trips,
        COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) as completed_trips,
        COUNT(CASE WHEN tl.status IN ('stopped', 'breakdown') THEN 1 END) as stopped_trips,
        COUNT(CASE WHEN tl.is_exception = true THEN 1 END) as exception_trips

      FROM trip_logs tl
      ${dateFilter}
      AND tl.status IN ('trip_completed', 'stopped', 'breakdown')
    `;

    // Location-based performance analysis
    const locationPerformanceQuery = `
      SELECT
        'loading' as location_type,
        COALESCE(al.name, ll.name) as location_name,
        COALESCE(al.id, ll.id) as location_id,
        COUNT(*) as trip_count,
        AVG(tl.loading_duration_minutes) as avg_duration,
        AVG(tl.total_duration_minutes) as avg_total_trip_time,
        COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) as completed_count,
        COUNT(CASE WHEN tl.is_exception = true THEN 1 END) as exception_count
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations al ON tl.actual_loading_location_id = al.id
      ${dateFilter}
      GROUP BY COALESCE(al.name, ll.name), COALESCE(al.id, ll.id)

      UNION ALL

      SELECT
        'unloading' as location_type,
        COALESCE(aul.name, ul.name) as location_name,
        COALESCE(aul.id, ul.id) as location_id,
        COUNT(*) as trip_count,
        AVG(tl.unloading_duration_minutes) as avg_duration,
        AVG(tl.total_duration_minutes) as avg_total_trip_time,
        COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) as completed_count,
        COUNT(CASE WHEN tl.is_exception = true THEN 1 END) as exception_count
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN locations aul ON tl.actual_unloading_location_id = aul.id
      ${dateFilter}
      GROUP BY COALESCE(aul.name, ul.name), COALESCE(aul.id, ul.id)

      ORDER BY trip_count DESC
      LIMIT 20
    `;

    // Route pattern analysis
    const routePatternQuery = `
      SELECT
        CONCAT(COALESCE(al.name, ll.name), ' → ', COALESCE(aul.name, ul.name)) as route,
        COUNT(*) as trip_count,
        AVG(tl.total_duration_minutes) as avg_duration,
        AVG(tl.travel_duration_minutes) as avg_travel_time,
        COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) as completed_count,
        COUNT(CASE WHEN tl.is_exception = true THEN 1 END) as exception_count,

        -- Workflow type detection (A→B→A vs A→B→C)
        CASE
          WHEN COALESCE(al.id, ll.id) = COALESCE(aul.id, ul.id) THEN 'Same Location'
          ELSE 'Different Locations'
        END as workflow_type

      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN locations al ON tl.actual_loading_location_id = al.id
      LEFT JOIN locations aul ON tl.actual_unloading_location_id = aul.id
      ${dateFilter}
      GROUP BY
        CONCAT(COALESCE(al.name, ll.name), ' → ', COALESCE(aul.name, ul.name)),
        COALESCE(al.id, ll.id),
        COALESCE(aul.id, ul.id)
      HAVING COUNT(*) >= 3
      ORDER BY trip_count DESC, avg_duration ASC
      LIMIT 15
    `;

    const [phaseResult, locationResult, routeResult] = await Promise.all([
      query(phaseAnalysisQuery, queryParams),
      query(locationPerformanceQuery, queryParams),
      query(routePatternQuery, queryParams)
    ]);

    const phaseAnalysis = phaseResult.rows[0];
    const locationPerformance = locationResult.rows;
    const routePatterns = routeResult.rows;

    res.json({
      success: true,
      data: {
        phaseAnalysis: {
          loading: {
            average: phaseAnalysis.avg_loading_duration ? Math.round(parseFloat(phaseAnalysis.avg_loading_duration)) : 0,
            median: phaseAnalysis.median_loading_duration ? Math.round(parseFloat(phaseAnalysis.median_loading_duration)) : 0,
            min: phaseAnalysis.min_loading_duration ? Math.round(parseFloat(phaseAnalysis.min_loading_duration)) : 0,
            max: phaseAnalysis.max_loading_duration ? Math.round(parseFloat(phaseAnalysis.max_loading_duration)) : 0
          },
          travel: {
            average: phaseAnalysis.avg_travel_duration ? Math.round(parseFloat(phaseAnalysis.avg_travel_duration)) : 0,
            median: phaseAnalysis.median_travel_duration ? Math.round(parseFloat(phaseAnalysis.median_travel_duration)) : 0,
            min: phaseAnalysis.min_travel_duration ? Math.round(parseFloat(phaseAnalysis.min_travel_duration)) : 0,
            max: phaseAnalysis.max_travel_duration ? Math.round(parseFloat(phaseAnalysis.max_travel_duration)) : 0
          },
          unloading: {
            average: phaseAnalysis.avg_unloading_duration ? Math.round(parseFloat(phaseAnalysis.avg_unloading_duration)) : 0,
            median: phaseAnalysis.median_unloading_duration ? Math.round(parseFloat(phaseAnalysis.median_unloading_duration)) : 0,
            min: phaseAnalysis.min_unloading_duration ? Math.round(parseFloat(phaseAnalysis.min_unloading_duration)) : 0,
            max: phaseAnalysis.max_unloading_duration ? Math.round(parseFloat(phaseAnalysis.max_unloading_duration)) : 0
          },
          overall: {
            totalTrips: parseInt(phaseAnalysis.total_trips) || 0,
            completedTrips: parseInt(phaseAnalysis.completed_trips) || 0,
            stoppedTrips: parseInt(phaseAnalysis.stopped_trips) || 0,
            exceptionTrips: parseInt(phaseAnalysis.exception_trips) || 0,
            avgTotalDuration: phaseAnalysis.avg_total_duration ? Math.round(parseFloat(phaseAnalysis.avg_total_duration)) : 0,
            completionRate: phaseAnalysis.total_trips > 0
              ? ((parseInt(phaseAnalysis.completed_trips) / parseInt(phaseAnalysis.total_trips)) * 100).toFixed(1)
              : '0.0'
          }
        },
        locationPerformance: locationPerformance.map(row => ({
          locationType: row.location_type,
          locationName: row.location_name,
          locationId: row.location_id,
          tripCount: parseInt(row.trip_count) || 0,
          avgDuration: row.avg_duration ? Math.round(parseFloat(row.avg_duration)) : 0,
          avgTotalTripTime: row.avg_total_trip_time ? Math.round(parseFloat(row.avg_total_trip_time)) : 0,
          completedCount: parseInt(row.completed_count) || 0,
          exceptionCount: parseInt(row.exception_count) || 0,
          efficiency: row.trip_count > 0
            ? ((parseInt(row.completed_count) / parseInt(row.trip_count)) * 100).toFixed(1)
            : '0.0'
        })),
        routePatterns: routePatterns.map(row => ({
          route: row.route,
          tripCount: parseInt(row.trip_count) || 0,
          avgDuration: row.avg_duration ? Math.round(parseFloat(row.avg_duration)) : 0,
          avgTravelTime: row.avg_travel_time ? Math.round(parseFloat(row.avg_travel_time)) : 0,
          completedCount: parseInt(row.completed_count) || 0,
          exceptionCount: parseInt(row.exception_count) || 0,
          workflowType: row.workflow_type,
          efficiency: row.trip_count > 0
            ? ((parseInt(row.completed_count) / parseInt(row.trip_count)) * 100).toFixed(1)
            : '0.0'
        }))
      }
    });

  } catch (error) {
    console.error('Trip performance analytics error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve trip performance analytics'
    });
  }
});

// @route   GET /api/analytics/stopped-analytics
// @desc    Get comprehensive stopped analytics for Analytics & Reports Tab 2
// @access  Private
router.get('/stopped-analytics', auth, async (req, res) => {
  try {
    const { start_date, end_date } = req.query;

    let dateFilter = '';
    let queryParams = [];

    if (start_date && end_date) {
      dateFilter = 'WHERE tl.breakdown_reported_at BETWEEN $1 AND $2';
      queryParams = [start_date, end_date];
    } else {
      dateFilter = 'WHERE tl.breakdown_reported_at >= $1';
      queryParams = [new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()];
    }

    // Stopped frequency analysis
    const stoppedFrequencyQuery = `
      SELECT
        dt.truck_number,
        dt.id as truck_id,
        d.full_name as driver_name,
        COUNT(*) as stopped_count,
        AVG(EXTRACT(EPOCH FROM (tl.breakdown_resolved_at - tl.breakdown_reported_at))/60) as avg_resolution_time,
        MAX(tl.breakdown_reported_at) as last_stopped,

        -- Stopped by trip phase
        COUNT(CASE WHEN tl.previous_status = 'loading_start' THEN 1 END) as loading_stopped,
        COUNT(CASE WHEN tl.previous_status = 'loading_end' THEN 1 END) as travel_to_unload_stopped,
        COUNT(CASE WHEN tl.previous_status = 'unloading_start' THEN 1 END) as unloading_stopped,
        COUNT(CASE WHEN tl.previous_status = 'unloading_end' THEN 1 END) as travel_to_load_stopped

      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      ${dateFilter}
      AND tl.status IN ('stopped', 'breakdown')
      GROUP BY dt.truck_number, dt.id, d.full_name
      ORDER BY stopped_count DESC, avg_resolution_time DESC
    `;

    // Stopped patterns by location
    const locationStoppedQuery = `
      SELECT
        COALESCE(al.name, aul.name, ll.name, ul.name, 'Unknown') as location_name,
        COUNT(*) as stopped_count,
        AVG(EXTRACT(EPOCH FROM (tl.breakdown_resolved_at - tl.breakdown_reported_at))/60) as avg_resolution_time,
        tl.previous_status as phase_when_stopped,

        -- Stopped reasons
        tl.breakdown_reason,
        COUNT(*) OVER (PARTITION BY tl.breakdown_reason) as reason_count

      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN locations al ON tl.actual_loading_location_id = al.id
      LEFT JOIN locations aul ON tl.actual_unloading_location_id = aul.id
      ${dateFilter}
      AND tl.status IN ('stopped', 'breakdown')
      GROUP BY al.name, aul.name, ll.name, ul.name, tl.previous_status, tl.breakdown_reason
      ORDER BY stopped_count DESC, avg_resolution_time DESC
    `;

    // Stopped trends by time
    const stoppedTrendsQuery = `
      SELECT
        DATE(tl.breakdown_reported_at) as stopped_date,
        EXTRACT(HOUR FROM tl.breakdown_reported_at) as stopped_hour,
        COUNT(*) as stopped_count,
        AVG(EXTRACT(EPOCH FROM (tl.breakdown_resolved_at - tl.breakdown_reported_at))/60) as avg_resolution_time,

        -- Most common reasons per day
        MODE() WITHIN GROUP (ORDER BY tl.breakdown_reason) as most_common_reason

      FROM trip_logs tl
      ${dateFilter}
      AND tl.status IN ('stopped', 'breakdown')
      GROUP BY DATE(tl.breakdown_reported_at), EXTRACT(HOUR FROM tl.breakdown_reported_at)
      ORDER BY stopped_date DESC, stopped_hour
    `;

    const [frequencyResult, locationResult, trendsResult] = await Promise.all([
      query(stoppedFrequencyQuery, queryParams),
      query(locationStoppedQuery, queryParams),
      query(stoppedTrendsQuery, queryParams)
    ]);

    res.json({
      success: true,
      data: {
        frequency: frequencyResult.rows.map(row => ({
          truckNumber: row.truck_number,
          truckId: row.truck_id,
          driverName: row.driver_name,
          stoppedCount: parseInt(row.stopped_count) || 0,
          avgResolutionTime: row.avg_resolution_time ? Math.round(parseFloat(row.avg_resolution_time)) : 0,
          lastStopped: row.last_stopped,
          phaseStopped: {
            loading: parseInt(row.loading_stopped) || 0,
            travelToUnload: parseInt(row.travel_to_unload_stopped) || 0,
            unloading: parseInt(row.unloading_stopped) || 0,
            travelToLoad: parseInt(row.travel_to_load_stopped) || 0
          }
        })),
        locationPatterns: locationResult.rows.map(row => ({
          locationName: row.location_name,
          stoppedCount: parseInt(row.stopped_count) || 0,
          avgResolutionTime: row.avg_resolution_time ? Math.round(parseFloat(row.avg_resolution_time)) : 0,
          phaseWhenStopped: row.phase_when_stopped,
          stoppedReason: row.breakdown_reason,
          reasonCount: parseInt(row.reason_count) || 0
        })),
        trends: trendsResult.rows.map(row => ({
          date: row.stopped_date,
          hour: parseInt(row.stopped_hour) || 0,
          stoppedCount: parseInt(row.stopped_count) || 0,
          avgResolutionTime: row.avg_resolution_time ? Math.round(parseFloat(row.avg_resolution_time)) : 0,
          mostCommonReason: row.most_common_reason
        }))
      }
    });

  } catch (error) {
    console.error('Stopped analytics error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve stopped analytics'
    });
  }
});

// @route   GET /api/analytics/live-operations
// @desc    Get real-time operations data for Analytics & Reports Tab 3
// @access  Private
router.get('/live-operations', auth, async (req, res) => {
  try {
    // Live operations dashboard query
    const liveOperationsQuery = `
      SELECT
        dt.truck_number,
        dt.id as truck_id,
        d.full_name as driver_name,
        a.assignment_code,
        tl.status as current_phase,
        tl.trip_number,

        -- Current location with uncertainty indicators
        CASE
          WHEN tl.status IN ('assigned', 'loading_start') THEN
            CASE
              WHEN tl.actual_loading_location_id IS NOT NULL THEN '📍'
              ELSE '❓'
            END || ' ' || COALESCE(al.name, ll.name, 'Unknown')
          WHEN tl.status = 'loading_end' THEN '🚛 En Route to Unloading'
          WHEN tl.status IN ('unloading_start', 'unloading_end') THEN
            CASE
              WHEN tl.actual_unloading_location_id IS NOT NULL THEN '📍'
              ELSE '❓'
            END || ' ' || COALESCE(aul.name, ul.name, 'Unknown')
          WHEN tl.status IN ('stopped', 'breakdown') THEN '⏹️ Stopped'
          ELSE '❓ Unknown'
        END as current_location_display,

        -- ETA calculation based on historical data
        CASE
          WHEN tl.status = 'loading_start' THEN
            (SELECT AVG(loading_duration_minutes) FROM trip_logs
             WHERE status = 'trip_completed' AND loading_duration_minutes IS NOT NULL)
          WHEN tl.status = 'loading_end' THEN
            (SELECT AVG(travel_duration_minutes) FROM trip_logs
             WHERE status = 'trip_completed' AND travel_duration_minutes IS NOT NULL)
          WHEN tl.status = 'unloading_start' THEN
            (SELECT AVG(unloading_duration_minutes) FROM trip_logs
             WHERE status = 'trip_completed' AND unloading_duration_minutes IS NOT NULL)
          WHEN tl.status = 'unloading_end' THEN
            (SELECT AVG(travel_duration_minutes) FROM trip_logs
             WHERE status = 'trip_completed' AND travel_duration_minutes IS NOT NULL)
          ELSE NULL
        END as estimated_minutes_remaining,

        -- Time in current phase
        CASE
          WHEN tl.status = 'loading_start' AND tl.loading_start_time IS NOT NULL THEN
            EXTRACT(EPOCH FROM (NOW() - tl.loading_start_time))/60
          WHEN tl.status = 'loading_end' AND tl.loading_end_time IS NOT NULL THEN
            EXTRACT(EPOCH FROM (NOW() - tl.loading_end_time))/60
          WHEN tl.status = 'unloading_start' AND tl.unloading_start_time IS NOT NULL THEN
            EXTRACT(EPOCH FROM (NOW() - tl.unloading_start_time))/60
          WHEN tl.status = 'unloading_end' AND tl.unloading_end_time IS NOT NULL THEN
            EXTRACT(EPOCH FROM (NOW() - tl.unloading_end_time))/60
          WHEN tl.status IN ('stopped', 'breakdown') AND tl.breakdown_reported_at IS NOT NULL THEN
            EXTRACT(EPOCH FROM (NOW() - tl.breakdown_reported_at))/60
          ELSE 0
        END as time_in_phase_minutes,

        -- Alert conditions
        CASE
          WHEN tl.status IN ('stopped', 'breakdown') THEN 'stopped'
          WHEN (tl.status = 'loading_start' AND tl.loading_start_time < NOW() - INTERVAL '2 hours') THEN 'overdue'
          WHEN (tl.status = 'loading_end' AND tl.loading_end_time < NOW() - INTERVAL '3 hours') THEN 'overdue'
          WHEN (tl.status = 'unloading_start' AND tl.unloading_start_time < NOW() - INTERVAL '2 hours') THEN 'overdue'
          WHEN (tl.status = 'unloading_end' AND tl.unloading_end_time < NOW() - INTERVAL '3 hours') THEN 'overdue'
          WHEN tl.is_exception = true THEN 'exception'
          ELSE 'normal'
        END as alert_status,

        -- Route information
        CONCAT(
          COALESCE(al.name, ll.name, 'Unknown'),
          ' → ',
          COALESCE(aul.name, ul.name, 'Unknown')
        ) as route_display,

        -- Dynamic assignment indicators
        a.is_adaptive,
        a.adaptation_strategy,
        a.adaptation_confidence,

        -- Priority and performance
        a.priority,
        tl.total_duration_minutes,
        tl.is_exception,
        tl.breakdown_reason,

        -- Timestamps for detailed tracking
        tl.loading_start_time,
        tl.loading_end_time,
        tl.unloading_start_time,
        tl.unloading_end_time,
        tl.breakdown_reported_at,
        tl.created_at as trip_started_at

      FROM dump_trucks dt
      LEFT JOIN assignments a ON dt.id = a.truck_id AND a.status IN ('assigned', 'in_progress')
      LEFT JOIN drivers d ON a.driver_id = d.id
      LEFT JOIN trip_logs tl ON a.id = tl.assignment_id
        AND tl.id = (
          SELECT id FROM trip_logs tl2
          WHERE tl2.assignment_id = a.id
          ORDER BY tl2.created_at DESC
          LIMIT 1
        )
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN locations al ON tl.actual_loading_location_id = al.id
      LEFT JOIN locations aul ON tl.actual_unloading_location_id = aul.id
      WHERE dt.status = 'active'
        AND (tl.status IS NULL OR tl.status NOT IN ('trip_completed', 'cancelled'))
      ORDER BY
        CASE
          WHEN tl.status IN ('stopped', 'breakdown') THEN 1
          WHEN tl.status IN ('loading_start', 'loading_end', 'unloading_start', 'unloading_end') THEN 2
          WHEN tl.status = 'assigned' THEN 3
          ELSE 4
        END,
        dt.truck_number
    `;

    const result = await query(liveOperationsQuery);

    // Remove duplicates and ensure unique truck IDs
    const uniqueOperations = new Map();

    result.rows.forEach(row => {
      const truckId = row.truck_id;
      if (truckId && !uniqueOperations.has(truckId)) {
        uniqueOperations.set(truckId, {
          truckId: truckId,
          truckNumber: row.truck_number,
          driverName: row.driver_name || 'Unassigned',
          assignmentCode: row.assignment_code || null,
          currentPhase: row.current_phase || 'idle',
          tripNumber: row.trip_number || null,
          currentLocationDisplay: row.current_location_display,
          routeDisplay: row.route_display,
          timeInPhaseMinutes: Math.round(parseFloat(row.time_in_phase_minutes) || 0),
          estimatedMinutesRemaining: row.estimated_minutes_remaining ? Math.round(parseFloat(row.estimated_minutes_remaining)) : null,
          alertStatus: row.alert_status,
          isDynamicAssignment: row.is_adaptive || false,
          adaptationStrategy: row.adaptation_strategy,
          adaptationConfidence: row.adaptation_confidence,
          priority: row.priority || 'normal',
          totalDuration: row.total_duration_minutes ? Math.round(parseFloat(row.total_duration_minutes)) : null,
          isException: row.is_exception || false,
          stoppedReason: row.breakdown_reason,
          timestamps: {
            loadingStart: row.loading_start_time,
            loadingEnd: row.loading_end_time,
            unloadingStart: row.unloading_start_time,
            unloadingEnd: row.unloading_end_time,
            stoppedReported: row.breakdown_reported_at,
            tripStarted: row.trip_started_at
          }
        });
      }
    });

    const liveOperations = Array.from(uniqueOperations.values());

    // Calculate summary statistics
    const summary = {
      totalActive: liveOperations.length,
      byPhase: {
        loading: liveOperations.filter(op => op.currentPhase === 'loading_start').length,
        travelingToUnload: liveOperations.filter(op => op.currentPhase === 'loading_end').length,
        unloading: liveOperations.filter(op => op.currentPhase === 'unloading_start').length,
        travelingToLoad: liveOperations.filter(op => op.currentPhase === 'unloading_end').length,
        stopped: liveOperations.filter(op => op.currentPhase === 'stopped' || op.currentPhase === 'breakdown').length,
        idle: liveOperations.filter(op => !op.currentPhase || op.currentPhase === 'idle').length
      },
      alerts: {
        stopped: liveOperations.filter(op => op.alertStatus === 'stopped' || op.alertStatus === 'breakdown').length,
        overdue: liveOperations.filter(op => op.alertStatus === 'overdue').length,
        exception: liveOperations.filter(op => op.alertStatus === 'exception').length
      },
      dynamicAssignments: liveOperations.filter(op => op.isDynamicAssignment).length
    };

    res.json({
      success: true,
      data: {
        operations: liveOperations,
        summary: summary,
        lastUpdated: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Live operations analytics error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve live operations data'
    });
  }
});

// @route   GET /api/analytics/truck-rankings
// @desc    Get truck performance rankings for Analytics & Reports Tab 2
// @access  Private
router.get('/truck-rankings', auth, async (req, res) => {
  try {
    const { start_date, end_date } = req.query;

    let dateFilter = '';
    let queryParams = [];

    if (start_date && end_date) {
      dateFilter = 'WHERE tl.created_at BETWEEN $1 AND $2';
      queryParams = [start_date, end_date];
    } else {
      dateFilter = 'WHERE tl.created_at >= $1';
      queryParams = [new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()];
    }

    const rankingsQuery = `
      SELECT
        dt.truck_number,
        dt.id as truck_id,
        d.full_name as driver_name,

        -- Trip counts
        COUNT(*) as trip_count,
        COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) as completed_trips,
        COUNT(CASE WHEN tl.status IN ('stopped', 'breakdown') THEN 1 END) as stopped_trips,
        COUNT(CASE WHEN tl.is_exception = true THEN 1 END) as exception_count,

        -- Performance metrics
        AVG(tl.total_duration_minutes) as avg_trip_duration,
        AVG(tl.loading_duration_minutes) as avg_loading_duration,
        AVG(tl.travel_duration_minutes) as avg_travel_duration,
        AVG(tl.unloading_duration_minutes) as avg_unloading_duration,

        -- Efficiency calculations
        COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0) as completion_rate,

        -- Performance score calculation (weighted average)
        CASE
          WHEN COUNT(*) = 0 THEN 0
          ELSE LEAST(100, GREATEST(0,
            (COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0)) * 0.4 +  -- 40% completion rate
            (100 - LEAST(100, COALESCE(AVG(tl.total_duration_minutes), 0) / 10)) * 0.3 +  -- 30% speed (inverse of duration)
            (100 - COUNT(CASE WHEN tl.is_exception = true THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0)) * 0.2 +  -- 20% exception rate (inverse)
            (100 - COUNT(CASE WHEN tl.status IN ('stopped', 'breakdown') THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0)) * 0.1  -- 10% stopped rate (inverse)
          ))
        END as performance_score

      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      ${dateFilter}
      GROUP BY dt.truck_number, dt.id, d.full_name
      HAVING COUNT(*) >= 1  -- Only include trucks with at least 1 trip
      ORDER BY performance_score DESC, completion_rate DESC, avg_trip_duration ASC
      LIMIT 50
    `;

    const result = await query(rankingsQuery, queryParams);

    const rankings = result.rows.map(row => ({
      truckId: row.truck_id,
      truckNumber: row.truck_number,
      driverName: row.driver_name || 'Unassigned',
      tripCount: parseInt(row.trip_count) || 0,
      completedTrips: parseInt(row.completed_trips) || 0,
      stoppedTrips: parseInt(row.stopped_trips) || 0,
      exceptionCount: parseInt(row.exception_count) || 0,
      avgTripDuration: row.avg_trip_duration ? Math.round(parseFloat(row.avg_trip_duration)) : 0,
      avgLoadingDuration: row.avg_loading_duration ? Math.round(parseFloat(row.avg_loading_duration)) : 0,
      avgTravelDuration: row.avg_travel_duration ? Math.round(parseFloat(row.avg_travel_duration)) : 0,
      avgUnloadingDuration: row.avg_unloading_duration ? Math.round(parseFloat(row.avg_unloading_duration)) : 0,
      completionRate: row.completion_rate ? parseFloat(row.completion_rate).toFixed(1) : '0.0',
      performanceScore: row.performance_score ? Math.round(parseFloat(row.performance_score)) : 0
    }));

    res.json({
      success: true,
      data: {
        rankings: rankings,
        totalTrucks: rankings.length,
        dateRange: {
          start: queryParams[0],
          end: queryParams[1] || new Date().toISOString()
        }
      }
    });

  } catch (error) {
    console.error('Truck rankings analytics error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve truck rankings data'
    });
  }
});

// @route   GET /api/analytics/shift-performance
// @desc    Get shift-based performance analytics
// @access  Private
router.get('/shift-performance', auth, async (req, res) => {
  try {
    const { start_date, end_date, truck_id, shift_type } = req.query;

    let dateFilter = '';
    let queryParams = [];
    let paramIndex = 1;

    // Build date filter
    if (start_date && end_date) {
      dateFilter = `AND tl.created_at BETWEEN $${paramIndex} AND $${paramIndex + 1}`;
      queryParams.push(start_date, end_date);
      paramIndex += 2;
    } else {
      dateFilter = `AND tl.created_at >= $${paramIndex}`;
      queryParams.push(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString());
      paramIndex++;
    }

    // Add truck filter if specified
    let truckFilter = '';
    if (truck_id) {
      truckFilter = `AND dt.id = $${paramIndex}`;
      queryParams.push(truck_id);
      paramIndex++;
    }

    // Add shift type filter if specified
    let shiftTypeFilter = '';
    if (shift_type) {
      shiftTypeFilter = `AND ds.shift_type = $${paramIndex}`;
      queryParams.push(shift_type);
      paramIndex++;
    }

    const shiftPerformanceQuery = `
      WITH shift_trips AS (
        SELECT
          dt.id as truck_id,
          dt.truck_number,
          d.id as driver_id,
          d.full_name as driver_name,
          d.employee_id,
          ds.id as shift_id,
          ds.shift_type,
          ds.shift_date,
          ds.start_time,
          ds.end_time,
          tl.id as trip_id,
          tl.status as trip_status,
          tl.total_duration_minutes,
          tl.loading_duration_minutes,
          tl.travel_duration_minutes,
          tl.unloading_duration_minutes,
          tl.is_exception,
          tl.created_at as trip_created_at
        FROM trip_logs tl
        JOIN assignments a ON tl.assignment_id = a.id
        JOIN dump_trucks dt ON a.truck_id = dt.id
        JOIN drivers d ON a.driver_id = d.id
        LEFT JOIN driver_shifts ds ON a.shift_id = ds.id
        WHERE 1=1
          ${dateFilter}
          ${truckFilter}
          ${shiftTypeFilter}
          AND dt.status = 'active'
          AND d.status = 'active'
      )
      SELECT
        truck_id,
        truck_number,
        driver_id,
        driver_name,
        employee_id,
        shift_type,

        -- Trip counts by shift
        COUNT(*) as total_trips,
        COUNT(CASE WHEN trip_status = 'trip_completed' THEN 1 END) as completed_trips,
        COUNT(CASE WHEN trip_status IN ('stopped', 'breakdown') THEN 1 END) as stopped_trips,
        COUNT(CASE WHEN is_exception = true THEN 1 END) as exception_trips,

        -- Performance metrics by shift
        AVG(total_duration_minutes) as avg_trip_duration,
        AVG(loading_duration_minutes) as avg_loading_duration,
        AVG(travel_duration_minutes) as avg_travel_duration,
        AVG(unloading_duration_minutes) as avg_unloading_duration,

        -- Efficiency by shift
        COUNT(CASE WHEN trip_status = 'trip_completed' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0) as completion_rate,
        COUNT(CASE WHEN is_exception = true THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0) as exception_rate,

        -- Shift-specific metrics
        COUNT(DISTINCT shift_id) as shifts_worked,
        COUNT(DISTINCT shift_date) as days_worked,

        -- Performance score (shift-aware)
        CASE
          WHEN COUNT(*) = 0 THEN 0
          ELSE LEAST(100, GREATEST(0,
            (COUNT(CASE WHEN trip_status = 'trip_completed' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0)) * 0.4 +
            (100 - LEAST(100, COALESCE(AVG(total_duration_minutes), 0) / 10)) * 0.3 +
            (100 - COUNT(CASE WHEN is_exception = true THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0)) * 0.2 +
            (COUNT(CASE WHEN trip_status = 'trip_completed' THEN 1 END) * 10.0 / NULLIF(COUNT(DISTINCT shift_date), 0)) * 0.1
          ))
        END as performance_score

      FROM shift_trips
      GROUP BY truck_id, truck_number, driver_id, driver_name, employee_id, shift_type
      HAVING COUNT(*) >= 1
      ORDER BY performance_score DESC, completed_trips DESC
      LIMIT 50
    `;

    const result = await query(shiftPerformanceQuery, queryParams);

    // Format the results
    const shiftPerformance = result.rows.map(row => ({
      truckId: row.truck_id,
      truckNumber: row.truck_number,
      driverId: row.driver_id,
      driverName: row.driver_name,
      employeeId: row.employee_id,
      shiftType: row.shift_type || 'regular',
      totalTrips: parseInt(row.total_trips) || 0,
      completedTrips: parseInt(row.completed_trips) || 0,
      stoppedTrips: parseInt(row.stopped_trips) || 0,
      exceptionTrips: parseInt(row.exception_trips) || 0,
      avgTripDuration: row.avg_trip_duration ? Math.round(parseFloat(row.avg_trip_duration)) : 0,
      avgLoadingDuration: row.avg_loading_duration ? Math.round(parseFloat(row.avg_loading_duration)) : 0,
      avgTravelDuration: row.avg_travel_duration ? Math.round(parseFloat(row.avg_travel_duration)) : 0,
      avgUnloadingDuration: row.avg_unloading_duration ? Math.round(parseFloat(row.avg_unloading_duration)) : 0,
      completionRate: row.completion_rate ? parseFloat(row.completion_rate).toFixed(1) : '0.0',
      exceptionRate: row.exception_rate ? parseFloat(row.exception_rate).toFixed(1) : '0.0',
      shiftsWorked: parseInt(row.shifts_worked) || 0,
      daysWorked: parseInt(row.days_worked) || 0,
      performanceScore: row.performance_score ? Math.round(parseFloat(row.performance_score)) : 0
    }));

    // Get summary statistics
    const summaryQuery = `
      SELECT
        COUNT(DISTINCT CONCAT(dt.id, '-', d.id, '-', COALESCE(ds.shift_type, 'regular'))) as unique_driver_truck_shifts,
        COUNT(DISTINCT dt.id) as trucks_with_data,
        COUNT(DISTINCT d.id) as drivers_with_data,
        COUNT(DISTINCT ds.shift_type) as shift_types_used,
        AVG(CASE WHEN tl.status = 'trip_completed' THEN 1.0 ELSE 0.0 END) * 100 as overall_completion_rate
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      JOIN drivers d ON a.driver_id = d.id
      LEFT JOIN driver_shifts ds ON a.shift_id = ds.id
      WHERE 1=1
        ${dateFilter}
        ${truckFilter}
        ${shiftTypeFilter}
        AND dt.status = 'active'
        AND d.status = 'active'
    `;

    const summaryResult = await query(summaryQuery, queryParams);
    const summary = summaryResult.rows[0];

    res.json({
      success: true,
      data: {
        shiftPerformance,
        summary: {
          uniqueDriverTruckShifts: parseInt(summary.unique_driver_truck_shifts) || 0,
          trucksWithData: parseInt(summary.trucks_with_data) || 0,
          driversWithData: parseInt(summary.drivers_with_data) || 0,
          shiftTypesUsed: parseInt(summary.shift_types_used) || 0,
          overallCompletionRate: summary.overall_completion_rate ? parseFloat(summary.overall_completion_rate).toFixed(1) : '0.0'
        },
        dateRange: {
          start: queryParams[0] || null,
          end: queryParams[1] || new Date().toISOString()
        }
      }
    });

  } catch (error) {
    console.error('Shift performance analytics error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve shift performance analytics'
    });
  }
});

// @route   GET /api/analytics/driver-shift-comparison
// @desc    Compare driver performance across different shifts
// @access  Private
router.get('/driver-shift-comparison', auth, async (req, res) => {
  try {
    const { start_date, end_date, driver_id } = req.query;

    let dateFilter = '';
    let queryParams = [];
    let paramIndex = 1;

    // Build date filter
    if (start_date && end_date) {
      dateFilter = `AND tl.created_at BETWEEN $${paramIndex} AND $${paramIndex + 1}`;
      queryParams.push(start_date, end_date);
      paramIndex += 2;
    } else {
      dateFilter = `AND tl.created_at >= $${paramIndex}`;
      queryParams.push(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString());
      paramIndex++;
    }

    // Add driver filter if specified
    let driverFilter = '';
    if (driver_id) {
      driverFilter = `AND d.id = $${paramIndex}`;
      queryParams.push(driver_id);
      paramIndex++;
    }

    const comparisonQuery = `
      SELECT
        d.id as driver_id,
        d.full_name as driver_name,
        d.employee_id,
        COALESCE(ds.shift_type, 'regular') as shift_type,

        -- Performance by shift type
        COUNT(*) as trips_in_shift,
        COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) as completed_in_shift,
        AVG(tl.total_duration_minutes) as avg_duration_in_shift,
        COUNT(CASE WHEN tl.is_exception = true THEN 1 END) as exceptions_in_shift,

        -- Efficiency metrics
        COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0) as completion_rate_in_shift,
        COUNT(CASE WHEN tl.is_exception = true THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0) as exception_rate_in_shift

      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN drivers d ON a.driver_id = d.id
      LEFT JOIN driver_shifts ds ON a.shift_id = ds.id
      WHERE 1=1
        ${dateFilter}
        ${driverFilter}
        AND d.status = 'active'
      GROUP BY d.id, d.full_name, d.employee_id, ds.shift_type
      ORDER BY d.full_name, ds.shift_type
    `;

    const result = await query(comparisonQuery, queryParams);

    // Group results by driver
    const driverComparisons = {};
    result.rows.forEach(row => {
      const driverId = row.driver_id;
      if (!driverComparisons[driverId]) {
        driverComparisons[driverId] = {
          driverId: row.driver_id,
          driverName: row.driver_name,
          employeeId: row.employee_id,
          shiftPerformance: {}
        };
      }

      driverComparisons[driverId].shiftPerformance[row.shift_type] = {
        tripsInShift: parseInt(row.trips_in_shift) || 0,
        completedInShift: parseInt(row.completed_in_shift) || 0,
        avgDurationInShift: row.avg_duration_in_shift ? Math.round(parseFloat(row.avg_duration_in_shift)) : 0,
        exceptionsInShift: parseInt(row.exceptions_in_shift) || 0,
        completionRateInShift: row.completion_rate_in_shift ? parseFloat(row.completion_rate_in_shift).toFixed(1) : '0.0',
        exceptionRateInShift: row.exception_rate_in_shift ? parseFloat(row.exception_rate_in_shift).toFixed(1) : '0.0'
      };
    });

    res.json({
      success: true,
      data: {
        driverComparisons: Object.values(driverComparisons),
        dateRange: {
          start: queryParams[0] || null,
          end: queryParams[1] || new Date().toISOString()
        }
      }
    });

  } catch (error) {
    console.error('Driver shift comparison analytics error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve driver shift comparison analytics'
    });
  }
});

module.exports = router;