-- ============================================================================
-- Migration 024: Update Trip Status Terminology (breakdown → stopped)
-- Purpose: Replace 'breakdown' with 'stopped' in trip_status enum for more general terminology
-- Issues Addressed: ISSUE 5 - Trip Status Terminology Update
-- Date: 2025-01-09
-- ============================================================================

-- Log the migration start
DO $$
BEGIN
    RAISE NOTICE 'Starting trip status terminology update: breakdown → stopped';
END $$;

-- ============================================================================
-- Step 1: Create new trip_status enum with 'stopped' instead of 'breakdown'
-- ============================================================================

CREATE TYPE trip_status_new AS ENUM (
    'assigned',
    'loading_start',
    'loading_end', 
    'unloading_start',
    'unloading_end',
    'trip_completed',
    'exception_pending',
    'cancelled',
    'stopped'  -- Replaces 'breakdown'
);

-- ============================================================================
-- Step 2: Backup existing breakdown trips for audit purposes
-- ============================================================================

-- Create backup table for breakdown trips
CREATE TABLE IF NOT EXISTS trip_logs_breakdown_backup AS
SELECT 
    id,
    assignment_id,
    trip_number,
    status,
    loading_start_time,
    loading_end_time,
    unloading_start_time,
    unloading_end_time,
    trip_completed_time,
    created_at,
    updated_at,
    CURRENT_TIMESTAMP as backup_created_at
FROM trip_logs 
WHERE status = 'breakdown';

-- Log backup creation
DO $$
DECLARE
    v_backup_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO v_backup_count FROM trip_logs_breakdown_backup;
    RAISE NOTICE 'Backed up % breakdown trips to trip_logs_breakdown_backup table', v_backup_count;
END $$;

-- ============================================================================
-- Step 3: Update existing breakdown trips to stopped status
-- ============================================================================

-- Update breakdown trips to stopped
UPDATE trip_logs 
SET status = 'stopped'::text::trip_status_new,
    updated_at = CURRENT_TIMESTAMP
WHERE status = 'breakdown';

-- Log the update
DO $$
DECLARE
    v_updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS v_updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % trips from breakdown to stopped status', v_updated_count;
END $$;

-- ============================================================================
-- Step 4: Update table column to use new enum
-- ============================================================================

-- Alter trip_logs table to use new enum
ALTER TABLE trip_logs 
ALTER COLUMN status TYPE trip_status_new 
USING status::text::trip_status_new;

-- ============================================================================
-- Step 5: Replace old enum with new enum
-- ============================================================================

-- Drop old enum and rename new one
DROP TYPE trip_status;
ALTER TYPE trip_status_new RENAME TO trip_status;

-- ============================================================================
-- Step 6: Update any other tables that might use trip_status
-- ============================================================================

-- Check if any other tables use trip_status enum
DO $$
DECLARE
    v_table_record RECORD;
    v_found_tables TEXT := '';
BEGIN
    FOR v_table_record IN
        SELECT DISTINCT 
            t.table_name,
            c.column_name
        FROM information_schema.tables t
        JOIN information_schema.columns c ON t.table_name = c.table_name
        WHERE c.udt_name = 'trip_status'
          AND t.table_schema = 'public'
          AND t.table_name != 'trip_logs'
    LOOP
        v_found_tables := v_found_tables || v_table_record.table_name || '.' || v_table_record.column_name || ', ';
    END LOOP;
    
    IF v_found_tables != '' THEN
        RAISE NOTICE 'Other tables using trip_status enum: %', TRIM(TRAILING ', ' FROM v_found_tables);
    ELSE
        RAISE NOTICE 'No other tables found using trip_status enum';
    END IF;
END $$;

-- ============================================================================
-- Step 7: Create functions for backward compatibility
-- ============================================================================

-- Function to check if a trip is in a terminal state (including stopped)
CREATE OR REPLACE FUNCTION is_trip_terminal(p_status trip_status)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN p_status IN ('trip_completed', 'stopped', 'cancelled');
END;
$$ LANGUAGE plpgsql;

-- Function to check if a trip allows new trip creation
CREATE OR REPLACE FUNCTION can_create_new_trip_after_status(p_status trip_status)
RETURNS BOOLEAN AS $$
BEGIN
    -- Stopped trips (formerly breakdown) should allow new trip creation
    RETURN p_status IN ('trip_completed', 'stopped', 'cancelled');
END;
$$ LANGUAGE plpgsql;

-- Function to get user-friendly status description
CREATE OR REPLACE FUNCTION get_trip_status_description(p_status trip_status)
RETURNS TEXT AS $$
BEGIN
    RETURN CASE p_status
        WHEN 'assigned' THEN 'Trip assigned to driver'
        WHEN 'loading_start' THEN 'Loading in progress'
        WHEN 'loading_end' THEN 'Loading finished, traveling'
        WHEN 'unloading_start' THEN 'Unloading in progress'
        WHEN 'unloading_end' THEN 'Unloading finished, returning'
        WHEN 'trip_completed' THEN 'Fully completed trip'
        WHEN 'exception_pending' THEN 'Exception pending approval'
        WHEN 'cancelled' THEN 'Cancelled or rejected'
        WHEN 'stopped' THEN 'Trip stopped (breakdown, repair, or finished)'
        ELSE 'Unknown status'
    END;
END;
$$ LANGUAGE plpgsql;

-- Function to get status display configuration for frontend
CREATE OR REPLACE FUNCTION get_trip_status_display_config(p_status trip_status)
RETURNS JSON AS $$
BEGIN
    RETURN CASE p_status
        WHEN 'assigned' THEN '{"icon": "📋", "color": "blue", "label": "Assigned"}'::json
        WHEN 'loading_start' THEN '{"icon": "⬆️", "color": "orange", "label": "Loading"}'::json
        WHEN 'loading_end' THEN '{"icon": "🚛", "color": "yellow", "label": "Traveling"}'::json
        WHEN 'unloading_start' THEN '{"icon": "⬇️", "color": "purple", "label": "Unloading"}'::json
        WHEN 'unloading_end' THEN '{"icon": "🔄", "color": "cyan", "label": "Returning"}'::json
        WHEN 'trip_completed' THEN '{"icon": "✅", "color": "green", "label": "Completed"}'::json
        WHEN 'exception_pending' THEN '{"icon": "⚠️", "color": "amber", "label": "Exception"}'::json
        WHEN 'cancelled' THEN '{"icon": "❌", "color": "red", "label": "Cancelled"}'::json
        WHEN 'stopped' THEN '{"icon": "⏹️", "color": "orange", "label": "Stopped"}'::json
        ELSE '{"icon": "❓", "color": "gray", "label": "Unknown"}'::json
    END;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- Step 8: Update existing functions that reference breakdown status
-- ============================================================================

-- Update any existing functions that might reference 'breakdown' status
-- This is a placeholder - actual functions would need to be identified and updated

-- Example: Update trip completion trigger if it exists
DO $$
BEGIN
    -- Check if the trigger function exists and update it
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'update_assignment_on_trip_complete') THEN
        -- This would need to be customized based on actual function content
        RAISE NOTICE 'Found update_assignment_on_trip_complete function - may need manual review for breakdown references';
    END IF;
END $$;

-- ============================================================================
-- Step 9: Create migration verification queries
-- ============================================================================

-- Function to verify migration success
CREATE OR REPLACE FUNCTION verify_trip_status_migration()
RETURNS TABLE (
    check_name TEXT,
    status TEXT,
    details TEXT
) AS $$
BEGIN
    -- Check 1: No breakdown status remains
    RETURN QUERY
    SELECT 
        'No breakdown status remaining'::TEXT,
        CASE WHEN COUNT(*) = 0 THEN 'PASS' ELSE 'FAIL' END::TEXT,
        'Found ' || COUNT(*)::TEXT || ' trips with breakdown status'::TEXT
    FROM trip_logs 
    WHERE status::TEXT = 'breakdown';
    
    -- Check 2: Stopped status exists and is used
    RETURN QUERY
    SELECT 
        'Stopped status in use'::TEXT,
        CASE WHEN COUNT(*) > 0 THEN 'PASS' ELSE 'FAIL' END::TEXT,
        'Found ' || COUNT(*)::TEXT || ' trips with stopped status'::TEXT
    FROM trip_logs 
    WHERE status = 'stopped';
    
    -- Check 3: Backup table exists
    RETURN QUERY
    SELECT 
        'Backup table created'::TEXT,
        CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'trip_logs_breakdown_backup') 
             THEN 'PASS' ELSE 'FAIL' END::TEXT,
        'Backup table trip_logs_breakdown_backup exists'::TEXT;
    
    -- Check 4: New enum is in place
    RETURN QUERY
    SELECT 
        'New trip_status enum'::TEXT,
        CASE WHEN EXISTS (
            SELECT 1 FROM pg_enum e 
            JOIN pg_type t ON e.enumtypid = t.oid 
            WHERE t.typname = 'trip_status' AND e.enumlabel = 'stopped'
        ) THEN 'PASS' ELSE 'FAIL' END::TEXT,
        'trip_status enum contains stopped value'::TEXT;
        
    -- Check 5: Helper functions created
    RETURN QUERY
    SELECT 
        'Helper functions created'::TEXT,
        CASE WHEN EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'is_trip_terminal') AND
                  EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'can_create_new_trip_after_status') AND
                  EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'get_trip_status_description')
             THEN 'PASS' ELSE 'FAIL' END::TEXT,
        'All helper functions are available'::TEXT;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- Step 10: Run verification and log results
-- ============================================================================

-- Run verification
DO $$
DECLARE
    v_check RECORD;
    v_all_passed BOOLEAN := TRUE;
BEGIN
    RAISE NOTICE 'Running migration verification...';
    
    FOR v_check IN SELECT * FROM verify_trip_status_migration()
    LOOP
        RAISE NOTICE 'Check: % - % - %', v_check.check_name, v_check.status, v_check.details;
        IF v_check.status != 'PASS' THEN
            v_all_passed := FALSE;
        END IF;
    END LOOP;
    
    IF v_all_passed THEN
        RAISE NOTICE 'Migration verification: ALL CHECKS PASSED';
    ELSE
        RAISE WARNING 'Migration verification: SOME CHECKS FAILED - Review required';
    END IF;
END $$;

-- ============================================================================
-- Step 11: Add comments and documentation
-- ============================================================================

COMMENT ON TYPE trip_status IS 'Trip status enum - updated to use "stopped" instead of "breakdown" for more general terminology';
COMMENT ON TABLE trip_logs_breakdown_backup IS 'Backup of trips that had breakdown status before migration to stopped';
COMMENT ON FUNCTION is_trip_terminal(trip_status) IS 'Check if trip status is terminal (completed, stopped, cancelled)';
COMMENT ON FUNCTION can_create_new_trip_after_status(trip_status) IS 'Check if new trips can be created after this status';
COMMENT ON FUNCTION get_trip_status_description(trip_status) IS 'Get user-friendly description for trip status';
COMMENT ON FUNCTION get_trip_status_display_config(trip_status) IS 'Get display configuration (icon, color, label) for frontend';
COMMENT ON FUNCTION verify_trip_status_migration() IS 'Verify that trip status migration completed successfully';

-- ============================================================================
-- Migration Complete
-- ============================================================================

-- Log migration completion
INSERT INTO migration_log (migration_name, executed_at, description) 
VALUES (
    '024_update_trip_status_terminology', 
    CURRENT_TIMESTAMP, 
    'Updated trip_status enum from breakdown to stopped with backward compatibility'
) ON CONFLICT (migration_name) DO UPDATE SET 
    executed_at = CURRENT_TIMESTAMP,
    description = EXCLUDED.description;

-- Final success message
DO $$
BEGIN
    RAISE NOTICE 'Migration 024 completed successfully: trip_status terminology updated from breakdown to stopped';
    RAISE NOTICE 'Key changes:';
    RAISE NOTICE '  - breakdown status replaced with stopped';
    RAISE NOTICE '  - Existing breakdown trips migrated to stopped';
    RAISE NOTICE '  - Backup table created: trip_logs_breakdown_backup';
    RAISE NOTICE '  - Helper functions added for compatibility';
    RAISE NOTICE '  - Frontend display configurations updated';
END $$;
