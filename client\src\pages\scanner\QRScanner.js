import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Scanner } from '@yudiel/react-qr-scanner';
import { scannerAPI } from '../../services/api';
import toast from 'react-hot-toast';

const QRScanner = () => {
  // Scanner states
  const [isScanning, setIsScanning] = useState(false);
  const [scanStep, setScanStep] = useState('location'); // 'location' or 'truck'
  const [locationScanData, setLocationScanData] = useState(null);
  const [scanResult, setScanResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [cameraError, setCameraError] = useState(null);
  const [facingMode, setFacingMode] = useState('environment'); // 'user' or 'environment'
  const [isMobile, setIsMobile] = useState(false);
  const [isLandscape, setIsLandscape] = useState(false);

  // Scan data tracking
  const [currentTrip, setCurrentTrip] = useState(null);
  const [tripHistory, setTripHistory] = useState([]);
  const [activeTruck, setActiveTruck] = useState(null);
  // Removed unused currentTruckData state

  // Audio feedback
  const audioRef = useRef();
  
  // Track if location has been restored
  const locationRestored = useRef(false);

  // Mobile detection and orientation handling
  useEffect(() => {
    const checkMobile = () => {
      const userAgent = navigator.userAgent || navigator.vendor || window.opera;
      const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent.toLowerCase());
      setIsMobile(isMobileDevice);
    };

    const checkOrientation = () => {
      setIsLandscape(window.innerWidth > window.innerHeight);
    };

    checkMobile();
    checkOrientation();

    window.addEventListener('resize', checkOrientation);
    window.addEventListener('orientationchange', checkOrientation);

    return () => {
      window.removeEventListener('resize', checkOrientation);
      window.removeEventListener('orientationchange', checkOrientation);
    };
  }, []);

  // Reset scanner state with optional location preservation
  const resetScannerState = useCallback((keepLocation = true) => {
    if (!keepLocation) {
      // Full reset - clear everything
      localStorage.removeItem('locationScanData');
      setLocationScanData(null);
      setScanStep('location');
      console.log('Full reset - cleared location data');
    } else {
      // Partial reset - keep location, reset scan step
      if (locationScanData) {
        setScanStep('truck');
        console.log('Partial reset - kept location:', locationScanData.name);
      } else {
        setScanStep('location');
        console.log('Partial reset - no location to preserve');
      }
    }
  }, [locationScanData]);

  // Store truck data in localStorage
  const storeTruckData = useCallback((truck) => {
    if (truck) {
      localStorage.setItem('activeTruckData', JSON.stringify(truck));
    } else {
      localStorage.removeItem('activeTruckData');
    }
  }, []);

  // Restore truck data when component mounts
  useEffect(() => {
    try {
      const storedTruck = localStorage.getItem('activeTruckData');
      if (storedTruck) {
        const truckData = JSON.parse(storedTruck);
        setActiveTruck(truckData);
      }
    } catch (error) {
      console.error('Failed to restore truck data:', error);
      localStorage.removeItem('activeTruckData');
    }
  }, []);

  // Watch for truck changes and persist
  useEffect(() => {
    storeTruckData(activeTruck);
  }, [activeTruck, storeTruckData]);

  // Reset scanner with location preservation option
  const resetScanner = useCallback(() => {
    const clearLocationAction = async () => {
      // Full reset - clear everything
      resetScannerState(false); // false = don't keep location
      setScanResult(null);
      setCurrentTrip(null);
      setCameraError(null);
      setActiveTruck(null);
      localStorage.removeItem('activeTruckData');
      toast.success('Scanner fully reset - ready for new location scan');
    };

    const resetTripOnlyAction = () => {
      // Partial reset - keep location
      resetScannerState(true); // true = keep location
      setScanResult(null);
      setCurrentTrip(null);
      setActiveTruck(null);
      localStorage.removeItem('activeTruckData');
      toast.success(`Reset trip data - continuing at ${locationScanData?.name}`);
    };

    if (locationScanData) {
      // Show dialog with both options
      const choice = window.confirm(
        `Do you want to:\n\n` +
        `• Click OK to clear everything (including location: ${locationScanData.name})\n` +
        `• Click Cancel to only reset trip data (keep current location)\n`
      );
      
      if (choice) {
        clearLocationAction();
      } else {
        resetTripOnlyAction();
      }
    } else {
      // No location to preserve, just do full reset
      clearLocationAction();
    }
  }, [resetScannerState, locationScanData]);

  // Initialize audio and handle one-time setup
  useEffect(() => {
    audioRef.current = new Audio();
    audioRef.current.preload = 'auto';
  }, []);

  // Handle location restoration separately
  useEffect(() => {
    // Skip if we already have location data
    if (locationScanData) return;

    // Skip if we've already attempted restoration
    if (locationRestored.current) return;
    locationRestored.current = true;

    const storedData = localStorage.getItem('locationScanData');
    if (!storedData) return;

    try {
      const parsedData = JSON.parse(storedData);

      if (typeof parsedData === 'object' &&
          parsedData !== null &&
          parsedData.id &&
          parsedData.name &&
          parsedData.type === 'location') {
        
        console.log('Restoring location:', parsedData);
        setLocationScanData(parsedData);
        setScanStep('truck');
        toast.success(`Location '${parsedData.name}' is active`);
      } else {
        console.warn('Invalid location data structure:', parsedData);
        localStorage.removeItem('locationScanData');
      }
    } catch (error) {
      console.error('Failed to parse location data:', error);
      localStorage.removeItem('locationScanData');
    }
  }, [locationScanData, setLocationScanData, setScanStep]);

  // Play success sound
  const playSuccessSound = useCallback(() => {
    try {
      // Create audio context for beep sound
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      oscillator.frequency.value = 800;
      oscillator.type = 'sine';
      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
      
      oscillator.start();
      oscillator.stop(audioContext.currentTime + 0.2);
    } catch (error) {
      console.log('Audio not supported');
    }
  }, []);

  // Play error sound
  const playErrorSound = useCallback(() => {
    try {
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      oscillator.frequency.value = 300;
      oscillator.type = 'sine';
      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
      
      oscillator.start();
      oscillator.stop(audioContext.currentTime + 0.5);
    } catch (error) {
      console.log('Audio not supported');
    }
  }, []);

  // Refresh camera function
  const refreshCamera = useCallback(() => {
    setIsScanning(false);
    setTimeout(() => {
      setIsScanning(true);
    }, 500); // Restart camera after a short delay
  }, []);

  // Add scan to trip history with comprehensive data parsing
  const addToTripHistory = useCallback((scanData) => {
    if (!scanData) {
      console.error('Invalid scan data received');
      return;
    }

    // Get location info with context awareness
    const getLocationInfo = (data) => {
      // Use current location context if available
      if (locationScanData) {
        return {
          name: locationScanData.name,
          type: data?.trip?.status?.includes('loading') ? 'loading' : 'unloading',
          code: locationScanData.id
        };
      }

      // Try to get from scan response
      if (data?.location?.name) {
        return {
          name: data.location.name,
          type: data.location.type || 'location',
          code: data.location.code || data.location.id
        };
      }

      // Try trip data
      if (data?.trip) {
        const isLoading = data.trip.status?.includes('loading');
        const locationName = isLoading ?
          (data.trip.loading_location_name || data.trip.actual_loading_location_name) :
          (data.trip.unloading_location_name || data.trip.actual_unloading_location_name);
        
        if (locationName) {
          return {
            name: locationName,
            type: isLoading ? 'loading' : 'unloading',
            code: isLoading ? data.trip.loading_location_code : data.trip.unloading_location_code
          };
        }
      }

      // Default with warning
      console.warn('No location context found for history item');
      return {
        name: locationScanData?.name || 'Location Required',
        type: 'unknown',
        code: 'UNKNOWN'
      };
    };

    // Extract truck data using component level state
    const getTruckInfo = (data) => {
      // Debug input
      console.log('getTruckInfo raw input:', data);

      // For location scans, use current truck or placeholder
      if (scanStep === 'location') {
        return activeTruck || {
          number: 'Awaiting Truck',
          plate: 'Scan truck QR next'
        };
      }

      // Try to get truck data from multiple sources
      const truckData = data?.truck ||
                       data?.trip?.truck ||
                       data?.trip?.assigned_truck ||
                       activeTruck;

      console.log('getTruckInfo processed data:', truckData);

      if (truckData) {
        let truckNumber = truckData.number || truckData.truck_number;
        
        if (truckNumber) {
          // Normalize truck number format
          truckNumber = truckNumber.replace(/^[Tt]/, '');
          truckNumber = `T${truckNumber}`;
          
          const truckInfo = {
            number: truckNumber,
            plate: truckData.license_plate || truckData.plate || 'Plate Pending'
          };

          // Update active truck at component level
          if (!activeTruck) {
            setActiveTruck(truckInfo);
          }
          return truckInfo;
        }
      }

      return activeTruck || {
        number: 'Active Trip In Progress',
        plate: 'Continue with current truck'
      };
    };

    // Get trip status in human-readable format
    const formatTripStatus = (status) => {
      if (!status) return '';
      return status
        .replace(/_/g, ' ')
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
    };

    // Extract and format all relevant data
    const locationInfo = getLocationInfo(scanData.data);
    const truckInfo = getTruckInfo(scanData.data);
    const tripNumber = scanData.data?.trip?.trip_number;
    const tripStatus = formatTripStatus(scanData.data?.trip?.status);

    // Create comprehensive history item
    const historyItem = {
      id: Date.now(),
      timestamp: new Date(),
      type: locationInfo.type,
      location: locationInfo.name,
      truck: truckInfo.number,
      truck_plate: truckInfo.plate,
      message: scanData.message || 'Scan processed',
      step: scanData.next_step || 'complete',
      trip_info: tripNumber ? {
        number: tripNumber,
        status: tripStatus
      } : null
    };

    // Add to history with size limit
    setTripHistory(prev => [historyItem, ...prev.slice(0, 9)]);

    // Log successful history addition
    console.log('Added to trip history:', historyItem);
  }, [scanStep, activeTruck, locationScanData]);

  // Handle QR code scan
  const handleScan = useCallback(async (result) => {
    if (!result || loading) return;

    const data = result[0]?.rawValue;
    if (!data) return;

    setLoading(true);
    
    try {
      // Parse QR data
      let qrData;
      try {
        qrData = JSON.parse(data);
      } catch (parseError) {
        toast.error('Invalid QR code format');
        playErrorSound();
        setLoading(false);
        refreshCamera();
        return;
      }

      // Validate QR code structure
      if (!qrData.type || !qrData.id) {
        toast.error('QR code missing required fields');
        playErrorSound();
        setLoading(false);
        refreshCamera();
        return;
      }

      // Note: Location type validation is handled by the backend
      // The backend will validate location types and provide proper error messages

      // Prepare scan request
      const scanRequest = {
        scan_type: scanStep,
        scanned_data: data,
        ip_address: await getClientIP(),
        user_agent: navigator.userAgent
      };

      // Add location context for truck scans
      if (scanStep === 'truck' && locationScanData) {
        scanRequest.location_scan_data = locationScanData;
      }

      // Validate scan type matches expected step
      if (qrData.type !== scanStep) {
        toast.error(`Expected ${scanStep} QR code, but scanned ${qrData.type}`);
        playErrorSound();
        setLoading(false);
        refreshCamera();
        return;
      }

      refreshCamera(); // Ensure camera stops and starts immediately after the first scan

      // Process scan with backend
      const response = await scannerAPI.processScan(scanRequest);

      const scanResponse = response.data; // Get the actual server response
      console.log('Server response:', scanResponse); // Debug log

      if (scanResponse.success) {
        playSuccessSound();
        setScanResult(scanResponse);
        
        // Handle successful scan based on type
        if (scanStep === 'location') {
          // Extract location data from QR and server response
          const serverLocation = scanResponse.data?.location;
          
          // Validate server response
          if (!serverLocation || !serverLocation.name) {
            console.error('Invalid location data in server response:', serverLocation);
            throw new Error('Server returned invalid location data');
          }

          // Construct location data with required fields
          const locationData = {
            id: serverLocation.code || qrData.id,
            type: 'location',
            name: serverLocation.name,
            last_scan: new Date().toISOString()
          };

          // Debug logging
          console.log('Valid location data received:', locationData);
          
          try {
            // Store in localStorage first to validate storage works
            const locationJson = JSON.stringify(locationData);
            localStorage.setItem('locationScanData', locationJson);
            
            // Then update application state
            setLocationScanData(locationData);
            setScanStep('truck');
            
            // Show success message with location name
            toast.success(`Location '${locationData.name}' ready for truck scan`);
            
          } catch (storageError) {
            console.error('Failed to store location data:', storageError);
            toast.error('Could not save location - please try again');
            throw storageError;
          }
        } else if (scanStep === 'truck') {
          if (scanResponse.data?.trip) {
            setCurrentTrip(scanResponse.data.trip);
          }
          
          // Build complete scan history data
          const enhancedScanData = {
            success: scanResponse.success,
            message: scanResponse.message,
            next_step: scanResponse.next_step,
            data: {
              location: scanResponse.data?.location || locationScanData,
              truck: scanResponse.data?.truck ||
                     scanResponse.data?.trip?.truck ||
                     scanResponse.data?.trip?.assigned_truck,
              trip: scanResponse.data?.trip,
              // Include additional context
              step: scanStep,
              scan_time: new Date().toISOString(),
              scan_context: {
                previous_location: locationScanData,
                current_trip: currentTrip
              }
            }
          };

          console.log('Enhanced scan data:', enhancedScanData);
          
          addToTripHistory(enhancedScanData);
          
          // Handle next steps
          if (response.data.next_step === 'trip_complete') {
            // Only reset trip-related data, keep location
            setCurrentTrip(null);
            setActiveTruck(null);
            setScanStep('truck');

            // Enhanced message for return travel completion
            if (response.data.return_travel_duration !== undefined) {
              const certaintyIcon = response.data.location_certainty === 'unconfirmed' ? '❓' : '📍';
              const assignmentType = response.data.assignment_type === 'auto_created' ? 'Auto-Created' : 'Existing';

              toast.success(
                <div>
                  <div className="font-medium">{response.data.message}</div>
                  <div className="text-sm mt-1">
                    {certaintyIcon} {assignmentType} Assignment | Return Travel: {response.data.return_travel_duration}min
                  </div>
                </div>,
                { duration: 5000 }
              );
            } else {
              toast.success(`${response.data.message} - Ready for next truck at ${locationScanData.name}`);
            }
          } else if (response.data.next_step === 'await_approval') {
            // Show special exception alert
            toast((t) => (
              <div className="max-w-md">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                      <span className="text-yellow-600 text-sm">⚠️</span>
                    </div>
                  </div>
                  <div className="ml-3 flex-1">
                    <div className="text-sm font-medium text-yellow-900">
                      Route Deviation Detected
                    </div>
                    <div className="text-sm text-yellow-700 mt-1">
                      {response.data.message}
                    </div>
                    <div className="text-xs text-yellow-600 mt-2">
                      ⏳ Awaiting admin approval to proceed
                    </div>
                  </div>
                  <button
                    onClick={() => toast.dismiss(t.id)}
                    className="ml-3 text-yellow-400 hover:text-yellow-600"
                  >
                    ✕
                  </button>
                </div>
              </div>
            ), {
              duration: 8000,
              position: 'top-center',
              style: {
                background: '#fef3c7',
                border: '1px solid #f59e0b',
                borderRadius: '8px',
                padding: '16px'
              }
            });
            
            // Reset scanner state for exceptions
            resetScannerState();
          } else if (response.data.next_step === 'trip_complete') {
            // Only reset trip-related state, keep location
            setCurrentTrip(null);
            
            // Keep the same location but prepare for next truck
            setScanStep('truck');
            toast.success(`${response.data.message} - Ready for next truck at ${locationScanData.name}`);
          } else {
            // For normal flow, just show success message
            toast.success(response.data.message);
          }
        }

        // Auto-hide scan result after 5 seconds (except for exceptions)
        if (response.data.next_step !== 'await_approval') {
          setTimeout(() => {
            setScanResult(null);
          }, 5000);
        }

      } else {
        playErrorSound();
        // Enhanced error handling for exceptions
        if (response.data.error === 'Route Deviation' || response.data.message?.includes('approval')) {
          toast.error(
            <div>
              <div className="font-medium">Route Deviation Detected</div>
              <div className="text-sm">{response.data.message}</div>
              <div className="text-xs mt-1">Please contact your supervisor</div>
            </div>,
            { duration: 6000 }
          );
        } else {
          toast.error(response.data.message || 'Scan processing failed');
        }
        refreshCamera();
      }

    } catch (error) {
      playErrorSound();
      console.error('Scan error:', error);
      
      if (error.response?.data?.message) {
        const errorMessage = error.response.data.message;

        // Enhanced validation error handling
        if (errorMessage.includes('Cannot start loading operation at unloading location') ||
            errorMessage.includes('Assignment role mismatch') ||
            errorMessage.includes('Invalid location type')) {
          toast.error(
            <div>
              <div className="font-medium">Location Type Validation Error</div>
              <div className="text-sm">{errorMessage}</div>
              <div className="text-xs mt-1 text-gray-600">
                Loading operations must be at loading locations only
              </div>
            </div>,
            { duration: 8000 }
          );
        } else if (errorMessage.includes('approval') ||
                   errorMessage.includes('deviation')) {
          toast.error(
            <div>
              <div className="font-medium">Exception Requires Approval</div>
              <div className="text-sm">{errorMessage}</div>
            </div>,
            { duration: 6000 }
          );
        } else {
          toast.error(errorMessage);
        }
      } else {
        toast.error('Failed to process scan. Please try again.');
      }
      refreshCamera();
    } finally {
      setLoading(false);
    }
  }, [loading, scanStep, locationScanData, refreshCamera, playErrorSound, playSuccessSound, addToTripHistory, resetScannerState, currentTrip]);

  // Handle scan error
  const handleError = useCallback((error) => {
    console.error('Scanner error:', error);
    setCameraError(error?.message || 'Camera access failed');
    
    if (error?.name === 'NotAllowedError') {
      toast.error('Camera permission denied. Please allow camera access.');
    } else if (error?.name === 'NotFoundError') {
      toast.error('No camera found. Please check your device.');
    } else {
      toast.error('Camera error. Please try again.');
    }
  }, []);

  // Get client IP address
  const getClientIP = async () => {
    try {
      const response = await fetch('https://api.ipify.org?format=json');
      const data = await response.json();
      return data.ip;
    } catch (error) {
      return 'unknown';
    }
  };

  // Toggle camera
  const toggleCamera = () => {
    setIsScanning(!isScanning);
    if (isScanning) {
      setCameraError(null);
    }
  };

  // Switch camera
  const switchCamera = () => {
    setFacingMode(prev => prev === 'environment' ? 'user' : 'environment');
  };

  // Get step instructions
  const getStepInstructions = () => {
    if (scanStep === 'location') {
      return {
        title: 'Step 1: Scan Location QR Code',
        description: 'Point your camera at the location QR code to identify where you are.',
        icon: '📍'
      };
    } else {
      return {
        title: 'Step 2: Scan Truck QR Code',
        description: `Scan the truck QR code at ${locationScanData?.name || 'this location'}.`,
        icon: '🚛'
      };
    }
  };

  const instructions = getStepInstructions();

  return (
    <div className={`${isMobile ? 'space-y-4 px-2' : 'space-y-6'}`}>
      {/* Enhanced Mobile Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className={`font-bold text-secondary-900 ${isMobile ? 'text-xl' : 'text-2xl'}`}>
            📱 QR Scanner
            {isMobile && (
              <span className="ml-2 text-sm text-secondary-500">
                {isLandscape ? '🔄 Landscape' : '📱 Portrait'}
              </span>
            )}
          </h1>
          <p className={`text-secondary-600 mt-1 ${isMobile ? 'text-xs' : 'text-sm'}`}>
            Scan truck and location QR codes for trip tracking
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button
            onClick={resetScanner}
            className="btn btn-secondary"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Reset
          </button>
          <button
            onClick={toggleCamera}
            className={`btn ${isScanning ? 'btn-danger' : 'btn-primary'}`}
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
            {isScanning ? 'Stop Camera' : 'Start Camera'}
          </button>
        </div>
      </div>




      {/* Step Instructions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div className="flex items-center">
          <span className="text-3xl mr-4">{instructions.icon}</span>
          <div>
            <h3 className="text-lg font-medium text-blue-900">{instructions.title}</h3>
            <p className="text-blue-700 mt-1">{instructions.description}</p>
          </div>
        </div>
      </div>

      {/* Status Indicators */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className={`p-4 rounded-lg border-2 ${
          scanStep === 'location' ? 'border-primary-500 bg-primary-50' : 'border-secondary-200 bg-white'
        }`}>
          <div className="flex items-center space-x-3">
            <span className="text-2xl">📍</span>
            <div>
              <h4 className="font-medium text-secondary-900">Location Status</h4>
              <div className="text-sm">
                {locationScanData ? (
                  <div className="flex items-center space-x-2">
                    <span className="text-green-600">✓</span>
                    <span className="font-medium text-secondary-900">
                      {locationScanData.name}
                    </span>
                    <span className="text-xs text-secondary-500">
                      {new Date(locationScanData.last_scan).toLocaleTimeString()}
                    </span>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2 text-secondary-600">
                    <span>📍</span>
                    <span>Scan location QR code to begin</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
        
        <div className={`p-4 rounded-lg border-2 ${
          scanStep === 'truck' ? 'border-primary-500 bg-primary-50' : 'border-secondary-200 bg-white'
        }`}>
          <div className="flex items-center space-x-3">
            <span className="text-2xl">🚛</span>
            <div>
              <h4 className="font-medium text-secondary-900">Truck Status</h4>
              <div className="text-sm">
                {activeTruck ? (
                  <div className="flex items-center space-x-2">
                    <span className="text-green-600">✓</span>
                    <span className="font-medium text-secondary-900">
                      {activeTruck.number}
                    </span>
                    {activeTruck.plate && (
                      <span className="text-xs text-secondary-500">
                        ({activeTruck.plate})
                      </span>
                    )}
                  </div>
                ) : (
                  <div className="flex items-center space-x-2 text-secondary-600">
                    <span>🚛</span>
                    <span>
                      {scanStep === 'truck' ? 'Ready to scan truck' : 'Complete location scan first'}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
        
        <div className="p-4 rounded-lg border-2 border-secondary-200 bg-white">
          <div className="flex items-center space-x-3">
            <span className="text-2xl">👥</span>
            <div>
              <h4 className="font-medium text-secondary-900">Multi-Operator</h4>
              <p className="text-sm text-secondary-600">
                Conflict prevention active
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Scanner Interface */}
      <div className="grid grid-cols-1 lg:grid-cols-1 gap-6">
        {/* Camera Scanner */}
        <div className="bg-white rounded-lg shadow-sm border border-secondary-200 p-6 flex flex-col items-center justify-center min-h-[400px]">
          <div className="w-full max-w-md mx-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-secondary-900">Camera Scanner</h3>
              <div className="flex space-x-2">
                <button
                  onClick={switchCamera}
                  className="p-2 text-secondary-600 hover:text-secondary-900"
                  title="Switch Camera"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                  </svg>
                </button>
              </div>
            </div>

            <div className="relative bg-black rounded-lg overflow-hidden flex items-center justify-center mx-auto" style={{ aspectRatio: '1/1', width: '100%', maxWidth: '600px' }}>
              {isScanning && !cameraError ? (
                <div className="relative w-full h-full flex items-center justify-center">
                  <Scanner
                    onScan={handleScan}
                    onError={handleError}
                    constraints={{
                      facingMode: facingMode,
                      aspectRatio: 1/1,
                      width: { ideal: 800 },
                      height: { ideal: 600 }
                    }}
                    styles={{
                      container: { width: '100%', height: '100%' },
                      video: { width: '100%', height: '100%', objectFit: 'cover' }
                    }}
                    components={{
                      audio: false,
                      finder: false
                    }}
                  />
                  
                  {/* Scanning overlay */}
                  <div className="absolute inset-0 pointer-events-none">
                    <div className="absolute inset-4 border-2 border-white opacity-30 rounded-lg"></div>
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                      <div className="w-48 h-48 border-2 border-primary-500 rounded-lg"></div>
                      <div className="absolute top-2 left-2 w-4 h-4 border-t-2 border-l-2 border-primary-500"></div>
                      <div className="absolute top-2 right-2 w-4 h-4 border-t-2 border-r-2 border-primary-500"></div>
                      <div className="absolute bottom-2 left-2 w-4 h-4 border-b-2 border-l-2 border-primary-500"></div>
                      <div className="absolute bottom-2 right-2 w-4 h-4 border-b-2 border-r-2 border-primary-500"></div>
                    </div>
                    
                    {/* Step indicator */}
                    <div className="absolute top-4 left-4 bg-black bg-opacity-70 text-white px-3 py-1 rounded-lg text-sm">
                      {scanStep === 'location' ? '1. Scan Location' : '2. Scan Truck'}
                    </div>
                  </div>

                  {/* Loading indicator */}
                  {loading && (
                    <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                      <div className="bg-white rounded-lg p-4 flex items-center space-x-3">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
                        <span className="text-sm font-medium">Processing scan...</span>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="w-full h-full flex items-center justify-center text-white">
                  {cameraError ? (
                    <div className="text-center">
                      <svg className="w-12 h-12 mx-auto mb-4 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5l-6.928-12c-.77-.833-2.694-.833-3.464 0l-6.928 12c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                      <p className="text-red-400 text-sm">{cameraError}</p>
                      <button
                        onClick={toggleCamera}
                        className="mt-2 btn btn-primary btn-sm"
                      >
                        Retry
                      </button>
                    </div>
                  ) : (
                    <div className="text-center">
                      <svg className="w-12 h-12 mx-auto mb-4 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                      <p className="text-secondary-400">Camera not active</p>
                      <p className="text-sm text-secondary-500 mt-1">Click "Start Camera" to begin scanning</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Scanner Settings */}
          <div className="mt-4 w-full max-w-md mx-auto text-center">
            <div className="text-sm text-secondary-600 font-medium mb-1">
              Multi-operator ready • Conflict prevention enabled
            </div>
            <div className="text-sm text-secondary-500">
              Camera: {facingMode === 'environment' ? 'Back' : 'Front'}
            </div>
          </div>
        </div>

        {/* Scan Results & Trip Status */}
        <div className="space-y-6">          
          {/* Current Scan Result */}
          {scanResult && (
            <div className={`border rounded-lg p-6 ${
              scanResult.next_step === 'await_approval' 
                ? 'bg-yellow-50 border-yellow-200' 
                : 'bg-green-50 border-green-200'
            }`}>
              <h3 className={`text-lg font-medium mb-3 ${
                scanResult.next_step === 'await_approval' 
                  ? 'text-yellow-900' 
                  : 'text-green-900'
              }`}>
                {scanResult.next_step === 'await_approval' ? '⚠️ Exception Detected' : '✅ Scan Result'}
              </h3>
              
              <div className="space-y-2">
                <p className={`${
                  scanResult.next_step === 'await_approval' 
                    ? 'text-yellow-800' 
                    : 'text-green-800'
                }`}>
                  {scanResult.message}
                </p>
                
                {scanResult.data?.location && (
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">📍</span>
                    <span className={`text-sm ${
                      scanResult.next_step === 'await_approval'
                        ? 'text-yellow-700'
                        : 'text-green-700'
                    }`}>
                      {scanResult.data.location.name} ({scanResult.data.location.type})
                    </span>
                  </div>
                )}
                
                {scanResult.data?.truck && (
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">🚛</span>
                    <span className={`text-sm ${
                      scanResult.next_step === 'await_approval'
                        ? 'text-yellow-700'
                        : 'text-green-700'
                    }`}>
                      {scanResult.data.truck.truck_number || scanResult.data.truck.number || 'Unknown Truck'} - {scanResult.data.truck.license_plate}
                    </span>
                  </div>
                )}
                
                {scanResult.next_step && (
                  <div className={`mt-3 p-3 rounded-lg ${
                    scanResult.next_step === 'await_approval' 
                      ? 'bg-yellow-100' 
                      : 'bg-green-100'
                  }`}>
                    <p className={`text-sm font-medium ${
                      scanResult.next_step === 'await_approval' 
                        ? 'text-yellow-800' 
                        : 'text-green-800'
                    }`}>
                      {scanResult.next_step === 'await_approval' ? 'Status:' : 'Next Step:'}
                    </p>
                    <p className={`text-sm ${
                      scanResult.next_step === 'await_approval' 
                        ? 'text-yellow-700' 
                        : 'text-green-700'
                    }`}>
                      {scanResult.next_step === 'await_approval' ? 'Awaiting supervisor approval for route deviation' :
                       scanResult.next_step === 'scan_truck' ? 'Scan truck QR code' :
                       scanResult.next_step === 'scan_loading_end' ? 'Scan truck again to end loading' :
                       scanResult.next_step === 'travel_to_unloading' ? 'Travel to unloading location' :
                       scanResult.next_step === 'scan_unloading_end' ? 'Scan truck again to end unloading' :
                       scanResult.next_step === 'return_to_loading' ? 'Return to loading location' :
                       scanResult.next_step === 'trip_completed' ? 'Trip completed (A→B→A cycle finished)' :
                       scanResult.next_step}
                    </p>
                    
                    {scanResult.next_step === 'await_approval' && (
                      <div className="mt-3 p-2 bg-yellow-200 rounded border border-yellow-300">
                        <div className="flex items-start space-x-2">
                          <span className="text-yellow-800 text-sm">⏰</span>
                          <div className="text-xs text-yellow-800">
                            <div className="font-medium">What happens next:</div>
                            <ul className="mt-1 space-y-1">
                              <li>• Supervisor will review this route deviation</li>
                              <li>• You will be notified when approved/rejected</li>
                              <li>• If approved:
                                <ul className="ml-4 mt-1 space-y-1">
                                  <li>- Truck will be directly assigned</li>
                                  <li>- Trip marked as completed (A→B→A cycle)</li>
                                  <li>- New assignment created if location changed</li>
                                </ul>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Current Trip Status */}
          {currentTrip && (
            <div className="bg-white rounded-lg shadow-sm border border-secondary-200 p-6">
              <h3 className="text-lg font-medium text-secondary-900 mb-4">🚛 Current Trip</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-secondary-600">Trip #</span>
                  <span className="text-sm font-medium">{currentTrip.trip_number}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-secondary-600">Status</span>
                  <span className={`text-sm px-2 py-1 rounded-full ${
                    currentTrip.status === 'loading_start' ? 'bg-blue-100 text-blue-800' :
                    currentTrip.status === 'loading_end' ? 'bg-yellow-100 text-yellow-800' :
                    currentTrip.status === 'unloading_start' ? 'bg-orange-100 text-orange-800' :
                    currentTrip.status === 'unloading_end' ? 'bg-green-100 text-green-800' :
                    currentTrip.status === 'trip_completed' ? 'bg-purple-100 text-purple-800' :
                    'bg-secondary-100 text-secondary-800'
                  }`}>
                    {currentTrip.status === 'trip_completed' ? 'Trip Completed (Full Cycle)' : currentTrip.status.replace('_', ' ')}
                  </span>
                </div>
                {currentTrip.loading_start_time && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-secondary-600">Started</span>
                    <span className="text-sm">
                      {new Date(currentTrip.loading_start_time).toLocaleTimeString()}
                    </span>
                  </div>
                )}
                {currentTrip.loading_duration_minutes && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-secondary-600">Loading Duration</span>
                    <span className="text-sm">{currentTrip.loading_duration_minutes} min</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Scan History */}
          {tripHistory.length > 0 && (
            <div className="bg-white rounded-lg shadow-sm border border-secondary-200 p-6">
              <h3 className="text-lg font-medium text-secondary-900 mb-4">📋 Recent Scans</h3>
              <div className="space-y-3 max-h-64 overflow-y-auto">
                {tripHistory.map((item, index) => (
                  <div key={item.id} className="flex items-center space-x-3 p-3 bg-secondary-50 rounded-lg hover:bg-secondary-100 transition-colors">
                    <span className="text-lg">
                      {item.type === 'loading' ? '📦' :
                       item.type === 'unloading' ? '🏗️' :
                       item.type === 'unknown' ? '❓' : '🚛'}
                    </span>
                    <div className="flex-1 min-w-0">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <p className="text-sm font-medium text-secondary-900 truncate">
                              {item.location}
                            </p>
                            <span className="text-xs text-secondary-600 px-1.5 py-0.5 bg-secondary-100 rounded">
                              {item.type === 'loading' ? 'Loading Point' :
                               item.type === 'unloading' ? 'Unloading Point' :
                               item.type === 'unknown' ? 'Unknown Location' :
                               'Location'}
                            </span>
                          </div>
                          <span className={`text-xs px-2 py-0.5 rounded-full ${
                            item.step === 'await_approval' ? 'bg-yellow-100 text-yellow-800' :
                            item.step === 'trip_complete' ? 'bg-green-100 text-green-800' :
                            'bg-secondary-100 text-secondary-800'
                          }`}>
                            {item.step === 'await_approval' ? 'Pending Approval' :
                             item.step === 'trip_complete' ? 'Complete' :
                             item.step === 'scan_loading_end' ? 'Loading' :
                             item.step === 'scan_unloading_end' ? 'Unloading' :
                             item.step?.replace(/_/g, ' ')}
                          </span>
                        </div>
                        
                        {item.trip_info?.context?.current_location && (
                          <div className="flex items-center text-xs text-secondary-500">
                            <span className="mr-1">📍</span>
                            <span>Current Location: {item.trip_info.context.current_location}</span>
                          </div>
                        )}
                      </div>
                      
                      <div className="flex items-center justify-between mb-1">
                        <div className="flex items-center space-x-2">
                          <span className="text-xs text-secondary-500">
                            {item.truck}
                          </span>
                          {item.truck_plate && (
                            <span className="text-xs text-secondary-400">
                              ({item.truck_plate})
                            </span>
                          )}
                        </div>
                        <span className="text-xs text-secondary-400">
                          {item.timestamp.toLocaleTimeString()}
                        </span>
                      </div>

                      <div className="flex items-center justify-between">
                        {item.trip_info ? (
                          <div className="flex items-center space-x-2">
                            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded">
                              Trip #{item.trip_info.number}
                            </span>
                            <span className="text-xs text-secondary-500">
                              {item.trip_info.status}
                            </span>
                          </div>
                        ) : (
                          <div />
                        )}
                        <div className="text-xs bg-primary-100 text-primary-800 px-2 py-0.5 rounded">
                          #{index + 1}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      

      {/* Help Section */}
      <div className="bg-secondary-50 rounded-lg p-6">
        <h3 className="text-lg font-medium text-secondary-900 mb-4">📖 How to Use</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <h4 className="font-medium text-secondary-800">📍 Step 1: Location Scan</h4>
            <p className="text-sm text-secondary-600">
              Scan the QR code at your current location (loading/unloading point) to identify where you are.
            </p>
          </div>
          <div className="space-y-2">
            <h4 className="font-medium text-secondary-800">🚛 Step 2: Truck Scan</h4>
            <p className="text-sm text-secondary-600">
              After successful location scan, scan the truck QR code to record the trip event.
            </p>
          </div>
          <div className="space-y-2">
            <h4 className="font-medium text-secondary-800">🔄 Trip Cycle</h4>
            <p className="text-sm text-secondary-600">
              Complete cycle: Loading Start → Loading End → Unloading Start → Unloading End → Trip Complete
            </p>
          </div>
          <div className="space-y-2">
            <h4 className="font-medium text-secondary-800">⚠️ Exception Handling</h4>
            <p className="text-sm text-secondary-600">
              Route deviations (unloading at different location) will be flagged for approval.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QRScanner;