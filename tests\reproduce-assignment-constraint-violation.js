const http = require('http');

console.log('🔍 Reproduce Assignment Constraint Violation\n');

function makeRequest(endpoint, options = {}) {
  return new Promise((resolve, reject) => {
    const requestOptions = {
      hostname: 'localhost',
      port: 5000,
      path: endpoint,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    };

    const req = http.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => { data += chunk; });
      res.on('end', () => {
        try {
          resolve({
            status: res.statusCode,
            data: JSON.parse(data)
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: data,
            parseError: error.message
          });
        }
      });
    });
    
    req.on('error', reject);
    if (options.body) req.write(JSON.stringify(options.body));
    req.end();
  });
}

async function reproduceConstraintViolation() {
  try {
    console.log('🔐 Authenticating...');
    const authResponse = await makeRequest('/api/auth/login', {
      method: 'POST',
      body: { username: 'admin', password: 'admin123' }
    });
    
    if (authResponse.status !== 200) {
      console.log('❌ Authentication failed');
      return false;
    }
    
    const token = authResponse.data.token;
    console.log('✅ Authentication successful');
    
    const headers = { 'Authorization': `Bearer ${token}` };
    
    console.log('\n📋 Step 1: Get available trucks and locations...');
    
    // Get trucks
    const trucksResponse = await makeRequest('/api/trucks', { headers });
    if (trucksResponse.status !== 200) {
      console.log('❌ Failed to get trucks');
      return false;
    }
    
    const trucks = trucksResponse.data.data || [];
    if (trucks.length === 0) {
      console.log('❌ No trucks available');
      return false;
    }
    
    // Get locations
    const locationsResponse = await makeRequest('/api/locations', { headers });
    if (locationsResponse.status !== 200) {
      console.log('❌ Failed to get locations');
      return false;
    }
    
    const locations = locationsResponse.data.data || [];
    if (locations.length < 2) {
      console.log('❌ Need at least 2 locations');
      return false;
    }
    
    const truck = trucks[0];
    const loadingLocation = locations.find(l => l.type === 'loading') || locations[0];
    const unloadingLocation = locations.find(l => l.type === 'unloading') || locations[1];
    
    console.log(`✅ Using truck: ${truck.truck_number} (ID: ${truck.id})`);
    console.log(`✅ Loading location: ${loadingLocation.name} (ID: ${loadingLocation.id})`);
    console.log(`✅ Unloading location: ${unloadingLocation.name} (ID: ${unloadingLocation.id})`);
    
    console.log('\n📋 Step 2: Create assignment WITHOUT driver_id to trigger constraint violation...');
    
    const assignmentData = {
      truck_id: truck.id,
      // driver_id: null, // Explicitly NOT providing driver_id
      loading_location_id: loadingLocation.id,
      unloading_location_id: unloadingLocation.id,
      assigned_date: new Date().toISOString().split('T')[0],
      status: 'assigned',
      priority: 'normal',
      expected_loads_per_day: 1,
      notes: 'Test assignment without driver - should trigger constraint violation'
    };
    
    console.log('📤 Creating assignment:', JSON.stringify(assignmentData, null, 2));
    
    const createResponse = await makeRequest('/api/assignments', {
      method: 'POST',
      headers,
      body: assignmentData
    });
    
    console.log(`📥 Assignment creation response: ${createResponse.status}`);
    
    if (createResponse.status === 500) {
      console.log('🚨 CONSTRAINT VIOLATION REPRODUCED!');
      console.log('Error details:', JSON.stringify(createResponse.data, null, 2));
      
      // Check if it's the specific constraint we're looking for
      if (createResponse.data && createResponse.data.message && 
          (createResponse.data.message.includes('chk_driver_id_or_auto') ||
           createResponse.data.message.includes('23514'))) {
        console.log('✅ CONFIRMED: This is the chk_driver_id_or_auto constraint violation!');
        return true;
      } else {
        console.log('⚠️ Different error than expected');
        return false;
      }
    } else if (createResponse.status === 201) {
      console.log('✅ Assignment created successfully (constraint might be fixed or auto-assignment worked)');
      const assignmentId = createResponse.data.data.id;
      
      console.log('Assignment details:', JSON.stringify(createResponse.data.data, null, 2));
      
      // Cleanup
      await makeRequest(`/api/assignments/${assignmentId}`, {
        method: 'DELETE',
        headers
      });
      console.log('🧹 Test assignment cleaned up');
      
      return false; // No constraint violation occurred
    } else {
      console.log('⚠️ Unexpected response:', createResponse.data);
      return false;
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  }
}

console.log('🚀 Starting Assignment Constraint Violation Reproduction');
console.log('📡 Testing against: http://localhost:5000\n');

reproduceConstraintViolation().then((violationReproduced) => {
  console.log('\n📊 CONSTRAINT VIOLATION TEST SUMMARY');
  console.log('====================================');
  
  if (violationReproduced) {
    console.log('✅ CONSTRAINT VIOLATION SUCCESSFULLY REPRODUCED');
    console.log('🎯 Issue confirmed: chk_driver_id_or_auto constraint violation');
    console.log('🔧 Root cause: Assignment creation without driver_id and auto_created=false');
    console.log('💡 Solution needed: Fix assignment creation logic to handle auto_created flag');
  } else {
    console.log('❌ CONSTRAINT VIOLATION NOT REPRODUCED');
    console.log('🔧 Either constraint is fixed, auto-assignment worked, or different issue');
  }
}).catch(error => {
  console.error('❌ Test execution failed:', error);
});
