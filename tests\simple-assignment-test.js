const http = require('http');

console.log('🔍 Simple Assignment Test (No Driver)\n');

function makeRequest(endpoint, options = {}) {
  return new Promise((resolve, reject) => {
    const requestOptions = {
      hostname: 'localhost',
      port: 5000,
      path: endpoint,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    };

    const req = http.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => { data += chunk; });
      res.on('end', () => {
        try {
          resolve({
            status: res.statusCode,
            data: JSON.parse(data)
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: data,
            parseError: error.message
          });
        }
      });
    });
    
    req.on('error', reject);
    if (options.body) req.write(JSON.stringify(options.body));
    req.end();
  });
}

async function simpleTest() {
  try {
    console.log('🔐 Authenticating...');
    const authResponse = await makeRequest('/api/auth/login', {
      method: 'POST',
      body: { username: 'admin', password: 'admin123' }
    });
    
    if (authResponse.status !== 200) {
      console.log('❌ Authentication failed');
      return;
    }
    
    const token = authResponse.data.token;
    console.log('✅ Authentication successful');
    
    const headers = { 'Authorization': `Bearer ${token}` };
    
    console.log('\n📋 Creating assignment without driver_id...');
    
    const assignmentData = {
      truck_id: 1,
      // No driver_id provided
      loading_location_id: 1,
      unloading_location_id: 4, // Use location 4 to avoid duplicates
      assigned_date: '2025-07-20',
      notes: 'Simple test without driver'
    };
    
    console.log('📤 Assignment data:', JSON.stringify(assignmentData, null, 2));
    
    const response = await makeRequest('/api/assignments', {
      method: 'POST',
      headers,
      body: assignmentData
    });
    
    console.log(`📥 Response status: ${response.status}`);
    console.log('📥 Response data:', JSON.stringify(response.data, null, 2));
    
    if (response.status === 201) {
      console.log('✅ Assignment created successfully!');
      const assignmentId = response.data.data.id;
      
      // Cleanup
      await makeRequest(`/api/assignments/${assignmentId}`, {
        method: 'DELETE',
        headers
      });
      console.log('🧹 Assignment cleaned up');
    } else {
      console.log('❌ Assignment creation failed');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

simpleTest();
