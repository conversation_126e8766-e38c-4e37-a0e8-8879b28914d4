-- Check the current constraint definition
SELECT 
  conname as constraint_name,
  pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'assignments'::regclass
  AND conname = 'chk_driver_id_or_auto';

-- Check if auto_created column exists and its properties
SELECT 
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'assignments'
  AND column_name = 'auto_created';

-- Test the constraint logic
SELECT 
  'Test 1: driver_id=1, auto_created=false' as test_case,
  CASE 
    WHEN (1 IS NOT NULL) OR (false = true) THEN 'PASS'
    ELSE 'FAIL'
  END as result
UNION ALL
SELECT 
  'Test 2: driver_id=NULL, auto_created=false' as test_case,
  CASE 
    WHEN (NULL IS NOT NULL) OR (false = true) THEN 'PASS'
    ELSE 'FAIL'
  END as result
UNION ALL
SELECT 
  'Test 3: driver_id=NULL, auto_created=true' as test_case,
  CASE 
    WHEN (NULL IS NOT NULL) OR (true = true) THEN 'PASS'
    ELSE 'FAIL'
  END as result;
