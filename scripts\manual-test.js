#!/usr/bin/env node
/**
 * Manual Testing Helper
 * Provides commands and URLs for manual testing
 */

const { loadConfig, writeClientEnv } = require('../config-loader');
const fs = require('fs');
const path = require('path');

function setHTTPMode() {
  const envPath = path.join(__dirname, '..', '.env');
  let envContent = fs.readFileSync(envPath, 'utf8');
  
  envContent = envContent.replace(/^NODE_ENV=.*/m, 'NODE_ENV=development');
  envContent = envContent.replace(/^ENABLE_HTTPS=.*/m, 'ENABLE_HTTPS=false');
  
  fs.writeFileSync(envPath, envContent);
  
  const config = loadConfig();
  writeClientEnv(config);
  
  console.log('🔵 CONFIGURED FOR HTTP MODE');
  console.log('='.repeat(40));
  console.log(`🌐 IP Address: ${config.IP_ADDRESS}`);
  console.log(`📡 Backend Port: ${config.BACKEND_HTTP_PORT}`);
  console.log(`🌐 Frontend Port: ${config.CLIENT_PORT}`);
  console.log(`🔗 API Base URL: ${config.API_BASE_URL}`);
  console.log(`🔌 WebSocket URL: ${config.WS_URL}`);
  
  console.log('\n📋 TESTING URLS:');
  console.log(`Backend Health: http://localhost:${config.BACKEND_HTTP_PORT}/health`);
  console.log(`Backend Health (Network): http://${config.IP_ADDRESS}:${config.BACKEND_HTTP_PORT}/health`);
  console.log(`Frontend: http://localhost:${config.CLIENT_PORT}`);
  console.log(`Frontend (Network): http://${config.IP_ADDRESS}:${config.CLIENT_PORT}`);
  
  console.log('\n🚀 START COMMANDS:');
  console.log('npm run dev');
  console.log('OR manually:');
  console.log('Terminal 1: cd server && node server.js');
  console.log('Terminal 2: cd client && npm start');
}

function setHTTPSMode() {
  const envPath = path.join(__dirname, '..', '.env');
  let envContent = fs.readFileSync(envPath, 'utf8');
  
  envContent = envContent.replace(/^NODE_ENV=.*/m, 'NODE_ENV=development');
  envContent = envContent.replace(/^ENABLE_HTTPS=.*/m, 'ENABLE_HTTPS=true');
  
  fs.writeFileSync(envPath, envContent);
  
  const config = loadConfig();
  writeClientEnv(config);
  
  // Check SSL certificates
  const sslDevPath = path.join(__dirname, '..', 'server', 'ssl', 'dev');
  const certExists = fs.existsSync(path.join(sslDevPath, 'server.crt'));
  const keyExists = fs.existsSync(path.join(sslDevPath, 'server.key'));
  
  console.log('🔴 CONFIGURED FOR HTTPS MODE');
  console.log('='.repeat(40));
  console.log(`🌐 IP Address: ${config.IP_ADDRESS}`);
  console.log(`📡 Backend Port: ${config.HTTPS_PORT}`);
  console.log(`🌐 Frontend Port: ${config.CLIENT_PORT}`);
  console.log(`🔗 API Base URL: ${config.API_BASE_URL}`);
  console.log(`🔌 WebSocket URL: ${config.WS_URL}`);
  console.log(`🔐 SSL Cert Exists: ${certExists}`);
  console.log(`🔐 SSL Key Exists: ${keyExists}`);
  
  if (!certExists || !keyExists) {
    console.log('\n⚠️  SSL CERTIFICATES MISSING!');
    console.log('Run this first: cd server && node ssl/generate-dev-certs.js');
  }
  
  console.log('\n📋 TESTING URLS:');
  console.log(`Backend Health: https://localhost:${config.HTTPS_PORT}/health`);
  console.log(`Backend Health (Network): https://${config.IP_ADDRESS}:${config.HTTPS_PORT}/health`);
  console.log(`Frontend: https://localhost:${config.CLIENT_PORT}`);
  console.log(`Frontend (Network): https://${config.IP_ADDRESS}:${config.CLIENT_PORT}`);
  
  console.log('\n🚀 START COMMANDS:');
  console.log('npm run dev:https');
  console.log('OR manually:');
  console.log('Terminal 1: cd server && node server.js');
  console.log('Terminal 2: cd client && HTTPS=true npm start');
}

function showCurrentConfig() {
  const config = loadConfig();
  
  console.log('📋 CURRENT CONFIGURATION');
  console.log('='.repeat(40));
  console.log(`🌍 Environment: ${config.NODE_ENV}`);
  console.log(`🔒 HTTPS Enabled: ${config.ENABLE_HTTPS}`);
  console.log(`🌐 IP Address: ${config.IP_ADDRESS}`);
  console.log(`📡 Backend HTTP Port: ${config.BACKEND_HTTP_PORT}`);
  console.log(`📡 Backend HTTPS Port: ${config.HTTPS_PORT}`);
  console.log(`🌐 Frontend Port: ${config.CLIENT_PORT}`);
  console.log(`🔗 API Base URL: ${config.API_BASE_URL}`);
  console.log(`🔌 WebSocket URL: ${config.WS_URL}`);
  
  if (config.ENABLE_HTTPS) {
    const sslDevPath = path.join(__dirname, '..', 'server', 'ssl', 'dev');
    const certExists = fs.existsSync(path.join(sslDevPath, 'server.crt'));
    const keyExists = fs.existsSync(path.join(sslDevPath, 'server.key'));
    console.log(`🔐 SSL Available: ${certExists && keyExists}`);
  }
}

function showHelp() {
  console.log('🧪 MANUAL TESTING HELPER');
  console.log('='.repeat(40));
  console.log('Usage: node scripts/manual-test.js [command]');
  console.log('');
  console.log('Commands:');
  console.log('  http     - Configure for HTTP mode');
  console.log('  https    - Configure for HTTPS mode');
  console.log('  config   - Show current configuration');
  console.log('  help     - Show this help');
  console.log('');
  console.log('Examples:');
  console.log('  node scripts/manual-test.js http');
  console.log('  node scripts/manual-test.js https');
  console.log('  node scripts/manual-test.js config');
}

// Main execution
const command = process.argv[2];

switch (command) {
  case 'http':
    setHTTPMode();
    break;
  case 'https':
    setHTTPSMode();
    break;
  case 'config':
    showCurrentConfig();
    break;
  case 'help':
  default:
    showHelp();
    break;
}

console.log('\n💡 TESTING TIPS:');
console.log('1. Test backend health endpoint first');
console.log('2. Check browser console for errors');
console.log('3. Verify WebSocket connection in Network tab');
console.log('4. Test login functionality');
console.log('5. Test QR scanning (requires HTTPS for camera access)');
console.log('');
console.log('🔧 TROUBLESHOOTING:');
console.log('- Port conflicts: Check if ports are already in use');
console.log('- CORS errors: Verify frontend is using correct backend URL');
console.log('- SSL errors: Accept self-signed certificate in browser');
console.log('- WebSocket errors: Check protocol matches (ws/wss)');
