-- Migration 021: Create Enhanced Assignments View
-- Purpose: Create the missing v_assignments_with_current_drivers view
-- Date: 2025-07-09

-- Create enhanced view for assignment management with current drivers
CREATE OR REPLACE VIEW v_assignments_with_current_drivers AS
SELECT 
    a.*,
    dt.truck_number,
    dt.license_plate,
    d.full_name as assigned_driver_name,
    d.employee_id as assigned_driver_employee_id,
    
    -- Current driver from shift management
    ds.driver_id as current_driver_id,
    cd.full_name as current_driver_name,
    cd.employee_id as current_driver_employee_id,
    ds.shift_type as current_shift_type,
    
    -- Driver status
    CASE 
        WHEN ds.driver_id IS NOT NULL THEN 'active_shift'
        WHEN d.id IS NOT NULL THEN 'assigned_only'
        ELSE 'no_driver'
    END as driver_status,
    
    -- Location information
    ll.name as loading_location_name,
    ul.name as unloading_location_name
FROM assignments a
JOIN dump_trucks dt ON a.truck_id = dt.id
LEFT JOIN drivers d ON a.driver_id = d.id
LEFT JOIN locations ll ON a.loading_location_id = ll.id
LEFT JOIN locations ul ON a.unloading_location_id = ul.id
LEFT JOIN driver_shifts ds ON (
    ds.truck_id = a.truck_id 
    AND ds.status = 'active'
    AND ds.shift_date = CURRENT_DATE
    AND CURRENT_TIME BETWEEN ds.start_time AND 
        CASE 
            WHEN ds.end_time < ds.start_time 
            THEN ds.end_time + interval '24 hours'
            ELSE ds.end_time 
        END
)
LEFT JOIN drivers cd ON ds.driver_id = cd.id;

-- Success message
DO $$ 
BEGIN 
    RAISE NOTICE 'Migration 021 completed successfully: Enhanced assignments view created';
    RAISE NOTICE '- Created v_assignments_with_current_drivers view';
    RAISE NOTICE '- View includes shift-based driver information';
END $$;
