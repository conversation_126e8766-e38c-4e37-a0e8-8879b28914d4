import React, { useState, useEffect } from 'react';
import toast from 'react-hot-toast';
import { getApiBaseUrl } from '../../../../utils/network-utils';
import PhaseAnalysis from './PhaseAnalysis';
import LocationPerformance from './LocationPerformance';
import BreakdownAnalytics from './BreakdownAnalytics';
import PerformanceRankings from './PerformanceRankings';
import ShiftPerformanceAnalytics from './ShiftPerformanceAnalytics';

const TripPerformance = ({ lastUpdated, isConnected, loading }) => {
  const [performanceData, setPerformanceData] = useState(null);
  const [breakdownData, setBreakdownData] = useState(null);
  const [rankingsData, setRankingsData] = useState(null);
  const [dataLoading, setDataLoading] = useState(true);
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0]
  });

  // Load trip performance data
  const loadTripPerformanceData = async () => {
    try {
      setDataLoading(true);
      
      const params = new URLSearchParams({
        start_date: dateRange.start,
        end_date: dateRange.end
      });
      
      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/analytics/trip-performance?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch trip performance data');
      }
      
      const result = await response.json();
      setPerformanceData(result.data);
      
    } catch (error) {
      console.error('Error loading trip performance data:', error);
      toast.error('Failed to load trip performance data');
    } finally {
      setDataLoading(false);
    }
  };

  // Load breakdown analytics data
  const loadBreakdownData = async () => {
    try {
      const params = new URLSearchParams({
        start_date: dateRange.start,
        end_date: dateRange.end
      });
      
      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/analytics/breakdown-analytics?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch breakdown analytics data');
      }
      
      const result = await response.json();
      setBreakdownData(result.data);
      
    } catch (error) {
      console.error('Error loading breakdown data:', error);
      toast.error('Failed to load breakdown analytics');
    }
  };

  // Load performance rankings data
  const loadRankingsData = async () => {
    try {
      const params = new URLSearchParams({
        start_date: dateRange.start,
        end_date: dateRange.end
      });
      
      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/analytics/truck-rankings?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch rankings data');
      }
      
      const result = await response.json();
      setRankingsData(result.data);
      
    } catch (error) {
      console.error('Error loading rankings data:', error);
      toast.error('Failed to load performance rankings');
    }
  };

  // Load data on component mount and when lastUpdated or dateRange changes
  useEffect(() => {
    loadTripPerformanceData();
    loadBreakdownData();
    loadRankingsData();
  }, [lastUpdated, dateRange]); // eslint-disable-line react-hooks/exhaustive-deps

  const handleDateRangeChange = (field, value) => {
    setDateRange(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Show loading state
  if (dataLoading && !performanceData) {
    return (
      <div className="animate-pulse space-y-6">
        <div className="bg-secondary-200 h-16 rounded-lg"></div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-secondary-200 h-32 rounded-lg"></div>
          ))}
        </div>
        <div className="bg-secondary-200 h-64 rounded-lg"></div>
        <div className="bg-secondary-200 h-96 rounded-lg"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Date Range Filter */}
      <div className="bg-white rounded-lg shadow border border-secondary-200 p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <h2 className="text-lg font-medium text-secondary-900 mb-4 sm:mb-0">
            Trip Performance Analytics
          </h2>
          
          <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
            <div className="flex items-center space-x-2">
              <label className="text-sm text-secondary-500">From:</label>
              <input
                type="date"
                value={dateRange.start}
                onChange={(e) => handleDateRangeChange('start', e.target.value)}
                className="border border-secondary-300 rounded-md px-3 py-1 text-sm"
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <label className="text-sm text-secondary-500">To:</label>
              <input
                type="date"
                value={dateRange.end}
                onChange={(e) => handleDateRangeChange('end', e.target.value)}
                className="border border-secondary-300 rounded-md px-3 py-1 text-sm"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Connection Status Alert */}
      {!isConnected && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-yellow-400 text-xl">⚠️</span>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Real-time updates unavailable
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>Performance data may not reflect the latest changes.</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Phase Analysis */}
      <div>
        <h2 className="text-lg font-medium text-secondary-900 mb-4">
          Phase-by-Phase Duration Analysis
        </h2>
        <PhaseAnalysis 
          data={performanceData?.phaseAnalysis} 
          loading={dataLoading || loading}
        />
      </div>

      {/* Location Performance */}
      <div>
        <h2 className="text-lg font-medium text-secondary-900 mb-4">
          Location-Based Performance
        </h2>
        <LocationPerformance 
          data={performanceData?.locationPerformance} 
          routeData={performanceData?.routePatterns}
          loading={dataLoading || loading}
        />
      </div>

      {/* Breakdown Analytics */}
      <div>
        <h2 className="text-lg font-medium text-secondary-900 mb-4">
          Breakdown Analytics
        </h2>
        <BreakdownAnalytics 
          data={breakdownData} 
          loading={dataLoading || loading}
        />
      </div>

      {/* Performance Rankings */}
      <div>
        <h2 className="text-lg font-medium text-secondary-900 mb-4">
          Performance Rankings
        </h2>
        <PerformanceRankings
          data={rankingsData}
          loading={dataLoading || loading}
        />
      </div>

      {/* Shift Performance Analytics */}
      <div>
        <h2 className="text-lg font-medium text-secondary-900 mb-4">
          Multi-Driver Shift Performance
        </h2>
        <ShiftPerformanceAnalytics
          dateRange={dateRange}
          loading={dataLoading || loading}
        />
      </div>
    </div>
  );
};

export default TripPerformance;
