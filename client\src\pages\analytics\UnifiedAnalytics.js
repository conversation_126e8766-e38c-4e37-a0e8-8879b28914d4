import React, { useState, useEffect, useCallback } from 'react';
import useWebSocket from '../../hooks/useWebSocket';
import { useAuth } from '../../context/AuthContext';
import { getApiBaseUrl } from '../../utils/network-utils';
import toast from 'react-hot-toast';

// Import components from analytics-reports (the better implementation)
import FleetMetrics from '../analytics-reports/components/FleetOverview/FleetMetrics';
import FleetStatusGrid from '../analytics-reports/components/FleetOverview/FleetStatusGrid';
import TrendCharts from '../analytics-reports/components/FleetOverview/TrendCharts';
import PhaseAnalysis from '../analytics-reports/components/TripPerformance/PhaseAnalysis';
import LocationPerformance from '../analytics-reports/components/TripPerformance/LocationPerformance';
import BreakdownAnalytics from '../analytics-reports/components/TripPerformance/BreakdownAnalytics';
import PerformanceRankings from '../analytics-reports/components/TripPerformance/PerformanceRankings';
import LiveDashboard from '../analytics-reports/components/LiveOperations/LiveDashboard';
import RouteVisualization from '../analytics-reports/components/LiveOperations/RouteVisualization';
import AlertSystem from '../analytics-reports/components/LiveOperations/AlertSystem';

const UnifiedAnalytics = () => {
  const [loading, setLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(new Date());
  const { user } = useAuth();
  const { lastMessage, isConnected } = useWebSocket(user);

  // Enhanced tab system - 5 comprehensive tabs
  const [activeTab, setActiveTab] = useState('dashboard');

  // Data states for all tabs
  const [fleetData, setFleetData] = useState(null);
  const [statusData, setStatusData] = useState(null);
  const [performanceData, setPerformanceData] = useState(null);
  const [breakdownData, setBreakdownData] = useState(null);
  const [liveData, setLiveData] = useState(null);
  const [rankingsData, setRankingsData] = useState(null);

  // Filter states
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0]
  });

  // Handle real-time WebSocket updates
  useEffect(() => {
    if (lastMessage && lastMessage.type) {
      switch (lastMessage.type) {
        case 'analytics_update':
        case 'fleet_status_changed':
        case 'live_operations_update':
        case 'breakdown_analytics_update':
        case 'trip_performance_update':
          setLastUpdated(new Date());
          break;
        case 'performance_alert':
          toast.error(`Performance Alert: ${lastMessage.message}`, {
            duration: 6000,
            icon: '⚠️'
          });
          break;
        default:
          break;
      }
    }
  }, [lastMessage]);

  // Auto-refresh data every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setLastUpdated(new Date());
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  // Enhanced tab definitions
  const tabs = [
    {
      id: 'dashboard',
      name: 'Dashboard',
      icon: '📊',
      description: 'Overview and KPIs'
    },
    {
      id: 'fleet',
      name: 'Fleet Operations',
      icon: '🚛',
      description: 'Fleet status and operations'
    },
    {
      id: 'performance',
      name: 'Trip Performance',
      icon: '📈',
      description: 'Performance analytics'
    },
    {
      id: 'monitoring',
      name: 'Live Monitoring',
      icon: '🔴',
      description: 'Real-time operations'
    },
    {
      id: 'reports',
      name: 'Reports & Insights',
      icon: '📋',
      description: 'Detailed reports'
    }
  ];

  // Load all data based on active tab
  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      const apiUrl = getApiBaseUrl();
      const token = localStorage.getItem('hauling_token');
      
      const headers = {
        'Authorization': `Bearer ${token}`
      };

      switch (activeTab) {
        case 'dashboard':
        case 'fleet':
          // Load fleet overview and status data
          const [fleetResponse, statusResponse] = await Promise.all([
            fetch(`${apiUrl}/analytics/fleet-overview`, { headers }),
            fetch(`${apiUrl}/analytics/fleet-status`, { headers })
          ]);

          if (fleetResponse.ok) {
            const fleetResult = await fleetResponse.json();
            setFleetData(fleetResult.data);
          }

          if (statusResponse.ok) {
            const statusResult = await statusResponse.json();
            setStatusData(statusResult.data);
          }
          break;

        case 'performance':
        case 'reports':
          // Load performance and breakdown data
          const params = new URLSearchParams({
            start_date: dateRange.start,
            end_date: dateRange.end
          });

          const [perfResponse, breakdownResponse, rankingsResponse] = await Promise.all([
            fetch(`${apiUrl}/analytics/trip-performance?${params}`, { headers }),
            fetch(`${apiUrl}/analytics/breakdown-analytics?${params}`, { headers }),
            fetch(`${apiUrl}/analytics/truck-rankings?${params}`, { headers })
          ]);

          if (perfResponse.ok) {
            const perfResult = await perfResponse.json();
            setPerformanceData(perfResult.data);
          }

          if (breakdownResponse.ok) {
            const breakdownResult = await breakdownResponse.json();
            setBreakdownData(breakdownResult.data);
          }

          if (rankingsResponse.ok) {
            const rankingsResult = await rankingsResponse.json();
            setRankingsData(rankingsResult.data);
          }
          break;

        case 'monitoring':
          // Load live operations data
          const liveResponse = await fetch(`${apiUrl}/analytics/live-operations`, { headers });

          if (liveResponse.ok) {
            const liveResult = await liveResponse.json();
            setLiveData(liveResult.data);
          }
          break;

        default:
          // Default case for ESLint
          break;
      }
    } catch (error) {
      console.error('Error loading analytics data:', error);
      toast.error('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  }, [activeTab, dateRange]);

  // Load data when tab changes or lastUpdated changes
  useEffect(() => {
    loadData();
  }, [loadData, lastUpdated]);

  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
  };

  const handleRefresh = useCallback(() => {
    setLoading(true);
    setLastUpdated(new Date());
    
    toast.success('Analytics data refreshed', {
      duration: 2000,
      icon: '🔄'
    });
    
    setTimeout(() => setLoading(false), 1000);
  }, []);

  const handleDateRangeChange = (field, value) => {
    setDateRange(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="min-h-screen bg-secondary-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-secondary-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-secondary-900">
                📊 Analytics & Reports
              </h1>
              <div className="ml-4 flex items-center text-sm text-secondary-500">
                <div className={`w-2 h-2 rounded-full mr-2 ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
                {isConnected ? 'Live' : 'Disconnected'}
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* Date Range Filter for applicable tabs */}
              {(activeTab === 'performance' || activeTab === 'reports') && (
                <div className="flex items-center space-x-2">
                  <input
                    type="date"
                    value={dateRange.start}
                    onChange={(e) => handleDateRangeChange('start', e.target.value)}
                    className="border border-secondary-300 rounded-md px-2 py-1 text-sm"
                  />
                  <span className="text-secondary-500">to</span>
                  <input
                    type="date"
                    value={dateRange.end}
                    onChange={(e) => handleDateRangeChange('end', e.target.value)}
                    className="border border-secondary-300 rounded-md px-2 py-1 text-sm"
                  />
                </div>
              )}
              
              <div className="text-sm text-secondary-500">
                Last updated: {lastUpdated.toLocaleTimeString()}
              </div>
              <button
                onClick={handleRefresh}
                disabled={loading}
                className="inline-flex items-center px-3 py-2 border border-secondary-300 shadow-sm text-sm leading-4 font-medium rounded-md text-secondary-700 bg-white hover:bg-secondary-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
              >
                <span className={`mr-2 ${loading ? 'animate-spin' : ''}`}>🔄</span>
                Refresh
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8" aria-label="Tabs">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => handleTabChange(tab.id)}
                className={`${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200`}
              >
                <div className="flex items-center">
                  <span className="mr-2 text-lg">{tab.icon}</span>
                  <div className="text-left">
                    <div className="font-medium">{tab.name}</div>
                    <div className="text-xs text-secondary-400 hidden sm:block">
                      {tab.description}
                    </div>
                  </div>
                </div>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Connection Status Alert */}
        {!isConnected && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <span className="text-yellow-400 text-xl">⚠️</span>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">
                  Real-time updates unavailable
                </h3>
                <div className="mt-2 text-sm text-yellow-700">
                  <p>WebSocket connection is disconnected. Data may not be current.</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Dashboard Tab */}
        {activeTab === 'dashboard' && (
          <div className="space-y-8">
            <div>
              <h2 className="text-lg font-medium text-secondary-900 mb-4">
                Fleet Overview
              </h2>
              <FleetMetrics 
                data={fleetData} 
                loading={loading}
              />
            </div>
            
            <div>
              <h2 className="text-lg font-medium text-secondary-900 mb-4">
                Performance Trends
              </h2>
              <TrendCharts 
                data={fleetData?.trends || []} 
                loading={loading}
              />
            </div>
          </div>
        )}

        {/* Fleet Operations Tab */}
        {activeTab === 'fleet' && (
          <div className="space-y-8">
            <div>
              <h2 className="text-lg font-medium text-secondary-900 mb-4">
                Fleet Metrics
              </h2>
              <FleetMetrics 
                data={fleetData} 
                loading={loading}
              />
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-medium text-secondary-900">
                  Fleet Status
                </h2>
                <div className="text-sm text-secondary-500">
                  {statusData?.totalTrucks || 0} trucks
                </div>
              </div>
              <FleetStatusGrid 
                data={statusData} 
                loading={loading}
              />
            </div>
          </div>
        )}

        {/* Trip Performance Tab */}
        {activeTab === 'performance' && (
          <div className="space-y-8">
            <div>
              <h2 className="text-lg font-medium text-secondary-900 mb-4">
                Phase-by-Phase Duration Analysis
              </h2>
              <PhaseAnalysis 
                data={performanceData?.phaseAnalysis} 
                loading={loading}
              />
            </div>

            <div>
              <h2 className="text-lg font-medium text-secondary-900 mb-4">
                Location-Based Performance
              </h2>
              <LocationPerformance 
                data={performanceData?.locationPerformance} 
                routeData={performanceData?.routePatterns}
                loading={loading}
              />
            </div>

            <div>
              <h2 className="text-lg font-medium text-secondary-900 mb-4">
                Performance Rankings
              </h2>
              <PerformanceRankings 
                data={rankingsData} 
                loading={loading}
              />
            </div>
          </div>
        )}

        {/* Live Monitoring Tab */}
        {activeTab === 'monitoring' && (
          <div className="space-y-8">
            <div>
              <h2 className="text-lg font-medium text-secondary-900 mb-4">
                🚨 Active Alerts
              </h2>
              <AlertSystem 
                data={liveData} 
                loading={loading}
              />
            </div>

            <div>
              <h2 className="text-lg font-medium text-secondary-900 mb-4">
                📊 Live Operations Dashboard
              </h2>
              <LiveDashboard 
                data={liveData} 
                loading={loading}
              />
            </div>

            <div>
              <h2 className="text-lg font-medium text-secondary-900 mb-4">
                🗺️ Active Route Visualization
              </h2>
              <RouteVisualization 
                data={liveData} 
                loading={loading}
              />
            </div>
          </div>
        )}

        {/* Reports & Insights Tab */}
        {activeTab === 'reports' && (
          <div className="space-y-8">
            <div>
              <h2 className="text-lg font-medium text-secondary-900 mb-4">
                Breakdown Analytics
              </h2>
              <BreakdownAnalytics 
                data={breakdownData} 
                loading={loading}
              />
            </div>

            <div>
              <h2 className="text-lg font-medium text-secondary-900 mb-4">
                Performance Rankings
              </h2>
              <PerformanceRankings 
                data={rankingsData} 
                loading={loading}
              />
            </div>

            <div>
              <h2 className="text-lg font-medium text-secondary-900 mb-4">
                Location Performance Analysis
              </h2>
              <LocationPerformance 
                data={performanceData?.locationPerformance} 
                routeData={performanceData?.routePatterns}
                loading={loading}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default UnifiedAnalytics;
