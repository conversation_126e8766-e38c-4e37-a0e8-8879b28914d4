-- Migration: Add stopped status to trip_status enum
-- Description: Adds 'stopped' status to trip_status enum and migrates existing 'breakdown' records
-- Version: 027
-- Date: 2025-07-09

-- Add 'stopped' to the trip_status enum if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = 'stopped' 
        AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'trip_status')
    ) THEN
        ALTER TYPE trip_status ADD VALUE 'stopped';
        RAISE NOTICE 'Added stopped status to trip_status enum';
    ELSE
        RAISE NOTICE 'Stopped status already exists in trip_status enum';
    END IF;
END $$;

-- Migrate existing 'breakdown' records to 'stopped'
UPDATE trip_logs 
SET status = 'stopped' 
WHERE status = 'breakdown';

-- Get count of migrated records
DO $$
DECLARE
    migrated_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO migrated_count 
    FROM trip_logs 
    WHERE status = 'stopped';
    
    RAISE NOTICE 'Migration completed: % records now have stopped status', migrated_count;
END $$;

-- Update any views or functions that reference breakdown status
-- Note: The application code has been updated to use 'stopped' terminology

-- Add comment for documentation
COMMENT ON TYPE trip_status IS 'Trip status enum: assigned, loading_start, loading_end, unloading_start, unloading_end, trip_completed, stopped';

-- Verify the migration
DO $$
BEGIN
    -- Check if stopped status exists
    IF EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = 'stopped' 
        AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'trip_status')
    ) THEN
        RAISE NOTICE 'Migration verification: stopped status successfully added to enum';
    ELSE
        RAISE EXCEPTION 'Migration verification failed: stopped status not found in enum';
    END IF;

    -- Check if any breakdown records still exist
    IF EXISTS (SELECT 1 FROM trip_logs WHERE status = 'breakdown') THEN
        RAISE WARNING 'Warning: Some breakdown records still exist and were not migrated';
    ELSE
        RAISE NOTICE 'Migration verification: All breakdown records successfully migrated to stopped';
    END IF;
END $$;
