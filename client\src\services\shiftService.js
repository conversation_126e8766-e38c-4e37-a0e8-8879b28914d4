/**
 * Shift Management Service
 * Handles all shift-related API operations
 */

import { getApiBaseUrl } from '../utils/network-utils';

class ShiftService {
  constructor() {
    this.baseUrl = getApiBaseUrl();
  }

  /**
   * Get authentication headers
   */
  getAuthHeaders() {
    const token = localStorage.getItem('hauling_token');
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };
  }

  /**
   * Get all shifts with optional filtering
   */
  async getShifts(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      Object.keys(filters).forEach(key => {
        if (filters[key] !== undefined && filters[key] !== null && filters[key] !== '') {
          params.append(key, filters[key]);
        }
      });

      const url = `${this.baseUrl}/shifts${params.toString() ? `?${params.toString()}` : ''}`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch shifts: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching shifts:', error);
      throw error;
    }
  }

  /**
   * Get current active shift for a truck
   */
  async getCurrentShift(truckId) {
    try {
      const response = await fetch(`${this.baseUrl}/shifts/current/${truckId}`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      if (response.status === 404) {
        return { success: true, data: null }; // No active shift
      }

      if (!response.ok) {
        throw new Error(`Failed to fetch current shift: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching current shift:', error);
      throw error;
    }
  }

  /**
   * Create a new shift
   */
  async createShift(shiftData) {
    try {
      const response = await fetch(`${this.baseUrl}/shifts`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(shiftData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to create shift: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating shift:', error);
      throw error;
    }
  }

  /**
   * Update an existing shift
   */
  async updateShift(shiftId, updateData) {
    try {
      const response = await fetch(`${this.baseUrl}/shifts/${shiftId}`, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(updateData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to update shift: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error updating shift:', error);
      throw error;
    }
  }

  /**
   * Cancel a shift (soft delete - changes status to cancelled)
   */
  async cancelShift(shiftId) {
    try {
      const response = await fetch(`${this.baseUrl}/shifts/${shiftId}/cancel`, {
        method: 'PATCH',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to cancel shift: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error cancelling shift:', error);
      throw error;
    }
  }

  /**
   * Delete a shift permanently (hard delete)
   */
  async deleteShift(shiftId) {
    try {
      const response = await fetch(`${this.baseUrl}/shifts/${shiftId}`, {
        method: 'DELETE',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to delete shift: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error deleting shift:', error);
      throw error;
    }
  }

  /**
   * Create a shift handover
   */
  async createHandover(handoverData) {
    try {
      const response = await fetch(`${this.baseUrl}/shifts/handover`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(handoverData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to create handover: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating handover:', error);
      throw error;
    }
  }

  /**
   * Get shift handovers with optional filtering
   */
  async getHandovers(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      Object.keys(filters).forEach(key => {
        if (filters[key] !== undefined && filters[key] !== null && filters[key] !== '') {
          params.append(key, filters[key]);
        }
      });

      const url = `${this.baseUrl}/shifts/handovers${params.toString() ? `?${params.toString()}` : ''}`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch handovers: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching handovers:', error);
      throw error;
    }
  }

  /**
   * Manually activate a shift
   */
  async activateShift(shiftId) {
    try {
      const response = await fetch(`${this.baseUrl}/shifts/activate/${shiftId}`, {
        method: 'POST',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to activate shift: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error activating shift:', error);
      throw error;
    }
  }

  /**
   * Get shifts for a specific date range
   */
  async getShiftsByDateRange(startDate, endDate, filters = {}) {
    return this.getShifts({
      ...filters,
      start_date: startDate,
      end_date: endDate
    });
  }

  /**
   * Get shifts for today
   */
  async getTodayShifts(filters = {}) {
    const today = new Date().toISOString().split('T')[0];
    return this.getShifts({
      ...filters,
      shift_date: today
    });
  }

  /**
   * Get shifts for a specific truck
   */
  async getTruckShifts(truckId, filters = {}) {
    return this.getShifts({
      ...filters,
      truck_id: truckId
    });
  }

  /**
   * Get shifts for a specific driver
   */
  async getDriverShifts(driverId, filters = {}) {
    return this.getShifts({
      ...filters,
      driver_id: driverId
    });
  }

  /**
   * Get active shifts
   */
  async getActiveShifts(filters = {}) {
    return this.getShifts({
      ...filters,
      status: 'active'
    });
  }

  /**
   * Get scheduled shifts
   */
  async getScheduledShifts(filters = {}) {
    return this.getShifts({
      ...filters,
      status: 'scheduled'
    });
  }

  /**
   * Utility function to format shift time for display
   */
  formatShiftTime(timeString) {
    if (!timeString) return '';
    
    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    
    return `${displayHour}:${minutes} ${ampm}`;
  }

  /**
   * Utility function to get shift type display name
   */
  getShiftTypeDisplay(shiftType) {
    const types = {
      'day': 'Day Shift',
      'night': 'Night Shift',
      'custom': 'Custom Shift'
    };
    
    return types[shiftType] || shiftType;
  }

  /**
   * Utility function to get shift status display
   */
  getShiftStatusDisplay(status) {
    const statuses = {
      'scheduled': 'Scheduled',
      'active': 'Active',
      'completed': 'Completed',
      'cancelled': 'Cancelled'
    };
    
    return statuses[status] || status;
  }

  /**
   * Utility function to get shift status color
   */
  getShiftStatusColor(status) {
    const colors = {
      'scheduled': 'text-blue-600 bg-blue-50',
      'active': 'text-green-600 bg-green-50',
      'completed': 'text-gray-600 bg-gray-50',
      'cancelled': 'text-red-600 bg-red-50'
    };
    
    return colors[status] || 'text-gray-600 bg-gray-50';
  }
}

// Create and export singleton instance
const shiftService = new ShiftService();
export default shiftService;
