const http = require('http');

console.log('🔍 Shift Mode Consistency Test\n');

const BASE_URL = 'http://localhost:5000';

function makeRequest(endpoint, options = {}) {
  return new Promise((resolve, reject) => {
    const requestOptions = {
      hostname: 'localhost',
      port: 5000,
      path: endpoint,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    };

    const req = http.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => { data += chunk; });
      res.on('end', () => {
        try {
          resolve({
            status: res.statusCode,
            data: JSON.parse(data)
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: data,
            parseError: error.message
          });
        }
      });
    });
    
    req.on('error', reject);
    if (options.body) req.write(JSON.stringify(options.body));
    req.end();
  });
}

async function authenticate() {
  const response = await makeRequest('/api/auth/login', {
    method: 'POST',
    body: { username: 'admin', password: 'admin123' }
  });
  
  if (response.status === 200 && response.data.success) {
    return response.data.token;
  }
  throw new Error('Authentication failed');
}

async function testSingleDayShiftCreateAndEdit(token) {
  console.log('📋 Test 1: Single Day Shift - Create → Edit (Single)');
  
  const headers = { 'Authorization': `Bearer ${token}` };
  
  // Create single-day shift using unified date range approach
  const createData = {
    truck_id: 1,
    driver_id: 1,
    shift_type: 'day',
    start_date: '2025-04-01',
    end_date: '2025-04-01', // Same date = single day
    start_time: '06:00:00',
    end_time: '18:00:00',
    status: 'scheduled'
  };
  
  try {
    const createResponse = await makeRequest('/api/shifts', {
      method: 'POST',
      headers,
      body: createData
    });
    
    if (createResponse.status === 201) {
      console.log('   ✅ Single-day shift created successfully');
      const shiftId = createResponse.data.data.id;
      
      // Edit the shift to another single day
      const editData = {
        start_date: '2025-04-02',
        end_date: '2025-04-02', // Still single day
        start_time: '07:00:00',
        end_time: '19:00:00'
      };
      
      const editResponse = await makeRequest(`/api/shifts/${shiftId}`, {
        method: 'PUT',
        headers,
        body: editData
      });
      
      if (editResponse.status === 200) {
        console.log('   ✅ Single-day shift edited successfully');
        
        // Verify the edit
        const verifyResponse = await makeRequest(`/api/shifts?truck_ids=1&date_from=2025-04-02&date_to=2025-04-02`, { headers });
        const foundShift = verifyResponse.data.data.find(s => s.id === shiftId);
        
        if (foundShift && foundShift.start_time === '07:00:00') {
          console.log('   ✅ Edit verification successful');
          return { success: true, shiftId };
        } else {
          console.log('   ❌ Edit verification failed');
          return { success: false, shiftId };
        }
      } else {
        console.log('   ❌ Single-day shift edit failed');
        return { success: false, shiftId };
      }
    } else {
      console.log('   ❌ Single-day shift creation failed');
      return { success: false };
    }
  } catch (error) {
    console.log('   ❌ Test error:', error.message);
    return { success: false };
  }
}

async function testSingleToMultiDayExpansion(token) {
  console.log('\n📋 Test 2: Single Day → Multi Day Expansion');
  
  const headers = { 'Authorization': `Bearer ${token}` };
  
  // Create single-day shift
  const createData = {
    truck_id: 2,
    driver_id: 2,
    shift_type: 'night',
    start_date: '2025-04-05',
    end_date: '2025-04-05',
    start_time: '18:00:00',
    end_time: '06:00:00',
    status: 'scheduled'
  };
  
  try {
    const createResponse = await makeRequest('/api/shifts', {
      method: 'POST',
      headers,
      body: createData
    });
    
    if (createResponse.status === 201) {
      console.log('   ✅ Single-day shift created');
      const shiftId = createResponse.data.data.id;
      
      // Expand to multi-day
      const expandData = {
        start_date: '2025-04-05',
        end_date: '2025-04-07', // Expand to 3 days
        start_time: '18:00:00',
        end_time: '06:00:00'
      };
      
      const expandResponse = await makeRequest(`/api/shifts/${shiftId}`, {
        method: 'PUT',
        headers,
        body: expandData
      });
      
      if (expandResponse.status === 200) {
        console.log('   ✅ Single-day shift expanded to multi-day');
        return { success: true, shiftId };
      } else {
        console.log('   ❌ Expansion failed:', expandResponse.data.message);
        return { success: false, shiftId };
      }
    } else {
      console.log('   ❌ Single-day shift creation failed');
      return { success: false };
    }
  } catch (error) {
    console.log('   ❌ Test error:', error.message);
    return { success: false };
  }
}

async function testMultiDayShiftCreateAndEdit(token) {
  console.log('\n📋 Test 3: Multi Day Shift - Create → Edit (Multi)');
  
  const headers = { 'Authorization': `Bearer ${token}` };
  
  // Create multi-day shift
  const createData = {
    truck_id: 3,
    driver_id: 3,
    shift_type: 'day',
    start_date: '2025-04-10',
    end_date: '2025-04-12', // 3 days
    start_time: '06:00:00',
    end_time: '18:00:00',
    status: 'scheduled'
  };
  
  try {
    const createResponse = await makeRequest('/api/shifts', {
      method: 'POST',
      headers,
      body: createData
    });
    
    if (createResponse.status === 201) {
      console.log('   ✅ Multi-day shift created successfully');
      const shiftIds = createResponse.data.data.map(s => s.id);
      
      // Edit the first shift in the range
      const editData = {
        start_date: '2025-04-10',
        end_date: '2025-04-14', // Extend to 5 days
        start_time: '05:30:00',
        end_time: '17:30:00'
      };
      
      const editResponse = await makeRequest(`/api/shifts/${shiftIds[0]}`, {
        method: 'PUT',
        headers,
        body: editData
      });
      
      if (editResponse.status === 200) {
        console.log('   ✅ Multi-day shift edited successfully');
        return { success: true, shiftIds };
      } else {
        console.log('   ❌ Multi-day shift edit failed');
        return { success: false, shiftIds };
      }
    } else {
      console.log('   ❌ Multi-day shift creation failed');
      return { success: false };
    }
  } catch (error) {
    console.log('   ❌ Test error:', error.message);
    return { success: false };
  }
}

async function testMultiToSingleDayConsolidation(token) {
  console.log('\n📋 Test 4: Multi Day → Single Day Consolidation');
  
  const headers = { 'Authorization': `Bearer ${token}` };
  
  // Create multi-day shift
  const createData = {
    truck_id: 1,
    driver_id: 2,
    shift_type: 'day',
    start_date: '2025-04-15',
    end_date: '2025-04-17', // 3 days
    start_time: '06:00:00',
    end_time: '18:00:00',
    status: 'scheduled'
  };
  
  try {
    const createResponse = await makeRequest('/api/shifts', {
      method: 'POST',
      headers,
      body: createData
    });
    
    if (createResponse.status === 201) {
      console.log('   ✅ Multi-day shift created');
      const shiftIds = createResponse.data.data.map(s => s.id);
      
      // Consolidate to single day
      const consolidateData = {
        start_date: '2025-04-16',
        end_date: '2025-04-16', // Consolidate to single day
        start_time: '06:00:00',
        end_time: '18:00:00'
      };
      
      const consolidateResponse = await makeRequest(`/api/shifts/${shiftIds[0]}`, {
        method: 'PUT',
        headers,
        body: consolidateData
      });
      
      if (consolidateResponse.status === 200) {
        console.log('   ✅ Multi-day shift consolidated to single day');
        return { success: true, shiftIds };
      } else {
        console.log('   ❌ Consolidation failed');
        return { success: false, shiftIds };
      }
    } else {
      console.log('   ❌ Multi-day shift creation failed');
      return { success: false };
    }
  } catch (error) {
    console.log('   ❌ Test error:', error.message);
    return { success: false };
  }
}

async function testValidationAndErrorHandling(token) {
  console.log('\n📋 Test 5: Validation and Error Handling');
  
  const headers = { 'Authorization': `Bearer ${token}` };
  let validationsPassed = 0;
  let totalValidations = 0;
  
  // Test 1: Invalid date range (end before start)
  totalValidations++;
  try {
    const response = await makeRequest('/api/shifts', {
      method: 'POST',
      headers,
      body: {
        truck_id: 1,
        driver_id: 1,
        shift_type: 'day',
        start_date: '2025-04-20',
        end_date: '2025-04-18', // End before start
        start_time: '06:00:00',
        end_time: '18:00:00'
      }
    });
    
    if (response.status === 400) {
      console.log('   ✅ Invalid date range correctly rejected');
      validationsPassed++;
    } else {
      console.log('   ❌ Invalid date range not rejected');
    }
  } catch (error) {
    console.log('   ❌ Validation test error:', error.message);
  }
  
  // Test 2: Missing required fields
  totalValidations++;
  try {
    const response = await makeRequest('/api/shifts', {
      method: 'POST',
      headers,
      body: {
        truck_id: 1,
        // Missing required fields
        start_date: '2025-04-21',
        end_date: '2025-04-21'
      }
    });
    
    if (response.status === 400) {
      console.log('   ✅ Missing required fields correctly rejected');
      validationsPassed++;
    } else {
      console.log('   ❌ Missing required fields not rejected');
    }
  } catch (error) {
    console.log('   ❌ Validation test error:', error.message);
  }
  
  return { validationsPassed, totalValidations };
}

async function cleanupTestShifts(token, shiftIds) {
  console.log('\n🧹 Cleaning up test shifts...');
  
  const headers = { 'Authorization': `Bearer ${token}` };
  let cleanedCount = 0;
  
  for (const shiftId of shiftIds) {
    try {
      await makeRequest(`/api/shifts/${shiftId}`, {
        method: 'DELETE',
        headers
      });
      cleanedCount++;
    } catch (error) {
      // Ignore cleanup errors
    }
  }
  
  console.log(`✅ Cleaned up ${cleanedCount} test shifts`);
}

async function runShiftModeConsistencyTests() {
  console.log('🚀 Starting Shift Mode Consistency Tests');
  console.log(`📡 Testing against: ${BASE_URL}\n`);
  
  try {
    // Authenticate
    const token = await authenticate();
    console.log('✅ Authentication successful\n');
    
    const allShiftIds = [];
    let totalTests = 0;
    let passedTests = 0;
    
    // Test 1: Single → Single
    totalTests++;
    const test1 = await testSingleDayShiftCreateAndEdit(token);
    if (test1.success) passedTests++;
    if (test1.shiftId) allShiftIds.push(test1.shiftId);
    
    // Test 2: Single → Multi
    totalTests++;
    const test2 = await testSingleToMultiDayExpansion(token);
    if (test2.success) passedTests++;
    if (test2.shiftId) allShiftIds.push(test2.shiftId);
    
    // Test 3: Multi → Multi
    totalTests++;
    const test3 = await testMultiDayShiftCreateAndEdit(token);
    if (test3.success) passedTests++;
    if (test3.shiftIds) allShiftIds.push(...test3.shiftIds);
    
    // Test 4: Multi → Single
    totalTests++;
    const test4 = await testMultiToSingleDayConsolidation(token);
    if (test4.success) passedTests++;
    if (test4.shiftIds) allShiftIds.push(...test4.shiftIds);
    
    // Test 5: Validation
    const validation = await testValidationAndErrorHandling(token);
    totalTests += validation.totalValidations;
    passedTests += validation.validationsPassed;
    
    // Cleanup
    await cleanupTestShifts(token, allShiftIds);
    
    // Summary
    console.log('\n📊 SHIFT MODE CONSISTENCY TEST SUMMARY');
    console.log('======================================');
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${totalTests - passedTests}`);
    console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
    
    if (passedTests === totalTests) {
      console.log('\n✅ ALL MODE CONSISTENCY TESTS PASSED');
      console.log('🎉 Unified date range approach working correctly!');
    } else {
      console.log('\n❌ SOME MODE CONSISTENCY TESTS FAILED');
      console.log('🔧 Mode consistency needs attention');
    }
    
    return passedTests === totalTests;
    
  } catch (error) {
    console.error('❌ Test execution failed:', error.message);
    return false;
  }
}

runShiftModeConsistencyTests().catch(error => {
  console.error('❌ Test execution failed:', error);
  process.exit(1);
});
