-- Migration 018: Fix Assignment Database Issues
-- Purpose: Fix critical database schema and constraint issues preventing assignment creation
-- Date: 2025-07-09
-- Issues Fixed:
--   1. Rate calculation queries using 'is_active' instead of 'status'
--   2. Assignment creation null driver_id constraint violations
--   3. Missing integration between shift management and assignment creation

-- =============================================================================
-- PART 1: Fix locations table compatibility for rate calculations
-- =============================================================================

-- Add is_active computed column for backward compatibility with existing queries
-- This allows existing queries using 'is_active = true' to work without modification
ALTER TABLE locations 
ADD COLUMN IF NOT EXISTS is_active BOOLEAN 
GENERATED ALWAYS AS (status = 'active') STORED;

-- Add index for the computed column to maintain performance
CREATE INDEX IF NOT EXISTS idx_locations_is_active ON locations(is_active) WHERE is_active = true;

-- =============================================================================
-- PART 2: Add missing columns to assignments table if needed
-- =============================================================================

-- Ensure driver_id can be nullable temporarily for auto-assignment scenarios
-- This will be handled by application logic to auto-populate from shift management
ALTER TABLE assignments 
ALTER COLUMN driver_id DROP NOT NULL;

-- Add a constraint to ensure driver_id is populated for non-auto assignments
-- This allows flexibility while maintaining data integrity
ALTER TABLE assignments 
ADD CONSTRAINT chk_driver_id_or_auto 
CHECK (
  driver_id IS NOT NULL OR 
  (notes IS NOT NULL AND notes LIKE '%Auto-assignment%') OR
  (assignment_code IS NOT NULL AND assignment_code LIKE '%AUTO%')
);

-- =============================================================================
-- PART 3: Create function to auto-populate driver from active shifts
-- =============================================================================

-- Function to get current active driver for a truck
CREATE OR REPLACE FUNCTION get_current_active_driver(p_truck_id INTEGER)
RETURNS INTEGER AS $$
DECLARE
    v_driver_id INTEGER;
BEGIN
    -- Get the current active driver for the truck from shift management
    SELECT ds.driver_id INTO v_driver_id
    FROM driver_shifts ds
    WHERE ds.truck_id = p_truck_id
      AND ds.status = 'active'
      AND ds.shift_date = CURRENT_DATE
      AND CURRENT_TIME BETWEEN ds.start_time AND 
          CASE 
            WHEN ds.end_time < ds.start_time 
            THEN ds.end_time + interval '24 hours'
            ELSE ds.end_time 
          END
    ORDER BY ds.created_at DESC
    LIMIT 1;
    
    RETURN v_driver_id;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- PART 4: Create trigger to auto-populate driver_id from shifts
-- =============================================================================

-- Function for the trigger
CREATE OR REPLACE FUNCTION auto_populate_driver_from_shift()
RETURNS TRIGGER AS $$
DECLARE
    v_current_driver_id INTEGER;
BEGIN
    -- Only auto-populate if driver_id is NULL
    IF NEW.driver_id IS NULL THEN
        -- Get current active driver for the truck
        v_current_driver_id := get_current_active_driver(NEW.truck_id);
        
        -- If we found an active driver, use it
        IF v_current_driver_id IS NOT NULL THEN
            NEW.driver_id := v_current_driver_id;
            
            -- Add note about auto-assignment
            IF NEW.notes IS NULL OR NEW.notes = '' THEN
                NEW.notes := '[Auto-assigned driver from active shift]';
            ELSE
                NEW.notes := NEW.notes || ' [Auto-assigned driver from active shift]';
            END IF;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS trg_auto_populate_driver ON assignments;
CREATE TRIGGER trg_auto_populate_driver
    BEFORE INSERT ON assignments
    FOR EACH ROW
    EXECUTE FUNCTION auto_populate_driver_from_shift();

-- =============================================================================
-- PART 5: Update existing assignments with null driver_id
-- =============================================================================

-- Update existing assignments that have null driver_id
-- This handles any existing data that might be in an inconsistent state
UPDATE assignments 
SET driver_id = get_current_active_driver(truck_id),
    notes = COALESCE(notes, '') || ' [Driver auto-populated from shift management]'
WHERE driver_id IS NULL 
  AND get_current_active_driver(truck_id) IS NOT NULL;

-- =============================================================================
-- PART 6: Create view for assignment management with current drivers
-- =============================================================================

-- Enhanced view that shows current driver information from shift management
CREATE OR REPLACE VIEW v_assignments_with_current_drivers AS
SELECT 
    a.*,
    dt.truck_number,
    dt.license_plate,
    d.full_name as assigned_driver_name,
    d.employee_id as assigned_driver_employee_id,
    
    -- Current driver from shift management
    ds.driver_id as current_driver_id,
    cd.full_name as current_driver_name,
    cd.employee_id as current_driver_employee_id,
    ds.shift_type as current_shift_type,
    
    -- Driver status
    CASE 
        WHEN ds.driver_id IS NOT NULL THEN 'active_shift'
        WHEN d.id IS NOT NULL THEN 'assigned_only'
        ELSE 'no_driver'
    END as driver_status,
    
    -- Location information
    ll.name as loading_location_name,
    ul.name as unloading_location_name
FROM assignments a
JOIN dump_trucks dt ON a.truck_id = dt.id
LEFT JOIN drivers d ON a.driver_id = d.id
LEFT JOIN locations ll ON a.loading_location_id = ll.id
LEFT JOIN locations ul ON a.unloading_location_id = ul.id
LEFT JOIN driver_shifts ds ON (
    ds.truck_id = a.truck_id 
    AND ds.status = 'active'
    AND ds.shift_date = CURRENT_DATE
    AND CURRENT_TIME BETWEEN ds.start_time AND 
        CASE 
            WHEN ds.end_time < ds.start_time 
            THEN ds.end_time + interval '24 hours'
            ELSE ds.end_time 
        END
)
LEFT JOIN drivers cd ON ds.driver_id = cd.id;

-- =============================================================================
-- PART 7: Add helpful indexes for performance
-- =============================================================================

-- Index for shift-based driver lookups
CREATE INDEX IF NOT EXISTS idx_driver_shifts_truck_active_current 
ON driver_shifts(truck_id, status, shift_date) 
WHERE status = 'active';

-- Index for assignment-driver relationships
CREATE INDEX IF NOT EXISTS idx_assignments_truck_driver_status 
ON assignments(truck_id, driver_id, status);

-- =============================================================================
-- PART 8: Create helper function for assignment creation
-- =============================================================================

-- Function to create assignment with automatic driver detection
CREATE OR REPLACE FUNCTION create_assignment_with_auto_driver(
    p_assignment_code VARCHAR(50),
    p_truck_id INTEGER,
    p_loading_location_id INTEGER,
    p_unloading_location_id INTEGER,
    p_assigned_date DATE DEFAULT CURRENT_DATE,
    p_priority VARCHAR(20) DEFAULT 'normal',
    p_expected_loads INTEGER DEFAULT 1,
    p_notes TEXT DEFAULT NULL
) RETURNS INTEGER AS $$
DECLARE
    v_assignment_id INTEGER;
    v_driver_id INTEGER;
    v_final_notes TEXT;
BEGIN
    -- Get current active driver
    v_driver_id := get_current_active_driver(p_truck_id);
    
    -- Prepare notes
    v_final_notes := COALESCE(p_notes, '');
    IF v_driver_id IS NOT NULL THEN
        v_final_notes := v_final_notes || ' [Driver auto-assigned from active shift]';
    END IF;
    
    -- Create the assignment
    INSERT INTO assignments (
        assignment_code, truck_id, driver_id,
        loading_location_id, unloading_location_id,
        assigned_date, status, priority,
        expected_loads_per_day, notes,
        created_at, updated_at
    ) VALUES (
        p_assignment_code, p_truck_id, v_driver_id,
        p_loading_location_id, p_unloading_location_id,
        p_assigned_date, 'assigned', p_priority,
        p_expected_loads, v_final_notes,
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    ) RETURNING id INTO v_assignment_id;
    
    RETURN v_assignment_id;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- MIGRATION COMPLETION LOG
-- =============================================================================

-- Create migration_log table if it doesn't exist
CREATE TABLE IF NOT EXISTS migration_log (
    id SERIAL PRIMARY KEY,
    migration_name VARCHAR(255) UNIQUE NOT NULL,
    executed_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    description TEXT
);

-- Log the completion of this migration
INSERT INTO migration_log (migration_name, executed_at, description)
VALUES (
    '018_fix_assignment_database_issues',
    CURRENT_TIMESTAMP,
    'Fixed assignment database issues: added is_active compatibility, driver auto-assignment, and shift integration'
) ON CONFLICT (migration_name) DO UPDATE SET
    executed_at = CURRENT_TIMESTAMP,
    description = EXCLUDED.description;

-- Success message
DO $$ 
BEGIN 
    RAISE NOTICE 'Migration 018 completed successfully: Assignment database issues fixed';
    RAISE NOTICE '- Added is_active compatibility for locations table';
    RAISE NOTICE '- Fixed driver_id constraint violations with auto-assignment';
    RAISE NOTICE '- Integrated shift management with assignment creation';
    RAISE NOTICE '- Created helper functions and views for enhanced functionality';
END $$;
