const { Pool } = require('pg');
require('dotenv').config({ path: '../.env' });

console.log('🔍 Checking Database Constraint Status\n');

async function checkConstraints() {
  const pool = new Pool({
    user: process.env.DB_USER,
    host: process.env.DB_HOST,
    database: process.env.DB_NAME,
    password: process.env.DB_PASSWORD,
    port: process.env.DB_PORT,
  });

  try {
    console.log('🔐 Connecting to database...');
    const client = await pool.connect();
    
    console.log('✅ Database connected successfully');
    
    console.log('\n📋 Checking assignment table constraints...');
    
    // Check all constraints on assignments table
    const constraintsQuery = `
      SELECT 
        conname as constraint_name,
        contype as constraint_type,
        pg_get_constraintdef(oid) as constraint_definition
      FROM pg_constraint 
      WHERE conrelid = 'assignments'::regclass
      ORDER BY conname;
    `;
    
    const constraintsResult = await client.query(constraintsQuery);
    
    console.log(`\n📊 Found ${constraintsResult.rows.length} constraints on assignments table:`);
    console.log('================================================================');
    
    constraintsResult.rows.forEach((constraint, index) => {
      console.log(`\n${index + 1}. ${constraint.constraint_name} (${constraint.constraint_type})`);
      console.log(`   Definition: ${constraint.constraint_definition}`);
      
      if (constraint.constraint_name === 'chk_driver_id_or_auto') {
        console.log('   🚨 FOUND PROBLEMATIC CONSTRAINT!');
      }
      if (constraint.constraint_name === 'chk_assignment_integrity') {
        console.log('   ✅ Found updated constraint from migration 027');
      }
    });
    
    console.log('\n📋 Checking assignments table structure...');
    
    // Check table columns
    const columnsQuery = `
      SELECT 
        column_name,
        data_type,
        is_nullable,
        column_default
      FROM information_schema.columns 
      WHERE table_name = 'assignments'
      ORDER BY ordinal_position;
    `;
    
    const columnsResult = await client.query(columnsQuery);
    
    console.log(`\n📊 Assignments table columns:`);
    console.log('============================');
    
    let hasAutoCreated = false;
    columnsResult.rows.forEach((column, index) => {
      console.log(`${index + 1}. ${column.column_name} (${column.data_type}) - Nullable: ${column.is_nullable} - Default: ${column.column_default || 'None'}`);
      
      if (column.column_name === 'auto_created') {
        hasAutoCreated = true;
        console.log('   ✅ auto_created column exists');
      }
    });
    
    if (!hasAutoCreated) {
      console.log('\n❌ auto_created column is MISSING from assignments table!');
    }
    
    console.log('\n📋 Testing constraint violation scenario...');
    
    // Test what happens with NULL driver_id and auto_created=false
    try {
      const testQuery = `
        SELECT 
          CASE 
            WHEN (driver_id IS NOT NULL) OR (auto_created = true) THEN 'PASS'
            ELSE 'FAIL'
          END as constraint_check
        FROM (
          SELECT NULL::integer as driver_id, false as auto_created
        ) test_data;
      `;
      
      const testResult = await client.query(testQuery);
      console.log(`   Constraint test result: ${testResult.rows[0].constraint_check}`);
      
      if (testResult.rows[0].constraint_check === 'FAIL') {
        console.log('   🚨 This combination would violate chk_driver_id_or_auto constraint!');
      }
    } catch (testError) {
      console.log(`   ❌ Constraint test failed: ${testError.message}`);
    }
    
    console.log('\n📋 Checking recent failed assignments...');
    
    // Check for any assignments that might have failed
    const recentQuery = `
      SELECT 
        id, assignment_code, truck_id, driver_id, auto_created, notes, created_at
      FROM assignments 
      WHERE created_at > NOW() - INTERVAL '1 hour'
      ORDER BY created_at DESC
      LIMIT 5;
    `;
    
    const recentResult = await client.query(recentQuery);
    
    if (recentResult.rows.length > 0) {
      console.log(`\n📊 Recent assignments (last hour):`);
      recentResult.rows.forEach((assignment, index) => {
        console.log(`${index + 1}. ID: ${assignment.id}, Code: ${assignment.assignment_code}`);
        console.log(`   Truck: ${assignment.truck_id}, Driver: ${assignment.driver_id || 'NULL'}`);
        console.log(`   Auto-created: ${assignment.auto_created}, Notes: ${assignment.notes || 'None'}`);
        console.log(`   Created: ${assignment.created_at}`);
        console.log('');
      });
    } else {
      console.log('   No recent assignments found');
    }
    
    client.release();
    
  } catch (error) {
    console.error('❌ Database check failed:', error.message);
    console.error('Full error:', error);
  } finally {
    await pool.end();
  }
}

console.log('🚀 Starting Database Constraint Check');
console.log('📡 Checking database configuration...\n');

checkConstraints().then(() => {
  console.log('\n📊 DATABASE CONSTRAINT CHECK COMPLETE');
  console.log('=====================================');
}).catch(error => {
  console.error('❌ Check execution failed:', error);
});
