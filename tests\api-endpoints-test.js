const http = require('http');
const https = require('https');
const url = require('url');

console.log('🔍 Testing API Endpoints for Stopped Terminology Refactoring\n');

// Configuration
const BASE_URL = process.env.API_BASE_URL || 'http://localhost:5000';
const AUTH_TOKEN = process.env.TEST_TOKEN || '';

// Helper function to make HTTP requests
function makeRequest(endpoint, options = {}) {
  return new Promise((resolve, reject) => {
    const fullUrl = `${BASE_URL}${endpoint}`;
    const parsedUrl = url.parse(fullUrl);
    const isHttps = parsedUrl.protocol === 'https:';
    
    const requestOptions = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port || (isHttps ? 443 : 80),
      path: parsedUrl.path,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${AUTH_TOKEN}`,
        ...options.headers
      }
    };

    const client = isHttps ? https : http;
    
    const req = client.request(requestOptions, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            status: res.statusCode,
            data: jsonData,
            headers: res.headers
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: data,
            headers: res.headers,
            parseError: error.message
          });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    if (options.body) {
      req.write(JSON.stringify(options.body));
    }
    
    req.end();
  });
}

// Test functions
async function testStoppedAnalyticsEndpoint() {
  console.log('📋 Test 1: Stopped Analytics Endpoint');
  
  try {
    const response = await makeRequest('/api/analytics/stopped-analytics?start_date=2025-01-01&end_date=2025-01-31');
    
    if (response.status === 200) {
      console.log('✅ Stopped analytics endpoint accessible');
      
      if (response.data.success) {
        console.log('✅ Response indicates success');
        
        // Check response structure
        const data = response.data.data;
        if (data && data.frequency && data.locationPatterns && data.trends) {
          console.log('✅ Response has correct structure (frequency, locationPatterns, trends)');
          
          // Check for stopped terminology in response
          const responseStr = JSON.stringify(response.data);
          if (responseStr.includes('stoppedCount') && !responseStr.includes('breakdownCount')) {
            console.log('✅ Response uses stopped terminology correctly');
          } else {
            console.log('❌ Response still contains breakdown terminology');
          }
        } else {
          console.log('❌ Response structure incomplete');
        }
      } else {
        console.log('❌ Response indicates failure:', response.data.message);
      }
    } else {
      console.log(`❌ Stopped analytics endpoint failed with status ${response.status}`);
      if (response.data) {
        console.log('   Error:', response.data);
      }
    }
  } catch (error) {
    console.log('❌ Error testing stopped analytics endpoint:', error.message);
  }
}

async function testFleetOverviewEndpoint() {
  console.log('\n📋 Test 2: Fleet Overview Endpoint');
  
  try {
    const response = await makeRequest('/api/analytics/fleet-overview');
    
    if (response.status === 200) {
      console.log('✅ Fleet overview endpoint accessible');
      
      if (response.data.success) {
        console.log('✅ Response indicates success');
        
        const data = response.data.data;
        if (data && data.metrics) {
          // Check for stopped terminology in metrics
          if (data.metrics.trucksInStopped !== undefined && data.metrics.todayStopped !== undefined) {
            console.log('✅ Fleet metrics use stopped terminology');
          } else if (data.metrics.trucksInBreakdown !== undefined) {
            console.log('❌ Fleet metrics still use breakdown terminology');
          } else {
            console.log('⚠️ Fleet metrics structure unclear');
          }
        }
      } else {
        console.log('❌ Response indicates failure:', response.data.message);
      }
    } else {
      console.log(`❌ Fleet overview endpoint failed with status ${response.status}`);
    }
  } catch (error) {
    console.log('❌ Error testing fleet overview endpoint:', error.message);
  }
}

async function testLiveOperationsEndpoint() {
  console.log('\n📋 Test 3: Live Operations Endpoint');
  
  try {
    const response = await makeRequest('/api/analytics/live-operations');
    
    if (response.status === 200) {
      console.log('✅ Live operations endpoint accessible');
      
      if (response.data.success) {
        console.log('✅ Response indicates success');
        
        const data = response.data.data;
        if (data && data.summary && data.summary.alerts) {
          // Check for stopped terminology in alerts
          if (data.summary.alerts.stopped !== undefined) {
            console.log('✅ Live operations alerts use stopped terminology');
          } else if (data.summary.alerts.breakdown !== undefined) {
            console.log('❌ Live operations alerts still use breakdown terminology');
          } else {
            console.log('⚠️ Live operations alerts structure unclear');
          }
        }
      } else {
        console.log('❌ Response indicates failure:', response.data.message);
      }
    } else {
      console.log(`❌ Live operations endpoint failed with status ${response.status}`);
    }
  } catch (error) {
    console.log('❌ Error testing live operations endpoint:', error.message);
  }
}

async function testBreakdownEndpointRemoval() {
  console.log('\n📋 Test 4: Breakdown Analytics Endpoint Removal');
  
  try {
    const response = await makeRequest('/api/analytics/breakdown-analytics?start_date=2025-01-01&end_date=2025-01-31');
    
    if (response.status === 404) {
      console.log('✅ Breakdown analytics endpoint properly removed (404)');
    } else if (response.status === 200) {
      console.log('❌ Breakdown analytics endpoint still exists');
    } else {
      console.log(`⚠️ Breakdown analytics endpoint returned status ${response.status}`);
    }
  } catch (error) {
    console.log('❌ Error testing breakdown endpoint removal:', error.message);
  }
}

async function testServerHealth() {
  console.log('\n📋 Test 5: Server Health Check');
  
  try {
    const response = await makeRequest('/api/health');
    
    if (response.status === 200) {
      console.log('✅ Server is healthy and responding');
    } else {
      console.log(`❌ Server health check failed with status ${response.status}`);
    }
  } catch (error) {
    console.log('❌ Server appears to be down:', error.message);
    return false;
  }
  
  return true;
}

// Main test execution
async function runTests() {
  console.log('🚀 Starting API Endpoint Tests for Stopped Terminology Refactoring');
  console.log(`📡 Testing against: ${BASE_URL}`);
  console.log(`🔑 Using auth token: ${AUTH_TOKEN ? 'Yes' : 'No'}\n`);
  
  // Check if server is running
  const serverHealthy = await testServerHealth();
  
  if (!serverHealthy) {
    console.log('\n❌ Server is not responding. Please start the server and try again.');
    console.log('   Run: npm start');
    return;
  }
  
  // Run all tests
  await testStoppedAnalyticsEndpoint();
  await testFleetOverviewEndpoint();
  await testLiveOperationsEndpoint();
  await testBreakdownEndpointRemoval();
  
  console.log('\n📊 TEST SUMMARY');
  console.log('================');
  console.log('✅ Tests completed successfully');
  console.log('🔍 Check the results above for any issues');
  console.log('\n💡 If tests fail:');
  console.log('   1. Ensure the server is running (npm start)');
  console.log('   2. Check that the database is accessible');
  console.log('   3. Verify authentication token if required');
  console.log('   4. Review server logs for errors');
}

// Run the tests
runTests().catch(error => {
  console.error('❌ Test execution failed:', error);
  process.exit(1);
});
