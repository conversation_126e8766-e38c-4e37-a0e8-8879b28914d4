<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hauling QR Trip System - Complete Workflow</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            background-color: #1e1e1e;
            color: #fff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 100%;
            overflow: auto;
            margin-bottom: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #4caf50;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .header p {
            color: #b0b0b0;
            font-size: 1.1em;
            margin-bottom: 5px;
        }
        .version {
            color: #666;
            font-size: 0.9em;
            font-style: italic;
        }
        .instructions {
            background: linear-gradient(135deg, #2d2d2d, #3a3a3a);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            max-width: 900px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            border: 1px solid #4caf50;
        }
        .instructions h2 {
            color: #4caf50;
            margin-top: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature-item {
            background-color: #333;
            padding: 10px;
            border-radius: 5px;
            border-left: 3px solid #4caf50;
        }
        .feature-item h4 {
            margin: 0 0 5px 0;
            color: #4caf50;
            font-size: 0.9em;
        }
        .feature-item p {
            margin: 0;
            color: #ccc;
            font-size: 0.8em;
        }
        ol {
            line-height: 1.8;
            padding-left: 20px;
        }
        ol li {
            margin-bottom: 8px;
            color: #e0e0e0;
        }
        .highlight {
            background-color: #4caf50;
            color: #000;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
        }
        #diagram {
            width: 100%;
            max-width: 2000px;
            margin: 0 auto;
            background-color: #1e1e1e;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.4);
        }
        .mermaid {
            background-color: #1e1e1e;
        }
        .footer {
            margin-top: 30px;
            padding: 20px;
            background-color: #2d2d2d;
            border-radius: 10px;
            text-align: center;
            max-width: 900px;
            color: #888;
        }
        .copyright {
            color: #4caf50;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚛 Hauling QR Trip System</h1>
        <p>Complete Workflow & System Architecture</p>
        <p class="version">Enhanced with Auto-Assignment Creation & Dynamic Route Discovery</p>
        <p class="copyright">© 2025 • Developer: Ariez-AI</p>
    </div>
    
    <div class="instructions">
        <h2>📋 System Features & Workflow</h2>
        <div class="feature-grid">
            <div class="feature-item">
                <h4>🎯 Auto-Assignment Creation</h4>
                <p>Intelligent assignment generation based on truck patterns and location context</p>
            </div>
            <div class="feature-item">
                <h4>🔄 Dynamic Route Discovery</h4>
                <p>Progressive route learning with real-time location updates</p>
            </div>
            <div class="feature-item">
                <h4>⚡ Exception Management</h4>
                <p>Automated exception handling with approval workflow integration</p>
            </div>
            <div class="feature-item">
                <h4>📊 Assignment Monitoring</h4>
                <p>Real-time tracking of assignment creation and status updates</p>
            </div>
            <div class="feature-item">
                <h4>📈 Analytics & Reports</h4>
                <p>Comprehensive performance metrics with CSV/PDF export capabilities</p>
            </div>
            <div class="feature-item">
                <h4>🔔 WebSocket Notifications</h4>
                <p>Real-time system notifications for route discovery and exceptions</p>
            </div>
        </div>
        
        <h3>💾 How to Save Diagram:</h3>
        <ol>
            <li>Wait for the diagram to <span class="highlight">fully render</span> below</li>
            <li><span class="highlight">Right-click</span> directly on the flowchart diagram</li>
            <li>Select <span class="highlight">"Save image as..."</span> from the context menu</li>
            <li>Choose your preferred location and filename</li>
            <li>Click <span class="highlight">Save</span> to download the diagram</li>
        </ol>
        <p><strong>Alternative:</strong> Use screenshot tools like <code>Windows + Shift + S</code> or <code>PrtScn</code> key for quick capture.</p>
    </div>
    
    <div id="diagram" class="container">
        <div class="mermaid">
flowchart TD
    A["📱 QR Scanner App"] --> B{"Scan Type?"}
    B -->|Location| C["📍 Location Scan"]
    B -->|Truck| D["🚛 Truck Scan"]
    
    C --> E["Validate Location QR"]
    E -->|Valid| F["Store Location Data"]
    E -->|Invalid| G["❌ Invalid QR Error"]
    
    F --> H["✅ Location Stored - Scan Truck Next"]
    
    D --> I["Validate Truck QR"]
    I -->|Valid| J["🔍 Enhanced Assignment Validation"]
    I -->|Invalid| K["❌ Invalid QR Error"]
    
    J --> L{"Assignment Found?"}
    
    L -->|Yes| M["✅ Valid Assignment Found"]
    L -->|No| N["🤖 AutoAssignmentCreator Check"]
    
    M --> O{"Active Trip Exists?"}
    
    O -->|Yes| P["📊 Trip Progression Logic"]
    O -->|No| Q["🆕 Create New Trip"]
    
    N --> R{"Should Create Assignment?"}
    
    R -->|Yes| S["🔧 Dynamic Assignment Creation"]
    R -->|No| T["⚠️ Exception Flow Triggered"]
    
    S --> U["✅ Assignment Created - Status: assigned"]
    S --> S1["🔄 Route Discovery Mode"]
    
    S1 --> S2{"Discovery Type?"}
    S2 -->|Loading Known| S3["📍 Loading → [Predicted Unloading]"]
    S2 -->|Unloading Known| S4["📍 [Predicted Loading] → Unloading"]
    S2 -->|Flexible| S5["📍 Flexible Assignment Creation"]
    
    U --> Q
    S3 --> Q
    S4 --> Q
    S5 --> Q
    
    T --> T1["📋 Exception Request Creation"]
    T1 --> T2["⏳ Awaiting Admin Approval"]
    T2 --> T3{"Approval Decision?"}
    T3 -->|Approved| T4["🔧 Auto-Assignment from Exception"]
    T3 -->|Rejected| T5["❌ Trip Cancelled"]
    T4 --> Q
    
    P --> V{"Current Status?"}
    
    V -->|loading_start| W["📦 Complete Loading"]
    V -->|loading_end| X["🚛 Start Travel"]
    V -->|unloading_start| Y["📤 Complete Unloading"]
    V -->|unloading_end| Z["✅ Trip Completed"]
    
    Q --> AA["🎯 Determine Initial Action"]
    
    AA --> BB{"Location Role?"}
    
    BB -->|Loading| CC["📦 Start Loading"]
    BB -->|Unloading| DD["📤 Start Unloading"]
    
    CC --> EE["Status: loading_start"]
    DD --> FF["Status: unloading_start"]
    
    W --> GG["Status: loading_end"]
    X --> HH["Status: travel"]
    Y --> II["Status: unloading_end"]
    Z --> JJ["Status: trip_completed"]
    
    EE --> KK["📡 WebSocket Notification"]
    FF --> KK
    GG --> KK
    HH --> KK
    II --> KK
    JJ --> KK
    S1 --> KK1["🔔 Route Discovery Notifications"]
    
    KK --> LL["📊 Update Assignment Monitoring"]
    KK1 --> LL
    LL --> MM["📈 Update Analytics Dashboard"]
    MM --> NN["✅ Response to Frontend"]
    
    %% Route Discovery Updates
    S3 --> RD1["🔄 Monitor Route Progression"]
    S4 --> RD1
    S5 --> RD1
    RD1 --> RD2{"Location Confirmed?"}
    RD2 -->|Different| RD3["📝 Update Assignment Route"]
    RD2 -->|Same| RD4["✅ Route Prediction Confirmed"]
    RD3 --> KK1
    RD4 --> KK1
    
    %% Styling
    classDef autoProcess fill:#4caf50,stroke:#2e7d32,stroke-width:2px,color:#fff
    classDef exceptionProcess fill:#ff9800,stroke:#f57c00,stroke-width:2px,color:#fff
    classDef discoveryProcess fill:#2196f3,stroke:#1565c0,stroke-width:2px,color:#fff
    classDef monitoringProcess fill:#9c27b0,stroke:#6a1b9a,stroke-width:2px,color:#fff
    
    class S,S1,S2,S3,S4,S5,U autoProcess
    class T,T1,T2,T3,T4,T5 exceptionProcess
    class RD1,RD2,RD3,RD4 discoveryProcess
    class LL,MM,NN,KK1 monitoringProcess
        </div>
    </div>

    <div class="footer">
        <h3>🎯 System Architecture Highlights</h3>
        <div class="feature-grid">
            <div class="feature-item">
                <h4>Auto-Assignment (Green)</h4>
                <p>Intelligent assignment creation with dynamic route discovery</p>
            </div>
            <div class="feature-item">
                <h4>Exception Flow (Orange)</h4>
                <p>Comprehensive exception handling with approval workflow</p>
            </div>
            <div class="feature-item">
                <h4>Route Discovery (Blue)</h4>
                <p>Progressive route learning and real-time updates</p>
            </div>
            <div class="feature-item">
                <h4>Monitoring (Purple)</h4>
                <p>Real-time dashboard updates and analytics integration</p>
            </div>
        </div>
        <p style="margin-top: 20px;">
            <strong>© 2025 Hauling QR Trip System</strong><br>
            <span class="copyright">Developer: Ariez-AI</span> • Enhanced Workflow Architecture
        </p>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'dark',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });
    </script>
</body>
</html>