import React, { useState, useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import Sidebar from './Sidebar';
import Header from './Header';

// Lazy-loaded page components
const Dashboard = React.lazy(() => import('../../pages/dashboard/Dashboard'));
const UsersManagement = React.lazy(() => import('../../pages/users/UsersManagement'));
const TrucksManagement = React.lazy(() => import('../../pages/trucks/TrucksManagement'));
const DriversManagement = React.lazy(() => import('../../pages/drivers/DriversManagement'));
const LocationsManagement = React.lazy(() => import('../../pages/locations/LocationsManagement'));
const AssignmentsManagement = React.lazy(() => import('../../pages/assignments/AssignmentsManagement'));
const TripMonitoring = React.lazy(() => import('../../pages/trips/TripMonitoring'));
const QRScanner = React.lazy(() => import('../../pages/scanner/QRScanner'));
const UnifiedAnalytics = React.lazy(() => import('../../pages/analytics/UnifiedAnalytics'));
const AssignmentMonitoring = React.lazy(() => import('../../pages/assignment-monitoring/AssignmentMonitoring'));
const ShiftManagement = React.lazy(() => import('../../pages/shifts/ShiftManagement'));
const ShiftAssignmentIntegration = React.lazy(() => import('../../pages/shifts/ShiftAssignmentIntegration'));

const Settings = React.lazy(() => import('../../pages/settings/Settings'));
const TruckTripSummary = React.lazy(() => import('../../pages/trucks/TruckTripSummary'));

const DashboardLayout = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  // Initialize sidebar hidden state from localStorage, default to false
  const [sidebarHidden, setSidebarHidden] = useState(() => {
    const saved = localStorage.getItem('sidebarHidden');
    return saved ? JSON.parse(saved) : false;
  });
  const { user } = useAuth();

  // Save sidebar hidden state to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('sidebarHidden', JSON.stringify(sidebarHidden));
  }, [sidebarHidden]);

  return (
    <div className="min-h-screen bg-secondary-50 flex">
      {/* Sidebar - conditionally rendered */}
      {!sidebarHidden && <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />}
      
      {/* Main Content */}
      <div className={`flex-1 flex flex-col ${!sidebarHidden ? 'lg:ml-64' : ''}`}>        {/* Header */}
        <Header
          user={user}
          sidebarOpen={sidebarOpen}
          setSidebarOpen={setSidebarOpen}
          sidebarHidden={sidebarHidden}
          setSidebarHidden={setSidebarHidden}
        />
          {/* Main Content Area */}
        <main className="flex-1 overflow-y-auto">
          <div className="py-6">
            <div className={`mx-auto px-4 sm:px-6 lg:px-8 ${sidebarHidden ? 'max-w-none' : 'max-w-7xl'}`}>
              <Routes>
                <Route path="/" element={<Navigate to="/dashboard" replace />} />
                <Route path="/dashboard" element={<Dashboard />} />
                <Route path="/users" element={<UsersManagement />} />
                <Route path="/trucks" element={<TrucksManagement />} />
                <Route path="/trucks/trip-summary" element={<TruckTripSummary />} />
                <Route path="/drivers" element={<DriversManagement />} />
                <Route path="/locations" element={<LocationsManagement />} />
                <Route path="/assignments" element={<AssignmentsManagement />} />
                <Route path="/trips" element={<TripMonitoring />} />
                <Route path="/scanner" element={<QRScanner />} />
                <Route path="/assignment-monitoring" element={<AssignmentMonitoring />} />
                <Route path="/shifts" element={<ShiftManagement />} />
                <Route path="/shifts/integration" element={<ShiftAssignmentIntegration />} />

                <Route path="/analytics" element={<UnifiedAnalytics />} />
                <Route path="/analytics-reports" element={<UnifiedAnalytics />} />
                <Route path="/settings" element={<Settings />} />
              </Routes>
            </div>
          </div>
        </main>
      </div>
        {/* Mobile sidebar backdrop */}
      {!sidebarHidden && sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
};

export default DashboardLayout;