#!/usr/bin/env node

/**
 * Multi-Driver System Test Runner
 * Executes comprehensive tests for the multi-driver shift management system
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 Multi-Driver System Test Runner');
console.log('=====================================');

// Test configuration
const testConfig = {
  testFile: './tests/multi-driver-system-tests.js',
  timeout: 60000, // 60 seconds
  reporter: 'spec',
  colors: true
};

// Check if test file exists
if (!fs.existsSync(testConfig.testFile)) {
  console.error('❌ Test file not found:', testConfig.testFile);
  process.exit(1);
}

// Check if server is running
const checkServer = async () => {
  try {
    // Try the correct port first (5444)
    const response = await fetch('http://localhost:5444/api/auth/health');
    if (response.ok) {
      console.log('✅ Server is running on port 5444');
      return true;
    }
  } catch (error) {
    // Try fallback port (5000)
    try {
      const response = await fetch('http://localhost:5000/api/auth/health');
      if (response.ok) {
        console.log('✅ Server is running on port 5000');
        return true;
      }
    } catch (error2) {
      console.log('⚠️ Server not accessible on either port');
      return false;
    }
  }
  return false;
};

// Start server if needed
const startServer = () => {
  return new Promise((resolve, reject) => {
    console.log('🔧 Starting server...');
    
    const serverProcess = spawn('npm', ['run', 'dev'], {
      cwd: './server',
      stdio: 'pipe',
      shell: true
    });

    let serverReady = false;

    serverProcess.stdout.on('data', (data) => {
      const output = data.toString();
      console.log('Server:', output.trim());
      
      if (output.includes('Server running on port') || output.includes('listening on')) {
        if (!serverReady) {
          serverReady = true;
          console.log('✅ Server started successfully');
          resolve(serverProcess);
        }
      }
    });

    serverProcess.stderr.on('data', (data) => {
      console.error('Server Error:', data.toString());
    });

    serverProcess.on('error', (error) => {
      console.error('❌ Failed to start server:', error);
      reject(error);
    });

    // Timeout after 30 seconds
    setTimeout(() => {
      if (!serverReady) {
        console.error('❌ Server startup timeout');
        serverProcess.kill();
        reject(new Error('Server startup timeout'));
      }
    }, 30000);
  });
};

// Run tests
const runTests = () => {
  return new Promise((resolve, reject) => {
    console.log('🧪 Running Multi-Driver System Tests...');
    console.log('=====================================');

    const testProcess = spawn('npx', [
      'mocha',
      testConfig.testFile,
      '--timeout', testConfig.timeout.toString(),
      '--reporter', testConfig.reporter,
      testConfig.colors ? '--colors' : '--no-colors'
    ], {
      stdio: 'inherit',
      shell: true
    });

    testProcess.on('close', (code) => {
      if (code === 0) {
        console.log('✅ All tests passed successfully!');
        resolve(true);
      } else {
        console.log('❌ Some tests failed');
        resolve(false);
      }
    });

    testProcess.on('error', (error) => {
      console.error('❌ Test execution error:', error);
      reject(error);
    });
  });
};

// Generate test report
const generateReport = (testResults) => {
  const report = {
    timestamp: new Date().toISOString(),
    testSuite: 'Multi-Driver System Tests',
    status: testResults ? 'PASSED' : 'FAILED',
    coverage: [
      'Shift Management CRUD Operations',
      'Assignment Management (Driver-less)',
      'Scanner Integration with Shifts',
      'Trip Monitoring with Multi-Driver Display',
      'Analytics with Multi-Driver Support',
      'Settings Page Administrative Tools',
      'System Integration Tests',
      '4-Phase Workflow Integrity'
    ],
    recommendations: testResults ? [
      'System is ready for production deployment',
      'All multi-driver features working correctly',
      'No disruption to existing 4-phase workflow',
      'Settings page organization successful'
    ] : [
      'Review failed test cases',
      'Check server configuration',
      'Verify database schema',
      'Test individual components'
    ]
  };

  const reportPath = './test-results.json';
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  console.log(`📊 Test report saved to: ${reportPath}`);

  return report;
};

// Main execution
const main = async () => {
  try {
    console.log('🔍 Checking system prerequisites...');
    
    // Check if server is running
    const serverRunning = await checkServer();
    let serverProcess = null;

    if (!serverRunning) {
      serverProcess = await startServer();
      // Wait a bit for server to fully initialize
      await new Promise(resolve => setTimeout(resolve, 5000));
    }

    // Run the tests
    const testResults = await runTests();

    // Generate report
    const report = generateReport(testResults);

    // Display summary
    console.log('\n📋 Test Summary');
    console.log('================');
    console.log(`Status: ${report.status}`);
    console.log(`Timestamp: ${report.timestamp}`);
    console.log('\n🎯 Test Coverage:');
    report.coverage.forEach(item => {
      console.log(`   ✓ ${item}`);
    });

    console.log('\n💡 Recommendations:');
    report.recommendations.forEach(item => {
      console.log(`   • ${item}`);
    });

    // Cleanup
    if (serverProcess) {
      console.log('\n🧹 Cleaning up server process...');
      serverProcess.kill();
    }

    console.log('\n🎉 Test execution completed!');
    process.exit(testResults ? 0 : 1);

  } catch (error) {
    console.error('❌ Test runner error:', error);
    process.exit(1);
  }
};

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n⚠️ Test execution interrupted');
  process.exit(1);
});

process.on('SIGTERM', () => {
  console.log('\n⚠️ Test execution terminated');
  process.exit(1);
});

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  runTests,
  generateReport,
  checkServer,
  startServer
};
