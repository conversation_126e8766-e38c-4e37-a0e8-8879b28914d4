# Production Monitoring and Uptime Setup

## High Availability Configuration

### 1. Enhanced Health Checks

Add comprehensive health monitoring to `docker-compose.yml`:

```yaml
services:
  app:
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    deploy:
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

  postgres:
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
```

### 2. Application-Level Health Monitoring

Enhance the health endpoint in `server/server.js`:

```javascript
app.get('/health', async (req, res) => {
  const health = {
    status: 'OK',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    checks: {}
  };

  try {
    // Database health check
    const dbResult = await query('SELECT 1 as health');
    health.checks.database = {
      status: 'healthy',
      responseTime: Date.now()
    };
  } catch (error) {
    health.checks.database = {
      status: 'unhealthy',
      error: error.message
    };
    health.status = 'DEGRADED';
  }

  // WebSocket health check
  health.checks.websocket = {
    status: wss ? 'healthy' : 'unhealthy',
    connections: wss ? wss.clients.size : 0
  };

  // Disk space check
  const stats = require('fs').statSync('.');
  health.checks.disk = {
    status: 'healthy' // Add actual disk space check
  };

  res.status(health.status === 'OK' ? 200 : 503).json(health);
});
```

### 3. External Monitoring Setup

#### Option A: UptimeRobot (Free)

```bash
# Set up monitoring URLs:
# 1. https://yourdomain.com/api/health
# 2. Check every 5 minutes
# 3. Alert via email/SMS on downtime
```

#### Option B: Pingdom (Paid)

```bash
# Advanced monitoring with:
# - Response time tracking
# - Geographic monitoring
# - Performance insights
# - Custom alerts
```

### 4. Log Aggregation and Monitoring

#### Centralized Logging with ELK Stack (Optional)

```yaml
# Add to docker-compose.yml
services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.15.0
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data

  logstash:
    image: docker.elastic.co/logstash/logstash:7.15.0
    volumes:
      - ./logstash.conf:/usr/share/logstash/pipeline/logstash.conf

  kibana:
    image: docker.elastic.co/kibana/kibana:7.15.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
```

#### Simple Log Monitoring Script

```bash
#!/bin/bash
# /usr/local/bin/monitor-hauling-logs.sh

LOG_FILE="/var/lib/docker/volumes/hauling-qr-system_app_logs/_data/app.log"
ERROR_THRESHOLD=10
ALERT_EMAIL="<EMAIL>"

# Count errors in last hour
ERROR_COUNT=$(grep -c "ERROR\|CRITICAL" "$LOG_FILE" | tail -n 100)

if [ "$ERROR_COUNT" -gt "$ERROR_THRESHOLD" ]; then
    echo "High error rate detected: $ERROR_COUNT errors" | \
    mail -s "Hauling System Alert: High Error Rate" "$ALERT_EMAIL"
fi

# Check disk space
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -gt 85 ]; then
    echo "Disk usage is at ${DISK_USAGE}%" | \
    mail -s "Hauling System Alert: Low Disk Space" "$ALERT_EMAIL"
fi

# Check memory usage
MEMORY_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
if [ "$MEMORY_USAGE" -gt 90 ]; then
    echo "Memory usage is at ${MEMORY_USAGE}%" | \
    mail -s "Hauling System Alert: High Memory Usage" "$ALERT_EMAIL"
fi
```

### 5. Performance Monitoring

#### Application Performance Monitoring

```javascript
// Add to server/server.js
const responseTime = require('response-time');

// Track response times
app.use(responseTime((req, res, time) => {
  if (time > 1000) { // Log slow requests
    console.warn(`Slow request: ${req.method} ${req.url} - ${time}ms`);
  }
}));

// Performance metrics endpoint
app.get('/metrics', (req, res) => {
  const metrics = {
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    cpu: process.cpuUsage(),
    activeConnections: wss ? wss.clients.size : 0,
    timestamp: new Date().toISOString()
  };
  res.json(metrics);
});
```

#### Database Performance Monitoring

```sql
-- Add to database monitoring script
SELECT 
  schemaname,
  tablename,
  n_tup_ins as inserts,
  n_tup_upd as updates,
  n_tup_del as deletes,
  n_live_tup as live_tuples,
  n_dead_tup as dead_tuples
FROM pg_stat_user_tables 
WHERE schemaname = 'public'
ORDER BY n_tup_ins + n_tup_upd + n_tup_del DESC;
```

### 6. Automated Backup and Recovery

#### Database Backup Script

```bash
#!/bin/bash
# /usr/local/bin/backup-hauling-system.sh

BACKUP_DIR="/var/backups/hauling-qr-system"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=30

mkdir -p "$BACKUP_DIR"

# Database backup
echo "Starting database backup..."
docker exec hauling-postgres pg_dump -U postgres hauling_qr_system | \
gzip > "$BACKUP_DIR/db_backup_$DATE.sql.gz"

# Application logs backup
echo "Backing up application logs..."
tar -czf "$BACKUP_DIR/logs_backup_$DATE.tar.gz" \
/var/lib/docker/volumes/hauling-qr-system_app_logs/_data/

# Configuration backup
echo "Backing up configuration..."
tar -czf "$BACKUP_DIR/config_backup_$DATE.tar.gz" \
/home/<USER>/hauling-qr-system/.env.production \
/home/<USER>/hauling-qr-system/docker-compose.yml

# Cleanup old backups
find "$BACKUP_DIR" -name "*backup_*.gz" -mtime +$RETENTION_DAYS -delete

echo "Backup completed: $BACKUP_DIR"

# Test backup integrity
if gunzip -t "$BACKUP_DIR/db_backup_$DATE.sql.gz"; then
    echo "✅ Database backup integrity verified"
else
    echo "❌ Database backup integrity check failed"
    exit 1
fi
```

### 7. Disaster Recovery Plan

#### Recovery Procedures

```bash
# 1. Restore from backup
#!/bin/bash
# restore-hauling-system.sh

BACKUP_FILE="$1"
if [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <backup_file.sql.gz>"
    exit 1
fi

# Stop services
docker-compose down

# Restore database
gunzip -c "$BACKUP_FILE" | docker exec -i hauling-postgres psql -U postgres -d hauling_qr_system

# Restart services
docker-compose up -d

echo "System restored from backup: $BACKUP_FILE"
```

### 8. Monitoring Dashboard Setup

#### Simple Status Page

```html
<!DOCTYPE html>
<html>
<head>
    <title>Hauling System Status</title>
    <meta http-equiv="refresh" content="30">
</head>
<body>
    <h1>Hauling QR Trip System Status</h1>
    <div id="status"></div>
    
    <script>
        async function checkStatus() {
            try {
                const response = await fetch('/api/health');
                const health = await response.json();
                
                document.getElementById('status').innerHTML = `
                    <h2>Status: ${health.status}</h2>
                    <p>Uptime: ${Math.floor(health.uptime / 3600)}h ${Math.floor((health.uptime % 3600) / 60)}m</p>
                    <p>Database: ${health.checks.database.status}</p>
                    <p>WebSocket: ${health.checks.websocket.status}</p>
                    <p>Last Updated: ${new Date().toLocaleString()}</p>
                `;
            } catch (error) {
                document.getElementById('status').innerHTML = `
                    <h2 style="color: red;">System Offline</h2>
                    <p>Error: ${error.message}</p>
                `;
            }
        }
        
        checkStatus();
        setInterval(checkStatus, 30000);
    </script>
</body>
</html>
```

### 9. Alerting Configuration

#### Email Alerts Setup

```bash
# Install mail utilities
sudo apt install -y mailutils

# Configure postfix for sending emails
sudo dpkg-reconfigure postfix

# Test email
echo "Test email from Hauling System" | mail -s "Test" <EMAIL>
```

#### Slack Alerts (Optional)

```bash
#!/bin/bash
# Send alert to Slack
send_slack_alert() {
    local message="$1"
    local webhook_url="YOUR_SLACK_WEBHOOK_URL"
    
    curl -X POST -H 'Content-type: application/json' \
    --data "{\"text\":\"🚨 Hauling System Alert: $message\"}" \
    "$webhook_url"
}

# Usage
send_slack_alert "Database connection failed"
```

### 10. Monitoring Checklist

#### Daily Checks
- [ ] System health status
- [ ] Database connectivity
- [ ] WebSocket connections
- [ ] Disk space usage
- [ ] Memory usage
- [ ] Error log review

#### Weekly Checks
- [ ] Backup integrity verification
- [ ] Performance metrics review
- [ ] Security log review
- [ ] System updates check

#### Monthly Checks
- [ ] Full system backup test
- [ ] Disaster recovery drill
- [ ] Performance optimization review
- [ ] Security audit
