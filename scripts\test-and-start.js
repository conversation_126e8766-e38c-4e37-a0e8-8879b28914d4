#!/usr/bin/env node
/**
 * Test and Start Script
 * Starts servers and performs live testing
 */

const { spawn } = require('child_process');
const { loadConfig, writeClientEnv } = require('../config-loader');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

class LiveTester {
  constructor() {
    this.serverProcess = null;
    this.clientProcess = null;
  }

  async testHTTPMode() {
    console.log('\n🔵 TESTING HTTP MODE');
    console.log('='.repeat(50));
    
    // Configure for HTTP
    await this.setMode('development', false);
    const config = loadConfig();
    
    console.log('📋 HTTP Configuration:');
    console.log(`- Backend Port: ${config.BACKEND_HTTP_PORT}`);
    console.log(`- Frontend Port: ${config.CLIENT_PORT}`);
    console.log(`- API URL: ${config.API_BASE_URL}`);
    console.log(`- WebSocket URL: ${config.WS_URL}`);
    
    // Start backend server
    console.log('\n🚀 Starting Backend Server (HTTP)...');
    await this.startBackend();
    
    // Wait for server to start
    await this.sleep(3000);
    
    // Test backend
    console.log('\n🧪 Testing Backend Connectivity...');
    const backendResult = await this.testBackendConnectivity(config.BACKEND_HTTP_PORT, 'http');
    
    // Start frontend
    console.log('\n🚀 Starting Frontend Server...');
    await this.startFrontend();
    
    // Wait for frontend to start
    await this.sleep(5000);
    
    // Test frontend
    console.log('\n🧪 Testing Frontend Accessibility...');
    const frontendResult = await this.testFrontendAccessibility(config.CLIENT_PORT, 'http');
    
    return { backend: backendResult, frontend: frontendResult };
  }

  async testHTTPSMode() {
    console.log('\n🔴 TESTING HTTPS MODE');
    console.log('='.repeat(50));
    
    // Stop existing servers
    await this.stopServers();
    
    // Configure for HTTPS
    await this.setMode('development', true);
    const config = loadConfig();
    
    console.log('📋 HTTPS Configuration:');
    console.log(`- Backend Port: ${config.HTTPS_PORT}`);
    console.log(`- Frontend Port: ${config.CLIENT_PORT}`);
    console.log(`- API URL: ${config.API_BASE_URL}`);
    console.log(`- WebSocket URL: ${config.WS_URL}`);
    
    // Check SSL certificates
    const sslDevPath = path.join(__dirname, '..', 'server', 'ssl', 'dev');
    const certExists = fs.existsSync(path.join(sslDevPath, 'server.crt'));
    const keyExists = fs.existsSync(path.join(sslDevPath, 'server.key'));
    
    console.log(`- SSL Cert Exists: ${certExists}`);
    console.log(`- SSL Key Exists: ${keyExists}`);
    
    if (!certExists || !keyExists) {
      console.log('⚠️  SSL certificates missing. Generating...');
      await this.generateSSLCerts();
    }
    
    // Start backend server
    console.log('\n🚀 Starting Backend Server (HTTPS)...');
    await this.startBackend();
    
    // Wait for server to start
    await this.sleep(3000);
    
    // Test backend
    console.log('\n🧪 Testing Backend Connectivity...');
    const backendResult = await this.testBackendConnectivity(config.HTTPS_PORT, 'https');
    
    // Start frontend
    console.log('\n🚀 Starting Frontend Server...');
    await this.startFrontend();
    
    // Wait for frontend to start
    await this.sleep(5000);
    
    // Test frontend
    console.log('\n🧪 Testing Frontend Accessibility...');
    const frontendResult = await this.testFrontendAccessibility(config.CLIENT_PORT, 'https');
    
    return { backend: backendResult, frontend: frontendResult };
  }

  async startBackend() {
    return new Promise((resolve) => {
      this.serverProcess = spawn('node', ['server.js'], {
        cwd: path.join(__dirname, '..', 'server'),
        stdio: 'pipe',
        detached: false
      });
      
      this.serverProcess.stdout.on('data', (data) => {
        const output = data.toString();
        console.log('📡 Backend:', output.trim());
        if (output.includes('Server running on port')) {
          resolve();
        }
      });
      
      this.serverProcess.stderr.on('data', (data) => {
        console.log('❌ Backend Error:', data.toString().trim());
      });
      
      // Resolve after timeout even if no specific message
      setTimeout(resolve, 2000);
    });
  }

  async startFrontend() {
    return new Promise((resolve) => {
      this.clientProcess = spawn('npm', ['start'], {
        cwd: path.join(__dirname, '..', 'client'),
        stdio: 'pipe',
        detached: false,
        shell: true
      });
      
      this.clientProcess.stdout.on('data', (data) => {
        const output = data.toString();
        console.log('🌐 Frontend:', output.trim());
        if (output.includes('webpack compiled') || output.includes('Local:')) {
          resolve();
        }
      });
      
      this.clientProcess.stderr.on('data', (data) => {
        console.log('⚠️  Frontend:', data.toString().trim());
      });
      
      // Resolve after timeout
      setTimeout(resolve, 3000);
    });
  }

  async testBackendConnectivity(port, protocol) {
    try {
      const url = `${protocol}://localhost:${port}/health`;
      console.log(`   Testing: ${url}`);
      
      const axiosConfig = { timeout: 5000 };
      if (protocol === 'https') {
        const https = require('https');
        axiosConfig.httpsAgent = new https.Agent({ rejectUnauthorized: false });
      }
      
      const response = await axios.get(url, axiosConfig);
      console.log('✅ Backend: WORKING');
      console.log(`   Status: ${response.status}`);
      console.log(`   Message: ${response.data.message}`);
      return true;
    } catch (error) {
      console.log('❌ Backend: FAILED');
      console.log(`   Error: ${error.message}`);
      return false;
    }
  }

  async testFrontendAccessibility(port, protocol) {
    try {
      const url = `${protocol}://localhost:${port}`;
      console.log(`   Testing: ${url}`);
      
      const axiosConfig = { 
        timeout: 5000,
        validateStatus: () => true
      };
      if (protocol === 'https') {
        const https = require('https');
        axiosConfig.httpsAgent = new https.Agent({ rejectUnauthorized: false });
      }
      
      const response = await axios.get(url, axiosConfig);
      console.log('✅ Frontend: ACCESSIBLE');
      console.log(`   Status: ${response.status}`);
      return true;
    } catch (error) {
      console.log('❌ Frontend: NOT ACCESSIBLE');
      console.log(`   Error: ${error.message}`);
      return false;
    }
  }

  async setMode(env, enableHttps) {
    const envPath = path.join(__dirname, '..', '.env');
    let envContent = fs.readFileSync(envPath, 'utf8');
    
    envContent = envContent.replace(/^NODE_ENV=.*/m, `NODE_ENV=${env}`);
    envContent = envContent.replace(/^ENABLE_HTTPS=.*/m, `ENABLE_HTTPS=${enableHttps}`);
    
    fs.writeFileSync(envPath, envContent);
    
    const config = loadConfig();
    writeClientEnv(config);
    
    console.log(`🔧 Configured for ${env} mode with HTTPS ${enableHttps ? 'enabled' : 'disabled'}`);
  }

  async generateSSLCerts() {
    return new Promise((resolve, reject) => {
      const generateCerts = spawn('node', ['ssl/generate-dev-certs.js'], {
        cwd: path.join(__dirname, '..', 'server'),
        stdio: 'inherit'
      });
      
      generateCerts.on('exit', (code) => {
        if (code === 0) {
          console.log('✅ SSL certificates generated');
          resolve();
        } else {
          console.log('❌ Failed to generate SSL certificates');
          reject(new Error('SSL generation failed'));
        }
      });
    });
  }

  async stopServers() {
    console.log('\n🛑 Stopping servers...');
    
    if (this.serverProcess) {
      this.serverProcess.kill('SIGTERM');
      this.serverProcess = null;
    }
    
    if (this.clientProcess) {
      this.clientProcess.kill('SIGTERM');
      this.clientProcess = null;
    }
    
    await this.sleep(2000);
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Main execution
async function main() {
  console.log('🧪 LIVE SYSTEM TESTING');
  console.log('='.repeat(50));
  console.log('This script will start servers and test both HTTP and HTTPS modes\n');
  
  const tester = new LiveTester();
  
  try {
    // Test HTTP mode
    const httpResults = await tester.testHTTPMode();
    
    console.log('\n⏸️  Pausing before HTTPS test...');
    await tester.sleep(3000);
    
    // Test HTTPS mode
    const httpsResults = await tester.testHTTPSMode();
    
    // Display final results
    console.log('\n📊 FINAL TEST RESULTS');
    console.log('='.repeat(50));
    console.log('🔵 HTTP Mode:');
    console.log(`   Backend:  ${httpResults.backend ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   Frontend: ${httpResults.frontend ? '✅ PASS' : '❌ FAIL'}`);
    
    console.log('\n🔴 HTTPS Mode:');
    console.log(`   Backend:  ${httpsResults.backend ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   Frontend: ${httpsResults.frontend ? '✅ PASS' : '❌ FAIL'}`);
    
    console.log('\n🎯 Servers are still running for manual testing!');
    console.log('Press Ctrl+C to stop all servers.');
    
    // Keep process alive
    process.on('SIGINT', async () => {
      await tester.stopServers();
      process.exit(0);
    });
    
    // Keep alive
    setInterval(() => {}, 1000);
    
  } catch (error) {
    console.error('❌ Testing failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { LiveTester };
