#!/usr/bin/env node
/**
 * Comprehensive System Testing Script
 * Tests both HTTP and HTTPS configurations with full connectivity verification
 */

const { spawn } = require('child_process');
const { loadConfig, writeClientEnv, displayConfig } = require('../config-loader');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

class SystemTester {
  constructor() {
    this.testResults = {
      http: { backend: false, frontend: false, websocket: false },
      https: { backend: false, frontend: false, websocket: false }
    };
  }

  async testHTTPMode() {
    console.log('\n🔵 TESTING HTTP MODE');
    console.log('='.repeat(50));
    
    // Configure for HTTP
    await this.setMode('development', false);
    const config = loadConfig();
    
    console.log('📋 HTTP Configuration:');
    console.log(`- Backend Port: ${config.BACKEND_HTTP_PORT}`);
    console.log(`- Frontend Port: ${config.CLIENT_PORT}`);
    console.log(`- API URL: ${config.API_BASE_URL}`);
    console.log(`- WebSocket URL: ${config.WS_URL}`);
    
    // Test backend connectivity
    console.log('\n🧪 Testing Backend (HTTP)...');
    try {
      const response = await axios.get(`http://localhost:${config.BACKEND_HTTP_PORT}/health`, { timeout: 5000 });
      console.log('✅ Backend HTTP: WORKING');
      console.log(`   Status: ${response.status}`);
      console.log(`   Message: ${response.data.message}`);
      this.testResults.http.backend = true;
    } catch (error) {
      console.log('❌ Backend HTTP: FAILED');
      console.log(`   Error: ${error.message}`);
      this.testResults.http.backend = false;
    }
    
    // Test frontend accessibility
    console.log('\n🧪 Testing Frontend (HTTP)...');
    try {
      const response = await axios.get(`http://localhost:${config.CLIENT_PORT}`, { 
        timeout: 5000,
        validateStatus: () => true // Accept any status
      });
      console.log('✅ Frontend HTTP: ACCESSIBLE');
      console.log(`   Status: ${response.status}`);
      this.testResults.http.frontend = true;
    } catch (error) {
      console.log('❌ Frontend HTTP: NOT ACCESSIBLE');
      console.log(`   Error: ${error.message}`);
      console.log('   Note: Frontend might not be running yet');
      this.testResults.http.frontend = false;
    }
    
    return this.testResults.http;
  }

  async testHTTPSMode() {
    console.log('\n🔴 TESTING HTTPS MODE');
    console.log('='.repeat(50));
    
    // Configure for HTTPS
    await this.setMode('development', true);
    const config = loadConfig();
    
    console.log('📋 HTTPS Configuration:');
    console.log(`- Backend Port: ${config.HTTPS_PORT}`);
    console.log(`- Frontend Port: ${config.CLIENT_PORT}`);
    console.log(`- API URL: ${config.API_BASE_URL}`);
    console.log(`- WebSocket URL: ${config.WS_URL}`);
    console.log(`- SSL Available: ${config.SSL_AVAILABLE}`);
    
    // Check SSL certificates
    const sslDevPath = path.join(__dirname, '..', 'server', 'ssl', 'dev');
    const certExists = fs.existsSync(path.join(sslDevPath, 'server.crt'));
    const keyExists = fs.existsSync(path.join(sslDevPath, 'server.key'));
    
    console.log(`- SSL Cert Exists: ${certExists}`);
    console.log(`- SSL Key Exists: ${keyExists}`);
    
    if (!certExists || !keyExists) {
      console.log('⚠️  SSL certificates missing. Generating...');
      await this.generateSSLCerts();
    }
    
    // Test backend connectivity (with SSL ignore for self-signed)
    console.log('\n🧪 Testing Backend (HTTPS)...');
    try {
      const https = require('https');
      const agent = new https.Agent({ rejectUnauthorized: false });
      const response = await axios.get(`https://localhost:${config.HTTPS_PORT}/health`, { 
        timeout: 5000,
        httpsAgent: agent
      });
      console.log('✅ Backend HTTPS: WORKING');
      console.log(`   Status: ${response.status}`);
      console.log(`   Message: ${response.data.message}`);
      this.testResults.https.backend = true;
    } catch (error) {
      console.log('❌ Backend HTTPS: FAILED');
      console.log(`   Error: ${error.message}`);
      this.testResults.https.backend = false;
    }
    
    // Test frontend accessibility
    console.log('\n🧪 Testing Frontend (HTTPS)...');
    try {
      const https = require('https');
      const agent = new https.Agent({ rejectUnauthorized: false });
      const response = await axios.get(`https://localhost:${config.CLIENT_PORT}`, { 
        timeout: 5000,
        httpsAgent: agent,
        validateStatus: () => true
      });
      console.log('✅ Frontend HTTPS: ACCESSIBLE');
      console.log(`   Status: ${response.status}`);
      this.testResults.https.frontend = true;
    } catch (error) {
      console.log('❌ Frontend HTTPS: NOT ACCESSIBLE');
      console.log(`   Error: ${error.message}`);
      console.log('   Note: Frontend might not be running yet');
      this.testResults.https.frontend = false;
    }
    
    return this.testResults.https;
  }

  async setMode(env, enableHttps) {
    const envPath = path.join(__dirname, '..', '.env');
    let envContent = fs.readFileSync(envPath, 'utf8');
    
    envContent = envContent.replace(/^NODE_ENV=.*/m, `NODE_ENV=${env}`);
    envContent = envContent.replace(/^ENABLE_HTTPS=.*/m, `ENABLE_HTTPS=${enableHttps}`);
    
    fs.writeFileSync(envPath, envContent);
    
    // Regenerate client env
    const config = loadConfig();
    writeClientEnv(config);
    
    console.log(`🔧 Configured for ${env} mode with HTTPS ${enableHttps ? 'enabled' : 'disabled'}`);
  }

  async generateSSLCerts() {
    return new Promise((resolve, reject) => {
      const generateCerts = spawn('node', ['ssl/generate-dev-certs.js'], {
        cwd: path.join(__dirname, '..', 'server'),
        stdio: 'inherit'
      });
      
      generateCerts.on('exit', (code) => {
        if (code === 0) {
          console.log('✅ SSL certificates generated');
          resolve();
        } else {
          console.log('❌ Failed to generate SSL certificates');
          reject(new Error('SSL generation failed'));
        }
      });
    });
  }

  async startServers(mode = 'http') {
    console.log(`\n🚀 Starting servers in ${mode.toUpperCase()} mode...`);
    
    const script = mode === 'https' ? 'start-dev-https.js' : 'start-dev.js';
    
    return new Promise((resolve) => {
      const servers = spawn('node', [`scripts/${script}`], {
        cwd: path.join(__dirname, '..'),
        stdio: 'inherit',
        detached: true
      });
      
      // Give servers time to start
      setTimeout(() => {
        console.log('⏱️  Servers should be starting...');
        resolve(servers);
      }, 5000);
    });
  }

  displayResults() {
    console.log('\n📊 TEST RESULTS SUMMARY');
    console.log('='.repeat(50));
    
    console.log('🔵 HTTP Mode:');
    console.log(`   Backend:  ${this.testResults.http.backend ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   Frontend: ${this.testResults.http.frontend ? '✅ PASS' : '❌ FAIL'}`);
    
    console.log('\n🔴 HTTPS Mode:');
    console.log(`   Backend:  ${this.testResults.https.backend ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   Frontend: ${this.testResults.https.frontend ? '✅ PASS' : '❌ FAIL'}`);
    
    const httpScore = Object.values(this.testResults.http).filter(Boolean).length;
    const httpsScore = Object.values(this.testResults.https).filter(Boolean).length;
    
    console.log('\n🎯 Overall Score:');
    console.log(`   HTTP:  ${httpScore}/2 tests passed`);
    console.log(`   HTTPS: ${httpsScore}/2 tests passed`);
    
    if (httpScore === 2 && httpsScore === 2) {
      console.log('\n🎉 ALL TESTS PASSED! Both HTTP and HTTPS modes are working correctly.');
    } else {
      console.log('\n⚠️  Some tests failed. Check the logs above for details.');
    }
  }
}

// Main execution
async function main() {
  console.log('🧪 COMPREHENSIVE SYSTEM TESTING');
  console.log('='.repeat(50));
  console.log('This script will test both HTTP and HTTPS configurations');
  console.log('Testing backend connectivity, frontend accessibility, and SSL setup\n');
  
  const tester = new SystemTester();
  
  try {
    // Test HTTP mode (backend only for now)
    await tester.testHTTPMode();
    
    // Test HTTPS mode (backend only for now)
    await tester.testHTTPSMode();
    
    // Display results
    tester.displayResults();
    
    console.log('\n📝 Next Steps:');
    console.log('1. Choose your preferred mode (HTTP for development, HTTPS for production)');
    console.log('2. Run: npm run dev (for HTTP) or npm run dev:https (for HTTPS)');
    console.log('3. Access frontend at the URLs shown above');
    console.log('4. Test login and QR scanning functionality');
    
  } catch (error) {
    console.error('❌ Testing failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { SystemTester };
