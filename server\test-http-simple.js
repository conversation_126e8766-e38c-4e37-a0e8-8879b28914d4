const express = require('express');
const http = require('http');
const { getServerConfig } = require('./config/unified-config');

console.log('🧪 Testing HTTP Server Configuration...');

// Load configuration
const config = getServerConfig();

console.log('Configuration loaded:');
console.log('- ENABLE_HTTPS:', config.ENABLE_HTTPS);
console.log('- BACKEND_HTTP_PORT:', config.BACKEND_HTTP_PORT);
console.log('- IP_ADDRESS:', config.IP_ADDRESS);

const app = express();

app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'HTTP Test Server is running',
    timestamp: new Date().toISOString(),
    config: {
      https_enabled: config.ENABLE_HTTPS,
      port: config.BACKEND_HTTP_PORT,
      ip: config.IP_ADDRESS
    }
  });
});

const server = http.createServer(app);

server.listen(config.BACKEND_HTTP_PORT, '0.0.0.0', () => {
  console.log(`✅ HTTP Test Server running on port ${config.BACKEND_HTTP_PORT}`);
  console.log(`🌐 Test URL: http://localhost:${config.BACKEND_HTTP_PORT}/health`);
  console.log(`🌐 Network URL: http://${config.IP_ADDRESS}:${config.BACKEND_HTTP_PORT}/health`);
});

server.on('error', (error) => {
  console.error('❌ Server error:', error);
  process.exit(1);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down test server...');
  server.close(() => {
    console.log('✅ Test server stopped');
    process.exit(0);
  });
});
