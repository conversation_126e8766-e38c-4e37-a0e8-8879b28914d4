const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const auth = require('../middleware/auth');
const Joi = require('joi');

// Validation schemas - Updated for flexible assignments
const assignmentSchema = Joi.object({
  assignment_code: Joi.string().max(50).optional(),
  truck_id: Joi.number().integer().positive().required(),
  driver_id: Joi.number().integer().positive().optional().allow(null), // Now optional - managed by shifts
  loading_location_id: Joi.number().integer().positive().required(),
  unloading_location_id: Joi.number().integer().positive().required(),
  assigned_date: Joi.date().optional(),
  start_time: Joi.date().optional().allow(null),
  end_time: Joi.date().optional().allow(null),
  status: Joi.string().valid('assigned', 'in_progress', 'completed', 'cancelled').optional(),
  priority: Joi.string().valid('low', 'normal', 'high', 'urgent').optional(),
  expected_loads_per_day: Joi.number().integer().min(1).optional(),
  notes: Joi.string().max(1000).optional().allow('')
});

const updateAssignmentSchema = assignmentSchema.fork(['truck_id', 'driver_id', 'loading_location_id', 'unloading_location_id'], (schema) => schema.optional());

// @route   GET /api/assignments
// @desc    Get all assignments with filtering and search
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      status = '',
      priority = '',
      truck_id = '',
      driver_id = '',
      date_from = '',
      date_to = '',
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    const validSortColumns = ['assignment_code', 'created_at', 'assigned_date', 'status', 'priority'];
    const validSortOrders = ['asc', 'desc'];
    
    const sortColumn = validSortColumns.includes(sortBy) ? sortBy : 'created_at';
    const sortDirection = validSortOrders.includes(sortOrder.toLowerCase()) ? sortOrder.toUpperCase() : 'DESC';

    let whereConditions = [];
    let queryParams = [];
    let paramCount = 0;

    // Search functionality
    if (search) {
      paramCount++;
      whereConditions.push(`(
        t.truck_number ILIKE $${paramCount} OR
        t.license_plate ILIKE $${paramCount} OR
        d.full_name ILIKE $${paramCount} OR
        d.employee_id ILIKE $${paramCount} OR
        ll.name ILIKE $${paramCount} OR
        ul.name ILIKE $${paramCount} OR
        a.notes ILIKE $${paramCount}
      )`);
      queryParams.push(`%${search}%`);
    }

    // Status filter
    if (status) {
      paramCount++;
      whereConditions.push(`a.status = $${paramCount}`);
      queryParams.push(status);
    }

    // Priority filter
    if (priority) {
      paramCount++;
      whereConditions.push(`a.priority = $${paramCount}`);
      queryParams.push(priority);
    }

    // Truck filter
    if (truck_id) {
      paramCount++;
      whereConditions.push(`a.truck_id = $${paramCount}`);
      queryParams.push(parseInt(truck_id));
    }

    // Driver filter
    if (driver_id) {
      paramCount++;
      whereConditions.push(`a.driver_id = $${paramCount}`);
      queryParams.push(parseInt(driver_id));
    }

    // Date range filter
    if (date_from) {
      paramCount++;
      whereConditions.push(`a.assigned_date >= $${paramCount}`);
      queryParams.push(date_from);
    }

    if (date_to) {
      paramCount++;
      whereConditions.push(`a.assigned_date <= $${paramCount}`);
      queryParams.push(date_to);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Get total count
    const countQuery = `
      SELECT COUNT(*)
      FROM assignments a
      JOIN dump_trucks t ON a.truck_id = t.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      JOIN locations ll ON a.loading_location_id = ll.id
      JOIN locations ul ON a.unloading_location_id = ul.id
      ${whereClause}
    `;
    const countResult = await query(countQuery, queryParams);
    const totalItems = parseInt(countResult.rows[0].count);

    // Get paginated data with joins - Enhanced with shift-based driver information
    const dataQuery = `
      SELECT
        a.id, a.assignment_code, a.assigned_date, a.start_time, a.end_time,
        a.status, a.priority, a.expected_loads_per_day, a.notes,
        a.created_at, a.updated_at,
        t.id as truck_id, t.truck_number, t.license_plate, t.make, t.model,

        -- Assignment driver (may be NULL)
        d.id as driver_id, d.employee_id, d.full_name as driver_name,

        -- Current shift driver information
        ds.driver_id as current_shift_driver_id,
        sd.full_name as current_shift_driver_name,
        sd.employee_id as current_shift_employee_id,
        ds.shift_type as current_shift_type,

        -- Driver status for display
        CASE
          WHEN ds.driver_id IS NOT NULL THEN 'active_shift'
          WHEN d.id IS NOT NULL THEN 'assigned_only'
          ELSE 'no_driver'
        END as driver_status,

        ll.id as loading_location_id, ll.location_code as loading_code, ll.name as loading_location_name,
        ul.id as unloading_location_id, ul.location_code as unloading_code, ul.name as unloading_location_name
      FROM assignments a
      JOIN dump_trucks t ON a.truck_id = t.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      JOIN locations ll ON a.loading_location_id = ll.id
      JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN driver_shifts ds ON (
        ds.truck_id = a.truck_id
        AND ds.status = 'active'
        AND (
          -- Day shift or night shift that doesn't cross midnight
          (ds.shift_date = CURRENT_DATE AND CURRENT_TIME BETWEEN ds.start_time AND ds.end_time)
          OR
          -- Night shift that crosses midnight - started yesterday, still active today
          (ds.shift_type = 'night' AND ds.end_time < ds.start_time AND
           ds.shift_date = CURRENT_DATE - 1 AND CURRENT_TIME <= ds.end_time)
          OR
          -- Night shift that crosses midnight - started today
          (ds.shift_type = 'night' AND ds.end_time < ds.start_time AND
           ds.shift_date = CURRENT_DATE AND CURRENT_TIME >= ds.start_time)
        )
      )
      LEFT JOIN drivers sd ON ds.driver_id = sd.id
      ${whereClause}
      ORDER BY a.${sortColumn} ${sortDirection}
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `;
    
    queryParams.push(parseInt(limit), offset);
    const assignmentsResult = await query(dataQuery, queryParams);

    const totalPages = Math.ceil(totalItems / parseInt(limit));

    res.json({
      success: true,
      data: assignmentsResult.rows,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems,
        itemsPerPage: parseInt(limit),
        hasNextPage: parseInt(page) < totalPages,
        hasPrevPage: parseInt(page) > 1
      }
    });

  } catch (error) {
    console.error('Get assignments error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve assignments'
    });
  }
});

// @route   GET /api/assignments/active
// @desc    Get active assignments (assigned or in_progress)
// @access  Private
router.get('/active', auth, async (req, res) => {
  try {
    const activeQuery = `
      SELECT
        a.id, a.assignment_code, a.assigned_date, a.start_time, a.end_time,
        a.status, a.priority, a.expected_loads_per_day,
        t.truck_number, t.license_plate,

        -- Assignment driver (may be NULL)
        d.employee_id, d.full_name as driver_name,

        -- Current shift driver information
        sd.employee_id as current_shift_employee_id,
        sd.full_name as current_shift_driver_name,
        ds.shift_type as current_shift_type,

        ll.name as loading_location_name, ul.name as unloading_location_name
      FROM assignments a
      JOIN dump_trucks t ON a.truck_id = t.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      JOIN locations ll ON a.loading_location_id = ll.id
      JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN driver_shifts ds ON (
        ds.truck_id = a.truck_id
        AND ds.status = 'active'
        AND (
          -- Day shift or night shift that doesn't cross midnight
          (ds.shift_date = CURRENT_DATE AND CURRENT_TIME BETWEEN ds.start_time AND ds.end_time)
          OR
          -- Night shift that crosses midnight - started yesterday, still active today
          (ds.shift_type = 'night' AND ds.end_time < ds.start_time AND
           ds.shift_date = CURRENT_DATE - 1 AND CURRENT_TIME <= ds.end_time)
          OR
          -- Night shift that crosses midnight - started today
          (ds.shift_type = 'night' AND ds.end_time < ds.start_time AND
           ds.shift_date = CURRENT_DATE AND CURRENT_TIME >= ds.start_time)
        )
      )
      LEFT JOIN drivers sd ON ds.driver_id = sd.id
      WHERE a.status IN ('assigned', 'in_progress')
      ORDER BY a.assigned_date ASC
    `;

    const result = await query(activeQuery);

    res.json({
      success: true,
      data: result.rows
    });

  } catch (error) {
    console.error('Get active assignments error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve active assignments'
    });
  }
});

// @route   GET /api/assignments/:id
// @desc    Get single assignment by ID
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await query(
      `SELECT 
        a.*,
        t.truck_number, t.license_plate, t.make, t.model,
        d.employee_id, d.full_name as driver_name,
        ll.location_code as loading_code, ll.name as loading_location_name,
        ul.location_code as unloading_code, ul.name as unloading_location_name
       FROM assignments a
       JOIN dump_trucks t ON a.truck_id = t.id
       JOIN drivers d ON a.driver_id = d.id
       JOIN locations ll ON a.loading_location_id = ll.id
       JOIN locations ul ON a.unloading_location_id = ul.id
       WHERE a.id = $1`,
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Assignment not found'
      });
    }

    res.json({
      success: true,
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Get assignment error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve assignment'
    });
  }
});

// @route   POST /api/assignments
// @desc    Create new assignment - FIXED
// @access  Private
router.post('/', auth, async (req, res) => {
  try {
    // Validate input
    const { error } = assignmentSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation Error',
        message: error.details[0].message
      });
    }

    const {
      truck_id,
      driver_id,
      loading_location_id,
      unloading_location_id,
      assigned_date = new Date().toISOString().split('T')[0],
      start_time = null,
      end_time = null,
      status = 'assigned',
      priority = 'normal',
      expected_loads_per_day = 1,
      notes = ''
    } = req.body;    // Generate unique assignment code
    const timestamp = Date.now();
    const randomSuffix = Math.random().toString(36).substr(2, 6).toUpperCase();
    const assignment_code = `ASG-${timestamp}-${randomSuffix}`;

    // Check for duplicate assignment (same truck, loading, and unloading locations with active status)
    // Focus on truck-location combination regardless of date to support exception flows
    // Include 'pending_approval' status to prevent duplicates during exception flows
    const duplicateCheck = await query(`
      SELECT id, assignment_code, status, assigned_date FROM assignments
      WHERE truck_id = $1
        AND loading_location_id = $2
        AND unloading_location_id = $3
        AND status IN ('pending_approval', 'assigned', 'in_progress')
      ORDER BY created_at DESC
      LIMIT 1
    `, [truck_id, loading_location_id, unloading_location_id]);

    if (duplicateCheck.rows.length > 0) {
      const existing = duplicateCheck.rows[0];
      return res.status(400).json({
        error: 'Duplicate Assignment',
        message: `Active assignment already exists for this truck with the same loading and unloading locations. Assignment Code: ${existing.assignment_code} (Status: ${existing.status}, Date: ${existing.assigned_date}). Cannot create duplicate active assignments.`
      });
    }

    // Rate calculation system removed - not needed for assignment creation
    // Validate foreign keys exist
    const validationPromises = [
      query('SELECT id FROM dump_trucks WHERE id = $1 AND status = $2', [truck_id, 'active']),
      query('SELECT id FROM locations WHERE id = $1 AND status = $2', [loading_location_id, 'active']),
      query('SELECT id FROM locations WHERE id = $1 AND status = $2', [unloading_location_id, 'active'])
    ];

    // Only validate driver if driver_id is provided
    if (driver_id) {
      validationPromises.push(query('SELECT id FROM drivers WHERE id = $1 AND status = $2', [driver_id, 'active']));
    }

    const validationResults = await Promise.all(validationPromises);
    const [truckCheck, loadingLocationCheck, unloadingLocationCheck, driverCheck] = validationResults;

    if (truckCheck.rows.length === 0) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'Selected truck is not available or inactive'
      });
    }

    if (driver_id && driverCheck && driverCheck.rows.length === 0) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'Selected driver is not available or inactive'
      });
    }

    if (loadingLocationCheck.rows.length === 0) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'Selected loading location is not available or inactive'
      });
    }

    if (unloadingLocationCheck.rows.length === 0) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'Selected unloading location is not available or inactive'
      });
    }

    // Auto-assign driver from active shift if not provided
    let finalDriverId = driver_id;
    let finalNotes = notes || '';
    let autoCreated = false;

    if (!finalDriverId) {
      try {
        const currentDriverResult = await query(`
          SELECT ds.driver_id, d.full_name
          FROM driver_shifts ds
          JOIN drivers d ON ds.driver_id = d.id
          WHERE ds.truck_id = $1
            AND ds.status = 'active'
            AND ds.shift_date = CURRENT_DATE
            AND CURRENT_TIME BETWEEN ds.start_time AND
                CASE
                  WHEN ds.end_time < ds.start_time
                  THEN ds.end_time + interval '24 hours'
                  ELSE ds.end_time
                END
          ORDER BY ds.created_at DESC
          LIMIT 1
        `, [truck_id]);

        if (currentDriverResult.rows.length > 0) {
          finalDriverId = currentDriverResult.rows[0].driver_id;
          finalNotes += (finalNotes ? ' ' : '') + `[Auto-assigned driver: ${currentDriverResult.rows[0].full_name} from active shift]`;
          autoCreated = true; // Mark as auto-created when driver is auto-assigned
        } else {
          // No active driver found - mark as auto-created to satisfy constraint
          autoCreated = true;
          finalNotes += (finalNotes ? ' ' : '') + '[No active driver found - manual assignment required]';
        }
      } catch (driverError) {
        console.error('Failed to get current driver from shift:', driverError.message);
        // Mark as auto-created to satisfy constraint when auto-assignment fails
        autoCreated = true;
        finalNotes += (finalNotes ? ' ' : '') + '[Auto-assignment failed - manual driver assignment required]';
      }
    }

    // Debug logging for constraint troubleshooting
    console.log('ASSIGNMENT_CREATE_DEBUG', 'Assignment creation parameters:', {
      assignment_code,
      truck_id,
      finalDriverId,
      autoCreated,
      finalNotes: finalNotes.substring(0, 100) + (finalNotes.length > 100 ? '...' : '')
    });

    // Insert new assignment with auto_created flag to satisfy constraint
    const result = await query(
      `INSERT INTO assignments
       (assignment_code, truck_id, driver_id, loading_location_id, unloading_location_id,
        assigned_date, start_time, end_time, status, priority, expected_loads_per_day, notes, auto_created)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
       RETURNING *`,
      [assignment_code, truck_id, finalDriverId, loading_location_id, unloading_location_id,
       assigned_date, start_time, end_time, status, priority, expected_loads_per_day, finalNotes, autoCreated]
    );    res.status(201).json({
      success: true,
      message: 'Assignment created successfully',
      data: result.rows[0]
    });
  } catch (error) {
    console.error('Create assignment error:', error);
    
    // Handle specific database errors
    if (error.code === '23505') { // Unique constraint violation
      if (error.constraint === 'assignments_assignment_code_key') {
        return res.status(400).json({
          error: 'Duplicate Error',
          message: 'Assignment code already exists. Please try again.'
        });
      } else if (error.constraint === 'idx_assignments_exact_duplicate') {
        return res.status(400).json({
          error: 'Duplicate Assignment',
          message: 'An identical assignment already exists for this truck with the same loading and unloading locations on this date.'
        });
      } else {
        return res.status(400).json({
          error: 'Duplicate Error', 
          message: 'A similar assignment already exists'
        });
      }
    }

    if (error.code === '42703') { // Column does not exist
      return res.status(500).json({
        error: 'Database Error',
        message: 'Database schema mismatch - please run migrations'
      });
    }

    if (error.code === '23503') { // Foreign key constraint violation
      return res.status(400).json({
        error: 'Reference Error',
        message: 'Selected truck, driver, or location is invalid or inactive'
      });
    }

    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to create assignment',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   PUT /api/assignments/:id
// @desc    Update assignment
// @access  Private
router.put('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    // Validate input
    const { error } = updateAssignmentSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation Error',
        message: error.details[0].message
      });
    }

    // Check if assignment exists
    const existingAssignment = await query(
      'SELECT * FROM assignments WHERE id = $1',
      [id]
    );

    if (existingAssignment.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Assignment not found'
      });
    }

    const {
      assignment_code,
      truck_id,
      driver_id,
      loading_location_id,
      unloading_location_id,
      assigned_date,
      start_time,
      end_time,
      status,
      priority,
      expected_loads_per_day,
      notes
    } = req.body;

    // Check for duplicates (excluding current assignment)
    if (assignment_code) {
      const duplicateCheck = await query(
        'SELECT id FROM assignments WHERE assignment_code = $1 AND id != $2',
        [assignment_code, id]
      );

      if (duplicateCheck.rows.length > 0) {
        return res.status(400).json({
          error: 'Duplicate Error',
          message: 'Assignment code already exists'
        });
      }
    }

    // Build update query dynamically
    const updates = [];
    const values = [];
    let paramCount = 0;

    const fields = {
      assignment_code,
      truck_id,
      driver_id,
      loading_location_id,
      unloading_location_id,
      assigned_date,
      start_time,
      end_time,
      status,
      priority,
      expected_loads_per_day,
      notes
    };

    Object.entries(fields).forEach(([key, value]) => {
      if (value !== undefined) {
        paramCount++;
        updates.push(`${key} = $${paramCount}`);
        values.push(value);
      }
    });

    if (updates.length === 0) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'No fields to update'
      });
    }

    // Add updated_at
    paramCount++;
    updates.push(`updated_at = $${paramCount}`);
    values.push(new Date());

    // Add id for WHERE clause
    paramCount++;
    values.push(id);

    const updateQuery = `
      UPDATE assignments 
      SET ${updates.join(', ')}
      WHERE id = $${paramCount}
      RETURNING *
    `;

    const result = await query(updateQuery, values);

    res.json({
      success: true,
      message: 'Assignment updated successfully',
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Update assignment error:', error);
    
    if (error.code === '23505') {
      return res.status(400).json({
        error: 'Duplicate Error',
        message: 'Assignment code already exists'
      });
    }

    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to update assignment'
    });
  }
});

// @route   DELETE /api/assignments/:id
// @desc    Delete assignment
// @access  Private
router.delete('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if assignment exists
    const existingAssignment = await query(
      'SELECT * FROM assignments WHERE id = $1',
      [id]
    );

    if (existingAssignment.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Assignment not found'
      });
    }

    // Check if assignment has active trips
    const activeTrips = await query(
      'SELECT id FROM trip_logs WHERE assignment_id = $1 AND status IN ($2, $3)',
      [id, 'assigned', 'loading_start']
    );

    if (activeTrips.rows.length > 0) {
      return res.status(400).json({
        error: 'Conflict',
        message: 'Cannot delete assignment with active trips'
      });
    }

    // Delete assignment
    await query('DELETE FROM assignments WHERE id = $1', [id]);

    res.json({
      success: true,
      message: 'Assignment deleted successfully'
    });

  } catch (error) {
    console.error('Delete assignment error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to delete assignment'
    });
  }
});

// Rate calculation system completely removed - not needed for assignment creation

module.exports = router;