import React from 'react';

const PhaseCard = ({ title, icon, data, color = 'blue' }) => {
  const colorClasses = {
    blue: 'bg-blue-50 border-blue-200 text-blue-600',
    green: 'bg-green-50 border-green-200 text-green-600',
    yellow: 'bg-yellow-50 border-yellow-200 text-yellow-600',
    purple: 'bg-purple-50 border-purple-200 text-purple-600'
  };

  if (!data) {
    return (
      <div className="bg-white rounded-lg shadow border border-secondary-200 p-6">
        <div className="flex items-center mb-4">
          <div className={`p-2 rounded-lg ${colorClasses[color]}`}>
            <span className="text-xl">{icon}</span>
          </div>
          <h3 className="ml-3 text-lg font-medium text-secondary-900">{title}</h3>
        </div>
        <div className="text-center py-4 text-secondary-500">
          No data available
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow border border-secondary-200 p-6">
      <div className="flex items-center mb-4">
        <div className={`p-2 rounded-lg ${colorClasses[color]}`}>
          <span className="text-xl">{icon}</span>
        </div>
        <h3 className="ml-3 text-lg font-medium text-secondary-900">{title}</h3>
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-secondary-900">
            {data.average}m
          </div>
          <div className="text-sm text-secondary-500">Average</div>
        </div>
        
        <div className="text-center">
          <div className="text-2xl font-bold text-secondary-900">
            {data.median}m
          </div>
          <div className="text-sm text-secondary-500">Median</div>
        </div>
        
        <div className="text-center">
          <div className="text-lg font-medium text-green-600">
            {data.min}m
          </div>
          <div className="text-sm text-secondary-500">Best Time</div>
        </div>
        
        <div className="text-center">
          <div className="text-lg font-medium text-red-600">
            {data.max}m
          </div>
          <div className="text-sm text-secondary-500">Worst Time</div>
        </div>
      </div>
      
      {/* Visual bar representation */}
      <div className="mt-4">
        <div className="flex items-center justify-between text-xs text-secondary-500 mb-1">
          <span>Min: {data.min}m</span>
          <span>Avg: {data.average}m</span>
          <span>Max: {data.max}m</span>
        </div>
        <div className="w-full bg-secondary-200 rounded-full h-2">
          <div 
            className={`h-2 rounded-full ${color === 'blue' ? 'bg-blue-500' : color === 'green' ? 'bg-green-500' : color === 'yellow' ? 'bg-yellow-500' : 'bg-purple-500'}`}
            style={{ 
              width: data.max > 0 ? `${(data.average / data.max) * 100}%` : '0%' 
            }}
          ></div>
        </div>
      </div>
    </div>
  );
};

const OverallSummary = ({ data }) => {
  if (!data) {
    return (
      <div className="bg-white rounded-lg shadow border border-secondary-200 p-6">
        <h3 className="text-lg font-medium text-secondary-900 mb-4">
          Overall Performance Summary
        </h3>
        <div className="text-center py-4 text-secondary-500">
          No summary data available
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow border border-secondary-200 p-6">
      <h3 className="text-lg font-medium text-secondary-900 mb-4">
        Overall Performance Summary
      </h3>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">
            {data.totalTrips}
          </div>
          <div className="text-sm text-secondary-500">Total Trips</div>
        </div>
        
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">
            {data.completedTrips}
          </div>
          <div className="text-sm text-secondary-500">Completed</div>
        </div>
        
        <div className="text-center">
          <div className="text-2xl font-bold text-red-600">
            {data.breakdownTrips}
          </div>
          <div className="text-sm text-secondary-500">Breakdowns</div>
        </div>
        
        <div className="text-center">
          <div className="text-2xl font-bold text-orange-600">
            {data.exceptionTrips}
          </div>
          <div className="text-sm text-secondary-500">Exceptions</div>
        </div>
      </div>
      
      <div className="mt-6 pt-6 border-t border-secondary-200">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-primary-600">
              {data.completionRate}%
            </div>
            <div className="text-sm text-secondary-500">Completion Rate</div>
            <div className="mt-2">
              <div className="w-full bg-secondary-200 rounded-full h-2">
                <div 
                  className="bg-primary-500 h-2 rounded-full"
                  style={{ width: `${data.completionRate}%` }}
                ></div>
              </div>
            </div>
          </div>
          
          <div className="text-center">
            <div className="text-3xl font-bold text-secondary-900">
              {data.avgTotalDuration}m
            </div>
            <div className="text-sm text-secondary-500">Average Total Duration</div>
            <div className="text-xs text-secondary-400 mt-1">
              End-to-end trip time
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const PhaseAnalysis = ({ data, loading }) => {
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse bg-secondary-200 h-32 rounded-lg"></div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="animate-pulse bg-secondary-200 h-48 rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-8 text-secondary-500">
        <span className="text-4xl block mb-2">📊</span>
        No phase analysis data available
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Overall Summary */}
      <OverallSummary data={data.overall} />
      
      {/* Phase Analysis Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <PhaseCard
          title="Loading Phase"
          icon="⬆️"
          data={data.loading}
          color="blue"
        />
        
        <PhaseCard
          title="Travel Phase"
          icon="🚛"
          data={data.travel}
          color="yellow"
        />
        
        <PhaseCard
          title="Unloading Phase"
          icon="⬇️"
          data={data.unloading}
          color="green"
        />
        
        <PhaseCard
          title="Return Travel"
          icon="🔄"
          data={data.travel} // Using travel data for return as well
          color="purple"
        />
      </div>
      
      {/* Phase Comparison Chart */}
      <div className="bg-white rounded-lg shadow border border-secondary-200 p-6">
        <h3 className="text-lg font-medium text-secondary-900 mb-4">
          Phase Duration Comparison
        </h3>
        
        <div className="space-y-4">
          {/* Legend */}
          <div className="flex items-center space-x-4 text-sm">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-blue-500 rounded mr-2"></div>
              <span>Loading</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-yellow-500 rounded mr-2"></div>
              <span>Travel</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-500 rounded mr-2"></div>
              <span>Unloading</span>
            </div>
          </div>
          
          {/* Horizontal bar chart */}
          <div className="space-y-3">
            <div className="flex items-center">
              <div className="w-20 text-sm text-secondary-600">Average:</div>
              <div className="flex-1 flex">
                <div 
                  className="bg-blue-500 h-6 flex items-center justify-center text-white text-xs"
                  style={{ width: `${(data.loading?.average || 0) * 2}px` }}
                >
                  {data.loading?.average}m
                </div>
                <div 
                  className="bg-yellow-500 h-6 flex items-center justify-center text-white text-xs"
                  style={{ width: `${(data.travel?.average || 0) * 2}px` }}
                >
                  {data.travel?.average}m
                </div>
                <div 
                  className="bg-green-500 h-6 flex items-center justify-center text-white text-xs"
                  style={{ width: `${(data.unloading?.average || 0) * 2}px` }}
                >
                  {data.unloading?.average}m
                </div>
              </div>
            </div>
            
            <div className="flex items-center">
              <div className="w-20 text-sm text-secondary-600">Best:</div>
              <div className="flex-1 flex">
                <div 
                  className="bg-blue-400 h-4 flex items-center justify-center text-white text-xs"
                  style={{ width: `${(data.loading?.min || 0) * 2}px` }}
                >
                  {data.loading?.min}m
                </div>
                <div 
                  className="bg-yellow-400 h-4 flex items-center justify-center text-white text-xs"
                  style={{ width: `${(data.travel?.min || 0) * 2}px` }}
                >
                  {data.travel?.min}m
                </div>
                <div 
                  className="bg-green-400 h-4 flex items-center justify-center text-white text-xs"
                  style={{ width: `${(data.unloading?.min || 0) * 2}px` }}
                >
                  {data.unloading?.min}m
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PhaseAnalysis;
