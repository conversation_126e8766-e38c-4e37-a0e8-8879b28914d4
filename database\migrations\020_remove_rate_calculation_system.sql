-- Migration 020: Remove Rate Calculation System
-- Purpose: Remove driver_rate column and rate calculation system completely
-- Date: 2025-07-09

-- Check for dependencies first
DO $$
DECLARE
    dep_record RECORD;
BEGIN
    -- Find dependencies on driver_rate column
    FOR dep_record IN
        SELECT DISTINCT
            n.nspname as schema_name,
            c.relname as table_name,
            a.attname as column_name,
            pg_get_constraintdef(con.oid) as constraint_def
        FROM pg_constraint con
        JOIN pg_class c ON c.oid = con.conrelid
        JOIN pg_namespace n ON n.oid = c.relnamespace
        JOIN pg_attribute a ON a.attrelid = c.oid AND a.attnum = ANY(con.conkey)
        WHERE a.attname = 'driver_rate'
        AND c.relname = 'assignments'
    LOOP
        RAISE NOTICE 'Found dependency: % on %.%', dep_record.constraint_def, dep_record.schema_name, dep_record.table_name;
    END LOOP;
END $$;

-- Drop any constraints that depend on driver_rate column
-- (This will be populated based on what we find)

-- Remove driver_rate column from assignments table
ALTER TABLE assignments DROP COLUMN IF EXISTS driver_rate CASCADE;

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'Migration 020 completed successfully: Rate calculation system removed';
    RAISE NOTICE '- Removed driver_rate column from assignments table with CASCADE';
    RAISE NOTICE '- Rate calculation system completely eliminated';
END $$;
