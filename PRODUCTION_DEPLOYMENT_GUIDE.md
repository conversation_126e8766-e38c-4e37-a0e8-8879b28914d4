# Production Deployment Guide - Hauling QR Trip System

## Prerequisites

- Cloud VPS with Ubuntu 20.04+ (2GB RAM minimum, 4GB recommended)
- Domain name (optional but recommended for SSL)
- SSH access to server
- Basic Linux command line knowledge

## Phase 1: Server Setup

### 1. Initial Server Configuration

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install essential packages
sudo apt install -y curl wget git ufw fail2ban

# Configure firewall
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 5000/tcp  # Application port
sudo ufw --force enable

# Configure fail2ban for SSH protection
sudo systemctl enable fail2ban
sudo systemctl start fail2ban
```

### 2. Install Docker and Docker Compose

```bash
# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Verify installation
docker --version
docker-compose --version

# Log out and back in for group changes to take effect
```

### 3. Clone and Prepare Application

```bash
# Clone repository
git clone <your-repository-url> hauling-qr-system
cd hauling-qr-system

# Create production environment file
cp .env.example .env.production
```

## Phase 2: Environment Configuration

### 1. Configure Production Environment

Edit `.env.production`:

```env
# Application
NODE_ENV=production
PORT=5000

# Database Configuration
DB_HOST=postgres
DB_PORT=5432
DB_NAME=hauling_qr_system
DB_USER=postgres
DB_PASSWORD=YOUR_STRONG_DB_PASSWORD_HERE

# Security
JWT_SECRET=YOUR_STRONG_JWT_SECRET_32_CHARS_MINIMUM
JWT_EXPIRY=8h

# Rate Limiting (Production Values)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS_PROD=1000
AUTH_RATE_LIMIT_WINDOW_MS=900000
AUTH_RATE_LIMIT_MAX_REQUESTS=20

# SSL/HTTPS
ENABLE_HTTPS=true
HTTPS_PORT=443

# Optional Services
REDIS_URL=redis://redis:6379

# Logging
LOG_LEVEL=info
```

### 2. Generate Strong Secrets

```bash
# Generate strong JWT secret
echo "JWT_SECRET=$(openssl rand -base64 32)" >> .env.production

# Generate strong database password
echo "DB_PASSWORD=$(openssl rand -base64 24)" >> .env.production
```

## Phase 3: SSL Certificate Setup

### Option A: Let's Encrypt (Recommended for domains)

```bash
# Install Certbot
sudo apt install -y certbot

# Stop any running services on port 80
sudo systemctl stop nginx apache2 2>/dev/null || true

# Generate certificate
sudo certbot certonly --standalone -d yourdomain.com -d www.yourdomain.com

# Copy certificates to application
sudo mkdir -p server/ssl/production
sudo cp /etc/letsencrypt/live/yourdomain.com/privkey.pem server/ssl/production/server.key
sudo cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem server/ssl/production/fullchain.crt
sudo chown -R $USER:$USER server/ssl/production
```

### Option B: Self-Signed (For IP-based access)

```bash
# Generate self-signed certificate
cd server
node ssl/generate-dev-certs.js
mv ssl/development/* ssl/production/
```

## Phase 4: Database Preparation

### 1. Modify Docker Compose for Production

Edit `docker-compose.yml`:

```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: hauling-postgres
    environment:
      POSTGRES_DB: hauling_qr_system
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
      - ./backups:/backups  # Add backup volume
    ports:
      - "127.0.0.1:5432:5432"  # Bind to localhost only
    networks:
      - hauling-network
    restart: unless-stopped

  app:
    build: .
    container_name: hauling-app
    environment:
      NODE_ENV: production
      PORT: 5000
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: hauling_qr_system
      DB_USER: postgres
      DB_PASSWORD: ${DB_PASSWORD}
      JWT_SECRET: ${JWT_SECRET}
    ports:
      - "5000:5000"
      - "443:443"  # HTTPS port
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - hauling-network
    restart: unless-stopped
    volumes:
      - app_logs:/app/logs
      - ./server/ssl/production:/app/ssl/production:ro

volumes:
  postgres_data:
  app_logs:

networks:
  hauling-network:
    driver: bridge
```

## Phase 5: Application Deployment

### 1. Build and Deploy

```bash
# Load environment variables
export $(cat .env.production | xargs)

# Build application
docker-compose build --no-cache

# Start services
docker-compose up -d

# Check status
docker-compose ps
docker-compose logs -f app
```

### 2. Verify Deployment

```bash
# Test health endpoint
curl -k https://localhost:443/api/health

# Test from external IP
curl -k https://YOUR_SERVER_IP:443/api/health

# Check logs
docker-compose logs app
docker-compose logs postgres
```

## Phase 6: Production Monitoring and Maintenance

### 1. Set Up Log Rotation

```bash
# Create logrotate configuration
sudo tee /etc/logrotate.d/hauling-qr-system << EOF
/var/lib/docker/volumes/hauling-qr-system_app_logs/_data/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
}
EOF
```

### 2. Set Up Automated Backups

```bash
# Create backup script
sudo tee /usr/local/bin/backup-hauling-db.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/var/backups/hauling-qr-system"
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR

# Database backup
docker exec hauling-postgres pg_dump -U postgres hauling_qr_system | gzip > $BACKUP_DIR/db_backup_$DATE.sql.gz

# Keep only last 7 days of backups
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +7 -delete

echo "Backup completed: $BACKUP_DIR/db_backup_$DATE.sql.gz"
EOF

sudo chmod +x /usr/local/bin/backup-hauling-db.sh

# Add to crontab (daily backup at 2 AM)
echo "0 2 * * * /usr/local/bin/backup-hauling-db.sh" | sudo crontab -
```

### 3. Set Up System Monitoring

```bash
# Install monitoring tools
sudo apt install -y htop iotop nethogs

# Create monitoring script
tee ~/monitor-system.sh << 'EOF'
#!/bin/bash
echo "=== System Status ==="
date
echo "=== Docker Containers ==="
docker-compose ps
echo "=== System Resources ==="
free -h
df -h
echo "=== Application Logs (Last 10 lines) ==="
docker-compose logs --tail=10 app
EOF

chmod +x ~/monitor-system.sh
```

## Phase 7: SSL Certificate Renewal (Let's Encrypt)

```bash
# Test renewal
sudo certbot renew --dry-run

# Set up automatic renewal
echo "0 12 * * * /usr/bin/certbot renew --quiet && docker-compose restart app" | sudo crontab -
```

## Troubleshooting

### Common Issues

1. **Port 443 already in use**: Check for other web servers
2. **SSL certificate errors**: Verify certificate paths and permissions
3. **Database connection errors**: Check environment variables and container networking
4. **WebSocket connection issues**: Verify firewall settings and SSL configuration

### Useful Commands

```bash
# View application logs
docker-compose logs -f app

# Restart application
docker-compose restart app

# Update application
git pull
docker-compose build app
docker-compose up -d app

# Database shell access
docker exec -it hauling-postgres psql -U postgres -d hauling_qr_system

# Application shell access
docker exec -it hauling-app /bin/bash
```
