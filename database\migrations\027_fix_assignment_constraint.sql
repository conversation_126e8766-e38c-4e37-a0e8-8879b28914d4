-- Migration 019: Fix Assignment Constraint
-- Purpose: Fix overly restrictive check constraint that prevents assignment creation
-- Date: 2025-07-09

-- Drop the overly restrictive constraint
ALTER TABLE assignments DROP CONSTRAINT IF EXISTS chk_driver_id_or_auto;

-- Make driver_id nullable to allow trigger to populate it
-- (This was already done in migration 018, but ensuring it's correct)
ALTER TABLE assignments ALTER COLUMN driver_id DROP NOT NULL;

-- Update the trigger function to be more robust
CREATE OR REPLACE FUNCTION auto_populate_driver_from_shift()
RETURNS TRIGGER AS $$
DECLARE
    v_current_driver_id INTEGER;
BEGIN
    -- Only auto-populate if driver_id is NULL
    IF NEW.driver_id IS NULL THEN
        -- Get current active driver for the truck
        SELECT ds.driver_id INTO v_current_driver_id
        FROM driver_shifts ds
        WHERE ds.truck_id = NEW.truck_id
          AND ds.status = 'active'
          AND ds.shift_date = CURRENT_DATE
          AND CURRENT_TIME BETWEEN ds.start_time AND 
              CASE 
                WHEN ds.end_time < ds.start_time 
                THEN ds.end_time + interval '24 hours'
                ELSE ds.end_time 
              END
        ORDER BY ds.created_at DESC
        LIMIT 1;
        
        -- If we found an active driver, use it
        IF v_current_driver_id IS NOT NULL THEN
            NEW.driver_id := v_current_driver_id;
            
            -- Add note about auto-assignment
            IF NEW.notes IS NULL OR NEW.notes = '' THEN
                NEW.notes := '[Auto-assigned driver from active shift]';
            ELSE
                NEW.notes := NEW.notes || ' [Auto-assigned driver from active shift]';
            END IF;
        ELSE
            -- No active driver found, leave driver_id as NULL
            -- Add note about missing driver
            IF NEW.notes IS NULL OR NEW.notes = '' THEN
                NEW.notes := '[No active driver found - manual assignment required]';
            ELSE
                NEW.notes := NEW.notes || ' [No active driver found - manual assignment required]';
            END IF;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Recreate the trigger
DROP TRIGGER IF EXISTS trg_auto_populate_driver ON assignments;
CREATE TRIGGER trg_auto_populate_driver
    BEFORE INSERT ON assignments
    FOR EACH ROW
    EXECUTE FUNCTION auto_populate_driver_from_shift();

-- Add a more flexible constraint that allows NULL driver_id
-- This ensures data integrity while allowing flexibility for auto-assignment
ALTER TABLE assignments 
ADD CONSTRAINT chk_assignment_integrity 
CHECK (
  -- Either driver_id is provided
  driver_id IS NOT NULL OR 
  -- Or it's a system-generated assignment (indicated by notes or assignment code)
  (notes IS NOT NULL AND (
    notes LIKE '%Auto-assigned%' OR 
    notes LIKE '%No active driver%' OR
    notes LIKE '%manual assignment required%'
  )) OR
  (assignment_code IS NOT NULL AND assignment_code LIKE '%AUTO%')
);

-- Update existing assignments that might violate the new constraint
UPDATE assignments 
SET notes = COALESCE(notes, '') || ' [Legacy assignment - driver assignment required]'
WHERE driver_id IS NULL 
  AND (notes IS NULL OR (
    notes NOT LIKE '%Auto-assigned%' AND 
    notes NOT LIKE '%No active driver%' AND
    notes NOT LIKE '%manual assignment required%'
  ))
  AND (assignment_code IS NULL OR assignment_code NOT LIKE '%AUTO%');

-- Success message
DO $$ 
BEGIN 
    RAISE NOTICE 'Migration 019 completed successfully: Assignment constraint fixed';
    RAISE NOTICE '- Removed overly restrictive constraint';
    RAISE NOTICE '- Enhanced trigger function for better driver auto-assignment';
    RAISE NOTICE '- Added flexible constraint that allows NULL driver_id with proper notes';
END $$;
