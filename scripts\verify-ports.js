#!/usr/bin/env node
/**
 * Port Configuration Verification Script
 * Verifies that the system is using the correct ports and URLs
 */

const { loadConfig } = require('../config-loader');
const http = require('http');
const https = require('https');

console.log('🔍 Verifying Port Configuration...\n');

// Load configuration
const config = loadConfig();

console.log('📋 EXPECTED CONFIGURATION:');
console.log('='.repeat(50));
console.log(`Frontend Port: ${config.CLIENT_PORT} (should be 3000)`);
console.log(`Backend HTTP Port: ${config.PORT} (should be 5000)`);
console.log(`Backend HTTPS Port: ${config.HTTPS_PORT} (should be 5444)`);
console.log(`IP Address: ${config.IP_ADDRESS}`);
console.log('='.repeat(50));

console.log('\n🌐 CORRECT ACCESS URLS:');
console.log('='.repeat(50));
console.log(`✅ Frontend (Login/Dashboard): https://${config.IP_ADDRESS}:${config.CLIENT_PORT}`);
console.log(`✅ Backend API: ${config.API_BASE_URL}`);
console.log(`✅ Health Check: https://${config.IP_ADDRESS}:${config.HTTPS_PORT}/health`);
console.log('='.repeat(50));

console.log('\n❌ WRONG URLS (DO NOT USE):');
console.log('='.repeat(50));
console.log(`❌ https://localhost:5000/dashboard (backend HTTP port)`);
console.log(`❌ https://${config.IP_ADDRESS}:5444/ (backend API port - no frontend)`);
console.log(`❌ http://${config.IP_ADDRESS}:3000 (should use HTTPS)`);
console.log('='.repeat(50));

// Test connectivity
async function testConnectivity() {
  console.log('\n🔍 Testing Connectivity...\n');
  
  // Test backend health endpoint
  try {
    const healthUrl = `https://${config.IP_ADDRESS}:${config.HTTPS_PORT}/health`;
    console.log(`Testing backend health: ${healthUrl}`);
    
    const response = await fetch(healthUrl, { 
      method: 'GET',
      headers: { 'Accept': 'application/json' }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Backend health check: ${data.status}`);
    } else {
      console.log(`❌ Backend health check failed: ${response.status}`);
    }
  } catch (error) {
    console.log(`❌ Backend not accessible: ${error.message}`);
  }
  
  // Test frontend (basic connectivity)
  try {
    const frontendUrl = `https://${config.IP_ADDRESS}:${config.CLIENT_PORT}`;
    console.log(`Testing frontend: ${frontendUrl}`);
    
    const response = await fetch(frontendUrl, { 
      method: 'GET',
      headers: { 'Accept': 'text/html' }
    });
    
    if (response.ok) {
      console.log(`✅ Frontend accessible`);
    } else {
      console.log(`❌ Frontend not accessible: ${response.status}`);
    }
  } catch (error) {
    console.log(`❌ Frontend not accessible: ${error.message}`);
  }
}

// Verify environment file
console.log('\n📁 Environment File Check:');
console.log('='.repeat(30));

const fs = require('fs');
const path = require('path');

try {
  const envPath = path.join(__dirname, '..', '.env');
  const envContent = fs.readFileSync(envPath, 'utf8');
  
  const portMatch = envContent.match(/^PORT=(\d+)/m);
  const httpsPortMatch = envContent.match(/^HTTPS_PORT=(\d+)/m);
  const clientPortMatch = envContent.match(/^CLIENT_PORT=(\d+)/m);
  
  console.log(`PORT=${portMatch ? portMatch[1] : 'NOT SET'} (should be 5000)`);
  console.log(`HTTPS_PORT=${httpsPortMatch ? httpsPortMatch[1] : 'NOT SET'} (should be 5444)`);
  console.log(`CLIENT_PORT=${clientPortMatch ? clientPortMatch[1] : 'NOT SET'} (should be 3000)`);
  
  if (portMatch && portMatch[1] === '5000' && 
      httpsPortMatch && httpsPortMatch[1] === '5444' && 
      clientPortMatch && clientPortMatch[1] === '3000') {
    console.log('✅ Environment file ports are correct');
  } else {
    console.log('❌ Environment file ports need fixing');
  }
} catch (error) {
  console.log(`❌ Could not read .env file: ${error.message}`);
}

console.log('\n🎯 SUMMARY:');
console.log('='.repeat(30));
console.log('1. Frontend should run on port 3000');
console.log('2. Backend should run on port 5444 (HTTPS)');
console.log('3. Use frontend URL for login/dashboard');
console.log('4. Backend URL is for API calls only');

// Run connectivity test if fetch is available
if (typeof fetch !== 'undefined') {
  testConnectivity();
} else {
  console.log('\n⚠️  Fetch not available, skipping connectivity test');
  console.log('Run this script with Node.js 18+ for connectivity testing');
}
